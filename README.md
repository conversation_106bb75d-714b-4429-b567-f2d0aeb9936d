<p align="center">
  <a href="https://github.com/codercup2/unibest">
    <img width="160" src="./src/static/logo.svg">
  </a>
</p>

<h1 align="center">
  Urban HealthCheck UniApp - 城市健康检查小程序
</h1>

<div align="center">

![node version](https://img.shields.io/badge/node-%3E%3D18-green)
![pnpm version](https://img.shields.io/badge/pnpm-%3E%3D7.30-green)
![Vue version](https://img.shields.io/badge/Vue-3.4.21-brightgreen)
![TypeScript](https://img.shields.io/badge/TypeScript-5.5.4-blue)
![UniApp](https://img.shields.io/badge/UniApp-3.0.0--alpha-orange)
![License](https://img.shields.io/badge/license-MIT-green)

</div>

## 📋 项目简介

基于 **UniApp + Vue3 + TypeScript** 构建的城市健康检查微信小程序，使用 **Unibest v2.4.3** 框架作为脚手架，集成了现代化的前端开发工具链和最佳实践。

项目主要面向微信小程序平台，提供城市健康检查相关功能，采用组件化开发模式，具备完善的代码质量保障和工程化配置。

## 🛠️ 技术栈

### 核心框架

- **UniApp** - 跨平台开发框架，主要生成微信小程序
- **Unibest** - 基于 UniApp 的最佳实践脚手架 (v2.4.3)
- **Vue 3** - 渐进式 JavaScript 框架 (v3.4.21)
- **TypeScript** - 类型安全的 JavaScript 超集 (v5.5.4)

### UI & 组件库

- **wot-design-uni** - 基于 UniApp 的 UI 组件库 (v1.9.1)
  - 参考文档：https://wot-design-uni.netlify.app/
- **z-paging** - 高性能分页组件 (v2.7.10)
  - 参考文档：https://z-paging.zxlee.cn/start/intro.html

### 构建工具 & 工程化

- **Vite** - 新一代前端构建工具 (v5.2.8)
- **UnoCSS** - 原子化 CSS 引擎 (v0.58.9)
- **Pinia** - Vue 状态管理库 (v2.0.36)
- **pinia-plugin-persistedstate** - Pinia 持久化插件 (v3.2.1)

### 开发工具链

- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Stylelint** - 样式代码检查
- **Husky** - Git hooks 管理
- **lint-staged** - 暂存文件检查

## ⚙️ 环境要求

- **Node.js** >= 18
- **pnpm** >= 7.30
- **微信开发者工具** (用于小程序调试)

## 🚀 快速开始

### 安装依赖

```bash
pnpm install
```

### 开发环境运行

```bash
# 微信小程序开发 (主要目标平台)
pnpm dev:mp-weixin

# H5 开发调试
pnpm dev:h5

# 通用开发命令
pnpm dev
```

### 微信小程序配置

- **AppID:** `wx4c831e61d8ef8a4b`
- 打开微信开发者工具，导入 `dist/dev/mp-weixin` 目录

## 📦 构建部署

### 生产环境构建

```bash
# 微信小程序构建
pnpm build:mp-weixin

# H5 构建
pnpm build:h5

# App 构建
pnpm build:app
```

### 部署说明

- **微信小程序:** 构建后在 `dist/build/mp-weixin` 目录，通过微信开发者工具上传
- **H5 版本:** 构建后在 `dist/build/h5` 目录，可部署到 Web 服务器
- **App 版本:** 需要通过 HBuilderX 进行云打包

## 📁 项目结构

```
src/
├── components/        # 公共组件
├── pages/            # 页面文件
├── pages-sub/        # 分包页面
├── static/           # 静态资源
├── store/            # Pinia 状态管理
├── utils/            # 工具函数
├── service/          # API 服务
├── types/            # TypeScript 类型定义
├── hooks/            # Vue 组合式函数
├── layouts/          # 布局组件
├── style/            # 全局样式
├── config/           # 配置文件
├── interceptors/     # 拦截器
├── uni_modules/      # uni-app 插件模块
├── App.vue          # 应用根组件
├── main.ts          # 应用入口
├── pages.json       # 页面配置
├── manifest.json    # 应用配置
└── uni.scss         # 全局样式变量
```

## 🔧 开发特性

### 核心功能

- ✅ **约定式路由** - 基于文件系统的路由配置
- ✅ **Layout 布局** - 灵活的页面布局系统
- ✅ **请求封装** - 统一的 HTTP 请求处理
- ✅ **状态管理** - Pinia + 持久化插件
- ✅ **TypeScript 支持** - 完整的类型提示和检查
- ✅ **代码质量保障** - ESLint + Prettier + Stylelint
- ✅ **原子化 CSS** - UnoCSS 支持

### UI 组件特性

- 🎨 **wot-design-uni** - 丰富的 UI 组件库
- 📄 **z-paging** - 高性能虚拟分页
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🎯 **微信小程序优化** - 针对小程序平台优化

## 📚 参考文档

- [UniApp 官方文档](https://uniapp.dcloud.net.cn/)
- [Unibest 框架文档](https://unibest.tech/)
- [wot-design-uni 组件库](https://wot-design-uni.netlify.app/)
- [z-paging 分页组件](https://z-paging.zxlee.cn/start/intro.html)
- [Vue 3 官方文档](https://cn.vuejs.org/)
- [Pinia 状态管理](https://pinia.vuejs.org/zh/)

## 📋 当前开发任务

项目目前正在进行以下功能开发和优化：

- 🔄 z-paging 虚拟分页和搜索功能优化
- 🎨 DrawView 按钮优化

详细任务进度请查看 `project_document/` 目录下的相关文档。

## 🔒 安全与质量

- **代码安全:** TypeScript 类型检查 + ESLint 规则
- **小程序安全:** 微信平台权限管理和数据安全
- **代码质量:** 自动化代码检查和格式化
- **版本控制:** Git hooks + Husky 质量门禁

## 📄 License

[MIT](https://opensource.org/license/mit/)

---

**最后更新:** 2025-06-18 09:47:24 +08:00  
**技术栈分析:** 基于 RIPER-5 v3.9 协议完成项目评估
