# 坐标转换快速参考

## 🔧 安装

```bash
pnpm add gcoord
```

## 📍 基本转换方法

### 单点转换

```typescript
import { wgs84ToGcj02, gcj02ToWgs84 } from '@/utils/coordTransform'

// GPS坐标 → 中国地图坐标
const gpsPoint = { latitude: 39.9042, longitude: 116.4074 }
const mapPoint = wgs84ToGcj02(gpsPoint)

// 中国地图坐标 → GPS坐标
const backToGps = gcj02ToWgs84(mapPoint)
```

### 批量转换

```typescript
import { batchWgs84ToGcj02, batchGcj02ToWgs84 } from '@/utils/coordTransform'

const gpsPoints = [
  { latitude: 39.9042, longitude: 116.4074 }, // 北京
  { latitude: 31.2304, longitude: 121.4737 }, // 上海
]

// 批量转换
const mapPoints = batchWgs84ToGcj02(gpsPoints)
const backToGps = batchGcj02ToWgs84(mapPoints)
```

## 🛠️ 工具函数

```typescript
import { calculateDistance, isInChina, testCoordTransform } from '@/utils/coordTransform'

// 计算距离（米）
const distance = calculateDistance(point1, point2)

// 检查是否在中国
const inChina = isInChina(point)

// 测试转换精度
const testResult = testCoordTransform()
console.log(testResult.precisionLoss) // 精度损失（米）
```

## 🗺️ 在WKT中使用

```typescript
import { wktToMapComponents } from '@/utils/wktConverter'

// WKT数据会自动进行坐标转换
const wktData = 'POINT (129.504 42.916)'
const mapComponents = wktToMapComponents(wktData) // 自动转换为GCJ02
```

## 📊 转换精度

- **往返精度损失**: < 1米
- **典型偏移量**: 300-800米（根据位置不同）
- **使用库**: gcoord v1.0.7

## 🎯 使用场景

- **WGS84 → GCJ02**: GPS数据显示在中国地图上
- **GCJ02 → WGS84**: 地图坐标转换为GPS标准坐标
- **WKT转换**: 自动集成在WKT解析器中

## ⚠️ 注意事项

1. 只有中国境内坐标需要转换
2. 转换失败时返回原坐标（不会崩溃）
3. 所有函数都有完整的错误处理和参数验证
