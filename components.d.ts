/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CenterMarker: (typeof import('./src/components/lomoMap/CenterMarker.vue'))['default']
    ControlPanel: (typeof import('./src/components/lomoMap/ControlPanel.vue'))['default']
    DrawView: (typeof import('./src/components/lomoMap/DrawView.vue'))['default']
    FgTabbar: (typeof import('./src/components/fg-tabbar/fg-tabbar.vue'))['default']
    LocationDisplay: (typeof import('./src/components/lomoMap/LocationDisplay.vue'))['default']
    MapCanvas: (typeof import('./src/components/lomoMap/MapCanvas.vue'))['default']
    WdButton: (typeof import('wot-design-uni/components/wd-button/wd-button.vue'))['default']
    WdCell: (typeof import('wot-design-uni/components/wd-cell/wd-cell.vue'))['default']
    WdCellGroup: (typeof import('wot-design-uni/components/wd-cell-group/wd-cell-group.vue'))['default']
    WdCheckbox: (typeof import('wot-design-uni/components/wd-checkbox/wd-checkbox.vue'))['default']
    WdForm: (typeof import('wot-design-uni/components/wd-form/wd-form.vue'))['default']
    WdIcon: (typeof import('wot-design-uni/components/wd-icon/wd-icon.vue'))['default']
    WdInput: (typeof import('wot-design-uni/components/wd-input/wd-input.vue'))['default']
    WdInputNumber: (typeof import('wot-design-uni/components/wd-input-number/wd-input-number.vue'))['default']
    WdNavbar: (typeof import('wot-design-uni/components/wd-navbar/wd-navbar.vue'))['default']
    WdSearch: (typeof import('wot-design-uni/components/wd-search/wd-search.vue'))['default']
    WdStatusTip: (typeof import('wot-design-uni/components/wd-status-tip/wd-status-tip.vue'))['default']
    WdToast: (typeof import('wot-design-uni/components/wd-toast/wd-toast.vue'))['default']
    YtCalendar: (typeof import('./src/components/ytWidget/special/datetime/yt-calendar.vue'))['default']
    YtCheckboxWithUpload: (typeof import('./src/components/ytWidget/special/upload/yt-checkbox-with-upload.vue'))['default']
    YtDatetimePicker: (typeof import('./src/components/ytWidget/special/datetime/yt-datetime-picker.vue'))['default']
    YtDynamicForm: (typeof import('./src/components/ytWidget/core/yt-dynamic-form.vue'))['default']
    YtInput: (typeof import('./src/components/ytWidget/inputs/yt-input.vue'))['default']
    YtInputNumber: (typeof import('./src/components/ytWidget/inputs/yt-input-number.vue'))['default']
    YtMapSelector: (typeof import('./src/components/ytWidget/special/map/yt-map-selector.vue'))['default']
    YtRegionCascader: (typeof import('./src/components/ytWidget/selectors/advanced/yt-region-cascader.vue'))['default']
    YtSelect: (typeof import('./src/components/ytWidget/selectors/basic/yt-select.vue'))['default']
    YtSelectMulti: (typeof import('./src/components/ytWidget/selectors/basic/yt-select-multi.vue'))['default']
    YtSelectSingle: (typeof import('./src/components/ytWidget/selectors/basic/yt-select-single.vue'))['default']
    YtTextarea: (typeof import('./src/components/ytWidget/inputs/yt-textarea.vue'))['default']
    YtUpload: (typeof import('./src/components/ytWidget/special/upload/yt-upload.vue'))['default']
  }
}
