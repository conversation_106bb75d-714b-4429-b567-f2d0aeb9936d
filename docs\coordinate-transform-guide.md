# 坐标系转换工具使用指南

## 概述

本工具使用 `gcoord` 库提供精确的 WGS84 ↔ GCJ02 坐标系转换功能。

### 坐标系说明

- **WGS84**: 世界大地测量系统坐标系，GPS设备采用的标准坐标系
- **GCJ02**: 中国国家测绘局制定的地理坐标系统，也叫"火星坐标系"，中国地图服务（如高德、百度等）使用此坐标系

### 转换精度

使用 gcoord 库的转换精度：

- 往返转换精度损失：< 1米
- 偏移量：通常在 300-800米 范围内（根据地理位置不同）

## 安装依赖

```bash
pnpm add gcoord
```

## 基本使用

### 1. 导入模块

```typescript
import {
  wgs84ToGcj02,
  gcj02ToWgs84,
  batchWgs84ToGcj02,
  batchGcj02ToWgs84,
  testCoordTransform,
  calculateDistance,
  isInChina,
  type CoordPoint,
} from '@/utils/coordTransform'
```

### 2. 单点转换

#### WGS84 → GCJ02 (GPS坐标 → 中国地图坐标)

```typescript
// GPS设备获取的坐标（WGS84）
const gpsPoint: CoordPoint = {
  latitude: 39.9042, // 纬度
  longitude: 116.4074, // 经度
}

// 转换为中国地图坐标（GCJ02）
const mapPoint = wgs84ToGcj02(gpsPoint)
console.log(mapPoint)
// 输出: { latitude: 39.905603, longitude: 116.413642 }
```

#### GCJ02 → WGS84 (中国地图坐标 → GPS坐标)

```typescript
// 中国地图上的坐标（GCJ02）
const mapPoint: CoordPoint = {
  latitude: 39.905603,
  longitude: 116.413642,
}

// 转换为GPS坐标（WGS84）
const gpsPoint = gcj02ToWgs84(mapPoint)
console.log(gpsPoint)
// 输出: { latitude: 39.9042, longitude: 116.4074 }
```

### 3. 批量转换

#### 批量 WGS84 → GCJ02

```typescript
const gpsPoints: CoordPoint[] = [
  { latitude: 39.9042, longitude: 116.4074 }, // 北京
  { latitude: 31.2304, longitude: 121.4737 }, // 上海
  { latitude: 22.3193, longitude: 114.1694 }, // 香港
]

const mapPoints = batchWgs84ToGcj02(gpsPoints)
console.log(mapPoints)
// 输出转换后的中国地图坐标数组
```

#### 批量 GCJ02 → WGS84

```typescript
const mapPoints: CoordPoint[] = [
  { latitude: 39.905603, longitude: 116.413642 },
  { latitude: 31.23189, longitude: 121.480139 },
]

const gpsPoints = batchGcj02ToWgs84(mapPoints)
console.log(gpsPoints)
// 输出转换后的GPS坐标数组
```

### 4. 工具函数

#### 计算两点距离

```typescript
const beijing = { latitude: 39.9042, longitude: 116.4074 }
const shanghai = { latitude: 31.2304, longitude: 121.4737 }

const distance = calculateDistance(beijing, shanghai)
console.log(distance) // 约 1068000 米 (1068公里)
```

#### 检查坐标是否在中国境内

```typescript
const beijing = { latitude: 39.9042, longitude: 116.4074 }
const newYork = { latitude: 40.7128, longitude: -74.006 }

console.log(isInChina(beijing)) // true
console.log(isInChina(newYork)) // false
```

#### 转换精度测试

```typescript
// 使用默认坐标测试
const result = testCoordTransform()

// 使用自定义坐标测试
const customResult = testCoordTransform({
  latitude: 39.9042,
  longitude: 116.4074,
})

console.log(customResult.precisionLoss) // 精度损失（米）
```

## 在 WKT 转换中的应用

WKT 转换工具已经集成了坐标系转换功能：

```typescript
import { wktToMapComponents } from '@/utils/wktConverter'

// WKT数据（WGS84坐标系）
const wktData = 'POINT (129.504 42.916)'

// 自动转换为GCJ02坐标系的地图组件
const mapComponents = wktToMapComponents(wktData)
console.log(mapComponents.markers)
// 输出: 已转换为GCJ02坐标系的标记点
```

## 在 UniApp 地图中的使用

```vue
<template>
  <map
    :latitude="mapCenter.latitude"
    :longitude="mapCenter.longitude"
    :markers="mapMarkers"
    :polygons="mapPolygons"
    :polyline="mapPolylines"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { wgs84ToGcj02 } from '@/utils/coordTransform'

const mapCenter = ref({ latitude: 39.9042, longitude: 116.4074 })
const mapMarkers = ref([])

onMounted(() => {
  // 假设从GPS设备获取的坐标（WGS84）
  const gpsCoords = [
    { latitude: 39.9042, longitude: 116.4074 },
    { latitude: 39.905, longitude: 116.408 },
  ]

  // 转换为地图坐标（GCJ02）
  mapMarkers.value = gpsCoords.map((coord, index) => {
    const gcjCoord = wgs84ToGcj02(coord)
    return {
      id: index + 1,
      latitude: gcjCoord.latitude,
      longitude: gcjCoord.longitude,
      iconPath: '/static/marker.png',
      width: 32,
      height: 32,
    }
  })

  // 地图中心也转换为GCJ02
  mapCenter.value = wgs84ToGcj02(mapCenter.value)
})
</script>
```

## 错误处理

所有转换函数都包含完整的错误处理：

```typescript
// 无效输入会返回原坐标并显示警告
const invalidPoint = { latitude: 'invalid', longitude: 200 }
const result = wgs84ToGcj02(invalidPoint)
// 控制台输出: ⚠️ 无效的WGS84坐标，返回原坐标
```

## 性能优化建议

1. **缓存转换结果**: 对于固定的坐标点，可以缓存转换结果避免重复计算
2. **批量转换**: 处理多个坐标时使用批量转换函数 `batchWgs84ToGcj02`
3. **按需转换**: 只在需要显示在中国地图上时才进行转换

## 常见问题

### Q: 为什么需要坐标转换？

A: 中国出于国家安全考虑，要求所有地图服务商使用加密后的坐标系（GCJ02），而GPS设备使用的是标准的WGS84坐标系，两者之间存在偏移。

### Q: 转换精度如何？

A: 使用 gcoord 库的转换精度很高，往返转换精度损失通常小于1米。

### Q: 是否所有坐标都需要转换？

A: 只有在中国境内的坐标才需要转换。工具提供了 `isInChina()` 函数来检查坐标是否在中国境内。

### Q: 如何验证转换是否正确？

A: 可以使用 `testCoordTransform()` 函数进行完整的转换测试，包括精度损失评估。

## 更新日志

### v2.0.0 (2025-06-11)

- 使用 gcoord 库替换手写转换算法
- 提升转换精度，精度损失 < 1米
- 增加完整的错误处理和参数验证
- 添加批量转换功能
- 增加详细的转换日志
