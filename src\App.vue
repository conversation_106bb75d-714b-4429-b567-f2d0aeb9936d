<script lang="ts" setup>
import { onHide, onLaunch, onShow } from '@dcloudio/uni-app'
import { getAccessToken } from '@/utils/auth'
import { useDictStore } from '@/store'

onLaunch(() => {
  console.log('App Launch')
  console.log('App Launch')

  uni.loadFontFace({
    family: 'HarmonyOS_Sans_Condensed',
    source: 'url("https://oss.urban.udo.top/public/fonts/HarmonyOS_Sans_Condensed_Regular.ttf")',
    // source: 'url("/static/SourceHanSansSC-Medium-2.otf")',
    global: true,
    success() {
      console.log('字体加载成功')
    },
    fail(err) {
      console.log('字体加载失败', err)
    },
  })
  // 检查token，如果没有token则跳转到登录页面
  checkTokenAndRedirect()
})

onShow(() => {
  console.log('App Show')
  const updateManager = uni.getUpdateManager()

  updateManager.onCheckForUpdate(function (res) {
    // 请求完新版本信息的回调
    console.log(res.hasUpdate)
  })

  updateManager.onUpdateReady(function (res) {
    uni.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      success(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate()
        }
      },
    })
  })

  updateManager.onUpdateFailed(function (res) {
    // 新的版本下载失败
  })
})

onHide(() => {
  console.log('App Hide')
})

// 检查token并重定向到登录页面
const checkTokenAndRedirect = async () => {
  try {
    // 使用auth工具函数获取token
    const token = getAccessToken()

    // 如果没有token，跳转到登录页面
    if (!token) {
      console.log('未找到token，跳转到登录页面')
      // 使用reLaunch清除页面栈，防止用户通过返回键回到主页面
      uni.reLaunch({
        url: '/pages/login/index',
        fail: (err) => {
          console.error('跳转登录页面失败:', err)
        },
      })
    } else {
      console.log('找到token:', token)
      const dictStore = useDictStore()
      if (!dictStore.dictInfo.isSetDict) {
        await dictStore.setDictMap()
      }
    }
  } catch (error) {
    console.error('获取token失败:', error)
    // 如果获取token出错，也跳转到登录页面
    uni.reLaunch({
      url: '/pages/login/index',
      fail: (err) => {
        console.error('跳转登录页面失败:', err)
      },
    })
  }
}
</script>

<style lang="scss">
page {
  font-family: 'HarmonyOS_Sans_Condensed';
  // 默认背景颜色
  background: #f2f3f7;
}
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
