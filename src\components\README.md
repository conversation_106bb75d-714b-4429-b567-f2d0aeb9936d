# YtWidget 组件库

基于 `wd-input`、`wd-textarea` 和 `wd-select-picker` 的二次封装组件，提供更便捷的表单输入功能。

## 组件列表

- **YtInput**: 输入框组件（基于 `wd-input`）
- **YtTextarea**: 文本域组件（基于 `wd-textarea`）
- **YtSelectSingle**: 单选选择器组件（基于 `wd-select-picker`）
- **YtSelectMulti**: 多选选择器组件（基于 `wd-select-picker`）
- **YtCalendar**: 日历选择器组件（基于 `wd-calendar`）
- **YtDatetimePicker**: 时间选择器组件（基于 `wd-datetime-picker`）
- **YtRegionCascader**: 城市多级选择器组件（基于 `wd-col-picker`）
- **YtMapSelector**: 地图位置选择器组件（基于 `wd-popup` + `map`）

## 功能特性

- 完整的 TypeScript 类型支持
- 双向数据绑定 (v-model)
- 支持表单验证配置
- 支持禁用、只读等状态控制
- 支持字符长度限制
- 统一的配置格式
- **单选多选分离**: 提供独立的单选和多选组件，逻辑更清晰

## YtInput 基本用法

```vue
<template>
  <yt-input v-model="formData.name" :item="nameConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtInput from '@/components/ytWidget/yt-input.vue'
import type { FormItem } from '@/types/form'

const formData = ref({
  name: ''
})

const nameConfig: FormItem = {
  type: 'input',
  field: 'name',
  title: '姓名',
  info: '请输入您的姓名',
  $required: true,
  props: {
    maxlength: 20,
    disabled: false,
    readonly: false
  }
}
</script>
```

## YtTextarea 基本用法

```vue
<template>
  <yt-textarea v-model="formData.description" :item="descConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtTextarea from '@/components/ytWidget/yt-textarea.vue'
import type { FormItem } from '@/types/form'

const formData = ref({
  description: ''
})

const descConfig: FormItem = {
  type: 'textarea',
  field: 'description',
  title: '项目描述',
  info: '请输入项目描述',
  $required: '请输入项目描述',
  props: {
    rows: 4,
    maxlength: 500,
    showCount: true,
    autosize: true
  }
}
</script>
```

## YtSelectSingle 单选用法

```vue
<template>
  <yt-select-single v-model="formData.category" :item="singleConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtSelectSingle from '@/components/ytWidget/yt-select-single.vue'
import type { FormItem } from '@/types/form'

const formData = ref({
  category: ''
})

const singleConfig: FormItem = {
  type: 'select',
  field: 'category',
  title: '分类选择',
  info: '请选择分类',
  $required: '请选择分类',
  props: {
    clearable: true,
    placeholder: '请选择...'
  },
  options: [
    { label: '前端开发', value: 'frontend' },
    { label: '后端开发', value: 'backend' },
    { label: '移动开发', value: 'mobile' }
  ]
}
</script>
```

## YtSelectMulti 多选用法

```vue
<template>
  <yt-select-multi v-model="formData.tags" :item="multiConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtSelectMulti from '@/components/ytWidget/yt-select-multi.vue'
import type { FormItem } from '@/types/form'

const formData = ref({
  tags: []
})

const multiConfig: FormItem = {
  type: 'select',
  field: 'tags',
  title: '技能标签',
  info: '请选择技能标签',
  $required: '请至少选择一个标签',
  props: {
    clearable: true,
    multiple: true,
    placeholder: '请选择标签...'
  },
  options: [
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'TypeScript', value: 'typescript' },
    { label: 'Node.js', value: 'nodejs' }
  ]
}
</script>
```

## 智能选择器用法（推荐）

在实际项目中，您可以根据配置自动选择使用单选或多选组件：

```vue
<template>
  <template v-for="(item, index) in formList" :key="index">
    <!-- 根据类型和配置动态选择组件 -->
    <yt-input v-if="item.type === 'input'" v-model="model[item.field]" :item="item" />
    <yt-textarea v-else-if="item.type === 'textarea'" v-model="model[item.field]" :item="item" />
    <!-- 根据multiple属性选择单选或多选 -->
    <template v-else-if="item.type === 'select'">
      <yt-select-multi v-if="item.props?.multiple" v-model="model[item.field]" :item="item" />
      <yt-select-single v-else v-model="model[item.field]" :item="item" />
    </template>
  </template>
</template>
```

## 属性配置

### Props

| 属性名     | 类型                                     | 必填 | 说明           |
| ---------- | ---------------------------------------- | ---- | -------------- |
| item       | FormItem                                 | 是   | 表单项配置对象 |
| modelValue | string \| number \| string[] \| number[] | 否   | 输入框的值     |

### FormItem 接口

```typescript
interface FormItem {
  type: string // 组件类型 ('input' | 'textarea' | 'select')
  field: string // 字段名
  title: string // 标签文本
  info: string // 占位符文本
  $required?: boolean | string // 是否必填，字符串时为错误提示
  props?: FormItemProps // 额外属性配置
  options?: FormSelectOption[] // 选择器选项 (select专用)
  display?: boolean // 是否显示
  hidden?: boolean // 是否隐藏
  [key: string]: any // 其他自定义属性
}

interface FormSelectOption {
  label: string // 选项显示文本
  value: string | number // 选项值
  disabled?: boolean // 是否禁用
  [key: string]: any // 其他属性
}

interface FormItemProps {
  // 通用属性
  disabled?: boolean // 是否禁用
  readonly?: boolean // 是否只读
  maxlength?: number // 最大字符长度
  minlength?: number // 最小字符长度
  clearable?: boolean // 是否可清空
  placeholder?: string // 占位符文本

  // input 特有属性
  showPassword?: boolean // 是否显示密码
  suffixIcon?: string // 后缀图标
  prefixIcon?: string // 前缀图标

  // textarea 特有属性
  rows?: number // 行数
  autosize?: boolean // 自适应高度
  showCount?: boolean // 显示字符计数

  // select 特有属性
  multiple?: boolean // 是否多选
  filterable?: boolean // 是否可筛选
  _optionType?: number // 选项类型

  [key: string]: any // 其他属性
}
```

## Events

| 事件名            | 参数                                            | 说明         |
| ----------------- | ----------------------------------------------- | ------------ |
| update:modelValue | value: string \| number \| string[] \| number[] | 值变化时触发 |

## 示例

- 输入框示例：`src/pages/demo/inputDemo.vue`
- 文本域示例：`src/pages/demo/textareaDemo.vue`
- 选择器示例：`src/pages/demo/selectDemo.vue`

## YtCalendar 日历用法

```vue
<template>
  <yt-calendar v-model="formData.selectedDate" :item="calendarConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtCalendar from '@/components/ytWidget/yt-calendar.vue'
import type { FormItem } from '@/types/form'

const formData = ref({
  selectedDate: Date.now() // 13位时间戳
})

const calendarConfig: FormItem = {
  type: 'calendar',
  field: 'selectedDate',
  title: '选择日期',
  info: '请选择一个日期',
  $required: '请选择日期',
  props: {
    calendarType: 'date', // 日历类型
    showConfirm: true,
    clearable: true,
    minDate: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30天前
    maxDate: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30天后
  }
}
</script>
```

### 日历类型说明

| 类型          | 说明             | 返回值类型        |
| ------------- | ---------------- | ----------------- |
| date          | 单日期选择       | number (时间戳)   |
| dates         | 多日期选择       | number[] (时间戳) |
| week          | 周选择           | number (时间戳)   |
| month         | 月选择           | number (时间戳)   |
| daterange     | 日期范围选择     | number[] (时间戳) |
| weekrange     | 周范围选择       | number[] (时间戳) |
| monthrange    | 月范围选择       | number[] (时间戳) |
| datetime      | 日期时间选择     | string            |
| datetimerange | 日期时间范围选择 | string[]          |

### Calendar 特有属性 (props.calendarType)

```typescript
interface CalendarProps {
  calendarType?:
    | 'date'
    | 'dates'
    | 'week'
    | 'month'
    | 'daterange'
    | 'weekrange'
    | 'monthrange'
    | 'datetime'
    | 'datetimerange'
  minDate?: number // 最小日期（13位时间戳）
  maxDate?: number // 最大日期（13位时间戳）
  showConfirm?: boolean // 是否显示确认按钮
  showTypeSwitch?: boolean // 是否显示类型切换
  firstDayOfWeek?: number // 一周的第一天 (0-6, 0为周日)
  allowSameDay?: boolean // 范围选择是否允许同一天
  hideSecond?: boolean // 是否隐藏秒
  timeFilter?: any // 时间过滤函数
  withCell?: boolean // 是否使用内置 cell 选择器
}
```

## 示例

- 表单综合示例：`src/pages/demo/formDemo.vue`
- 输入框示例：`src/pages/demo/inputDemo.vue`
- 文本域示例：`src/pages/demo/textareaDemo.vue`
- 选择器示例：`src/pages/demo/selectDemo.vue`
- **日历示例：`src/pages/demo/calendarDemo.vue`**

## YtDatetimePicker 时间选择用法

```vue
<template>
  <yt-datetime-picker v-model="formData.selectedTime" :item="timeConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtDatetimePicker from '@/components/ytWidget/yt-datetime-picker.vue'
import type { FormItem } from '@/types/form'

const formData = ref({
  selectedTime: '12:30' // 默认为时间字符串格式 (HH:mm)
})

const timeConfig: FormItem = {
  type: 'timePicker',
  field: 'selectedTime',
  title: '选择时间',
  info: '请选择时间（默认只显示时分秒）',
  $required: '请选择时间',
  props: {
    pickerType: 'time', // 默认类型，只显示时分秒
    showConfirm: true,
    clearable: true,
    minHour: 9, // 可选：最小小时
    maxHour: 18, // 可选：最大小时
  }
}
</script>
```

### 时间选择器类型说明

| 类型       | 说明               | 返回值类型          | 默认使用 |
| ---------- | ------------------ | ------------------- | -------- |
| time       | 时间选择（时分秒） | string (HH:mm 格式) | ✅       |
| datetime   | 日期时间选择       | number (时间戳)     | -        |
| date       | 日期选择           | number (时间戳)     | -        |
| year-month | 年月选择           | number (时间戳)     | -        |
| year       | 年选择             | number (时间戳)     | -        |

**注意：** YtDatetimePicker 组件默认配置为 `pickerType: 'time'`，只显示时分秒，不显示日期。如需显示日期请明确设置 `pickerType` 为其他类型。

## 示例

- 表单综合示例：`src/pages/demo/formDemo.vue`
- 输入框示例：`src/pages/demo/inputDemo.vue`
- 文本域示例：`src/pages/demo/textareaDemo.vue`
- 选择器示例：`src/pages/demo/selectDemo.vue`
- **日历示例：`src/pages/demo/calendarDemo.vue`**
- **时间选择示例：`src/pages/demo/datetimePickerDemo.vue`**

## YtRegionCascader 城市多级选择用法

```vue
<template>
  <yt-region-cascader v-model="formData.selectedRegion" :item="regionConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtRegionCascader from '@/components/ytWidget/yt-region-cascader.vue'
import type { FormItem } from '@/types/form'

const formData = ref({
  selectedRegion: [] // 数组格式，如 ['110000', '110100', '110101']
})

const regionConfig: FormItem = {
  type: 'RegionCascader',
  field: 'selectedRegion',
  title: '选择地区',
  info: '请选择省市区',
  $required: '请选择地区',
  props: {
    placeholder: '请选择省市区',
    clearable: true,
    autoComplete: true, // 自动补全数据
  }
}
</script>
```

### 级联选择器特性

- **多级级联**: 支持省市区多级选择
- **异步加载**: 动态请求子级数据
- **API驱动**: 通过配置 API 地址获取数据
- **自动补全**: 支持根据已选值自动补全层级
- **错误处理**: 网络请求失败时自动提示

### API 数据格式要求

组件会自动处理以下几种 API 响应格式：

```typescript
// 格式1: 直接数组
;[
  { id: '110000', name: '北京市', code: '110000', level: 1 },
  // ...
]

// 格式2: data 包装
{
  data: [
    { id: '110000', name: '北京市', code: '110000', level: 1 },
    // ...
  ]
}

// 格式3: list 包装
{
  list: [
    { id: '110000', name: '北京市', code: '110000', level: 1 },
    // ...
  ]
}
```

### RegionCascader 特有属性 (props)

```typescript
interface RegionCascaderProps {
  apiUrl?: string // API地址，默认为组件内置地址
  columns?: any[][] // 列数据（通常由组件自动管理）
  autoComplete?: boolean // 自动补全数据
  loadingColor?: string // 加载颜色
  lineWidth?: number // 底部条宽度
  lineHeight?: number // 底部条高度
  // ... 继承自 col-picker 的其他属性
}
```

### 使用说明

1. **API 服务**: 组件使用标准的 `RegionAPI.getRegionList()` 服务，会自动添加 `parentCode` 参数来获取子级数据
2. **请求格式**: 遵循项目的 HTTP 工具函数，包含认证头、租户信息等
3. **数据转换**: 自动将 API 返回的数据转换为 `{ value, label }` 格式
4. **显示格式**: 选中结果以 "省 / 市 / 区" 格式显示
5. **返回值**: v-model 绑定的是编码数组，如 `['110000', '110100', '110101']`
6. **错误处理**: 网络请求失败时会显示友好的错误提示

## 示例

- 表单综合示例：`src/pages/demo/formDemo.vue`
- 输入框示例：`src/pages/demo/inputDemo.vue`
- 文本域示例：`src/pages/demo/textareaDemo.vue`
- 选择器示例：`src/pages/demo/selectDemo.vue`
- **日历示例：`src/pages/demo/calendarDemo.vue`**
- **时间选择示例：`src/pages/demo/datetimePickerDemo.vue`**
- **城市级联示例：`src/pages/demo/regionCascaderDemo.vue`**

## YtMapSelector 地图位置选择用法

```vue
<template>
  <yt-map-selector v-model="formData.selectedLocation" :item="mapConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtMapSelector from '@/components/ytWidget/yt-map-selector.vue'
import type { FormItem } from '@/types/form'

const formData = ref({
  selectedLocation: null // 地图位置对象
})

const mapConfig: FormItem = {
  type: 'mapSelector',
  field: 'selectedLocation',
  title: '选择位置',
  info: '请选择一个位置',
  $required: '请选择一个位置',
  props: {
    // 地图配置选项
  }
}
</script>
```

### 地图选择器特性

- **地图显示**: 支持地图显示和位置选择
- **位置标记**: 支持在地图上标记位置
- **位置搜索**: 支持根据地址搜索位置
- **位置编辑**: 支持手动编辑位置

### 使用说明

1. **地图配置**: 根据需要配置地图选项，如缩放级别、中心点等
2. **位置获取**: 通过地图选择或地址搜索获取位置信息
3. **位置编辑**: 支持手动编辑位置信息
4. **返回值**: v-model 绑定的是位置对象，如 `{ latitude, longitude }`
5. **错误处理**: 网络请求失败时会显示友好的错误提示

## 示例

- 表单综合示例：`src/pages/demo/formDemo.vue`
- 输入框示例：`src/pages/demo/inputDemo.vue`
- 文本域示例：`src/pages/demo/textareaDemo.vue`
- 选择器示例：`src/pages/demo/selectDemo.vue`
- **地图选择示例：`src/pages/demo/mapSelectorDemo.vue`**
