<template>
  <view :style="markerStyle" class="center-marker"></view>
  <!-- 坐标显示已隐藏 -->
  <!-- <view class="control-wrapper">
    <LocationDisplay :latitude="props.marker.latitude" :longitude="props.marker.longitude" />
  </view> -->
</template>

<script lang="ts" setup>
import { computed, defineProps, watch } from 'vue'
// import LocationDisplay from '@/components/lomoMap/LocationDisplay.vue' // 已移除坐标显示

const props = defineProps({
  isMoving: {
    type: Boolean,
    default: false,
  },
  marker: {
    type: Object,
    default: () => ({
      latitude: 0,
      longitude: 0,
    }),
  },
  mapCanvasId: {
    default: '',
  },
})
const emits = defineEmits(['update:marker'])
const markerStyle = computed(() => ({
  transform: `translate(-50%, -50%) translateY(${props.isMoving ? -10 : 0}px)`,
  transition: props.isMoving ? 'transform 0.1s ease-out' : 'transform 0.2s ease-out',
}))
watch(
  () => props.isMoving,
  (isMoving) => {
    if (!isMoving) {
      getCenterLocation()
    }
  },
)
/**
 * @description  更新地图中心点位置
 */
const getCenterLocation = async () => {
  try {
    const ctx = uni.createMapContext('myMap')
    const res = await new Promise((resolve, reject) => {
      ctx.getCenterLocation({
        success: resolve,
        fail: reject,
      })
    })
    // 触发坐标更新
    console.log('地图中心点更新:', res)
    emits('update:marker', {
      latitude: res.latitude,
      longitude: res.longitude,
    })
  } catch (err) {
    console.error('坐标获取失败:', err)
  }
}
</script>

<style lang="scss" scoped>
.center-marker {
  position: absolute;
  top: 48%;
  left: 50%;
  z-index: 999;
  width: 40px;
  height: 40px;
  background-image: url('@/static/plot.png');
  background-size: 100% 100%;
}
.control-wrapper {
  position: absolute;
  top: 10px;
  left: 20px;
  display: flex;
  //gap: 15px;
  pointer-events: none;
}
</style>
