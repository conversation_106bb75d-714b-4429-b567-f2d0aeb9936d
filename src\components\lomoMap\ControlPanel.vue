<!-- components/ControlPanel.vue -->
<template>
  <view class="control-panel">
    <!-- 点绘制按钮 -->
    <wd-button
      :type="content == 1 ? 'primary' : 'info'"
      icon="location"
      size="small"
      @click="handleStart(1)"
    >
      点
    </wd-button>

    <!-- 线绘制按钮 -->
    <wd-button
      :type="content == 2 ? 'primary' : 'info'"
      icon="decrease"
      size="small"
      @click="handleStart(2)"
    >
      线
    </wd-button>

    <!-- 面绘制按钮 -->
    <wd-button
      :type="content == 3 ? 'primary' : 'info'"
      icon="rectangle"
      size="small"
      @click="handleStart(3)"
    >
      面
    </wd-button>

    <!-- 完成绘制按钮 - 仅在绘制线或面时显示 -->
    <wd-button
      v-if="content === 2 || content === 3"
      icon="checkmark-outline"
      size="small"
      type="success"
      @click="handleFinish"
    >
      完成
    </wd-button>

    <!-- 清空按钮 - 仅在有绘制内容时显示 -->
    <wd-button
      v-if="hasPolygons"
      icon="trash-outline"
      size="small"
      type="error"
      @click="handleClear"
    >
      清空
    </wd-button>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface Props {
  drawing: boolean
  hasPolygons?: boolean
}

defineProps<Props>()

const content = ref(0)
const emit = defineEmits(['start', 'finish', 'clear'])

const handleStart = (index: number) => {
  if (content.value === index) {
    // 如果重复点击相同模式，给出提示
    if (index === 1) {
      uni.showToast({ title: '继续绘制点标记', icon: 'none' })
    } else if (index === 2) {
      uni.showToast({ title: '开始新线段（自动完成上一条）', icon: 'none' })
    } else if (index === 3) {
      uni.showToast({ title: '开始新多边形（自动完成上一个）', icon: 'none' })
    }
  }

  content.value = index
  emit('start', content.value)
}

const handleFinish = () => {
  emit('finish')
  content.value = 0
}

const handleClear = () => {
  emit('clear')
  content.value = 0
}
</script>

<style lang="scss" scoped>
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-btn {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1;
  border-radius: 6px;
  transition: all 0.2s;

  &[disabled] {
    filter: grayscale(0.8);
    opacity: 0.6;
  }

  &.primary {
    background-color: #2979ff;
  }

  &.success {
    background-color: #18bc37;
  }

  &.danger {
    background-color: #ff4444;
  }

  text {
    color: #fff;
  }
}
</style>
