<template>
  <view :style="markerStyle" class="center-marker"></view>
  <view class="control-wrapper">
    <LocationDisplay :latitude="props.marker.latitude" :longitude="props.marker.longitude" />
  </view>
  <view class="control-wrapper-btn">
    <wd-button icon="check" type="icon" @click="handleConfirm"></wd-button>
    <wd-button icon="close" type="icon" @click="handleCancel"></wd-button>
    <wd-button icon="delete-thin" type="icon" @click="handleDelete"></wd-button>
  </view>
</template>

<script lang="ts" setup>
import { computed, defineProps, watch } from 'vue'
import LocationDisplay from '@/components/lomoMap/LocationDisplay.vue'

const props = defineProps({
  isMoving: {
    type: Boolean,
    default: false,
  },
  marker: {
    type: Object,
    default: () => ({
      latitude: 0,
      longitude: 0,
    }),
  },
  mapCanvasId: {
    default: '',
  },
})
const emits = defineEmits(['update:marker', 'confirm', 'cancel', 'delete'])
const markerStyle = computed(() => ({
  transform: `translate(-50%, -50%) translateY(${props.isMoving ? -10 : 0}px)`,
  transition: props.isMoving ? 'transform 0.1s ease-out' : 'transform 0.2s ease-out',
}))

watch(
  () => props.isMoving,
  (isMoving) => {
    console.log('🔍 [DrawView] 地图移动状态变化:', isMoving)
    if (!isMoving) {
      console.log('🔍 [DrawView] 地图停止移动，开始获取中心点坐标')
      getCenterLocation()
    }
  },
)

/**
 * @description 确认按钮点击事件 - 添加当前坐标到绘制中
 */
const handleConfirm = () => {
  console.log('🔍 [DrawView] 确认添加点位:', props.marker)
  // 发送当前坐标到父组件进行绘制处理
  emits('confirm', props.marker)
}

/**
 * @description 取消按钮点击事件 - 删除上一个点
 */
const handleCancel = () => {
  console.log('🔍 [DrawView] 取消操作 - 删除上一个点')
  // 发送删除上一个点的事件到父组件
  emits('cancel')
}

/**
 * @description 删除按钮点击事件 - 清空当前绘制
 */
const handleDelete = () => {
  console.log('🔍 [DrawView] 删除当前绘制')
  // 发送删除当前绘制的事件到父组件
  emits('delete', props.marker)
}

/**
 * @description  更新地图中心点位置
 */
const getCenterLocation = async () => {
  try {
    // 使用传入的mapCanvasId，如果没有则使用默认的'myMap'
    const mapId = props.mapCanvasId || 'myMap'
    console.log('🔍 [DrawView] 使用地图ID:', mapId)
    const ctx = uni.createMapContext(mapId)
    const res = await new Promise<{ latitude: number; longitude: number }>((resolve, reject) => {
      ctx.getCenterLocation({
        success: resolve,
        fail: reject,
      })
    })
    // 触发坐标更新
    console.log('🔍 [DrawView] 地图中心点获取成功:', res)
    emits('update:marker', {
      latitude: res.latitude,
      longitude: res.longitude,
    })
    console.log('🔍 [DrawView] 已发送update:marker事件')
  } catch (err) {
    console.error('🔍 [DrawView] 坐标获取失败:', err)
  }
}
</script>

<style lang="scss" scoped>
.center-marker {
  position: absolute;
  top: 48%;
  left: 50%;
  z-index: 999;
  width: 40px;
  height: 40px;
  background-image: url('@/static/fixedPoint.png');
  background-size: 100% 100%;
}
.control-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 15px;
  pointer-events: none;
}
.control-wrapper-btn {
  position: fixed;
  bottom: 20px;
  left: 50%;
  z-index: 1000;
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  pointer-events: auto;

  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateX(-50%);

  :deep(.wd-button) {
    padding: 10px;
    margin: 0 8px;
    background-color: transparent;
    border: 1px solid #e0e0e0;
    border-radius: 8px;

    &:hover {
      background-color: #f5f5f5;
    }

    &:active {
      background-color: #e0e0e0;
    }
  }
}
</style>
