<template>
  <view class="location-panel">
    <view class="coordinates">
      <text class="coordinate">lon:{{ longitude.toFixed(6) }}</text>
      <text class="coordinate">lat:{{ latitude.toFixed(6) }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'

defineProps({
  longitude: { type: Number, required: true },
  latitude: { type: Number, required: true },
})
</script>

<style lang="scss" scoped>
.location-panel {
  padding: 12px;
  background: rgba($color: #000, $alpha: 0.4);
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

  .label {
    font-weight: 500;
    color: #ffffff;
  }

  .coordinate {
    display: block;
    font-size: 12px;
    line-height: 1.6;
    color: #ffffff;
  }
}
</style>
