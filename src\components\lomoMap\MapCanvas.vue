<template>
  <!--   :markers="covers"
    :polygons="polygons"-->
  <map
    id="myMap"
    :latitude="latitude"
    :longitude="longitude"
    :scale="scale"
    :show-location="false"
    :enable-satellite="true"
    class="map-container"
    @regionchange="handleRegionChange"
    @tap="emit('tap', $event)"
  >
    <slot />
  </map>
</template>

<script lang="ts" setup>
import { defineEmits, defineExpose, defineProps, onMounted, ref } from 'vue'

const latitude = ref(43.823755)
const longitude = ref(125.277062)

const props = defineProps({
  // latitude: { type: Number, required: true },
  // longitude: { type: Number, required: true },
  scale: { type: Number, default: 14 },
  covers: { type: Array, default: () => [] },
  polygons: { type: Array, default: () => [] },
})

const emit = defineEmits(['regionchange', 'tap'])
const handleRegionChange = (e) => {
  // 触发基础事件
  // emit('regionchange', e)
  // 仅在拖动结束时触发坐标更新
  if (e.type === 'end') {
    getCenterLocation()
  }
}
onMounted(() => {
  getCenterLocation()
})
// 中心点坐标获取
const getCenterLocation = async () => {
  console.log('🚀🚀🚀~~~当前 44 行,方法名：getCenterLocation，变量：123=====', 123)
  try {
    const ctx = uni.createMapContext('myMap')
    // const res = await new Promise((resolve, reject) => {
    //   ctx.getCenterLocation({
    //     success: resolve,
    //     fail: reject
    //   })
    // })
    ctx.getCenterLocation({
      success: (res) => {
        console.log('🚀🚀🚀~~~当前 46 行,方法名：success，变量：res=====', res)
      },
      fail: (e) => {
        console.log('🚀🚀🚀~~~当前 51 行,方法名：fail，变量：e=====', e)
      },
    })
    // 触发坐标更新
    // emit('update:latitude', res.latitude)
    // emit('update:longitude', res.longitude)
    // console.log('地图中心点更新:', res)
  } catch (err) {
    console.error('坐标获取失败:', err)
  }
}
// const mapContext=()=>{
//   return uni.createMapContext('myMap')
// }
// 暴露地图上下文供父组件使用
defineExpose({
  refreshLocation: getCenterLocation,
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100vh;
}
</style>
