# YtWidget 架构设计文档 v2.0

## 📋 概览

YtWidget 是一个基于 Vue3 + TypeScript + wot-design-uni 的企业级动态表单组件库，采用分层架构设计，支持多端运行（H5、小程序、APP）。

## 🏗️ 架构设计原则

### 1. 分层设计

- **核心层 (Core)**: 动态表单引擎
- **基础层 (Inputs)**: 基础输入控件
- **选择层 (Selectors)**: 选择器组件
- **功能层 (Special)**: 特殊功能组件
- **业务层 (Composites)**: 复合业务组件

### 2. 设计原则

- **单一职责**: 每个组件职责明确
- **开放封闭**: 对扩展开放，对修改封闭
- **依赖倒置**: 依赖抽象而非具体实现
- **组合优于继承**: 通过组合实现复杂功能
- **约定优于配置**: 提供合理默认值

## 📁 目录结构

```
src/components/ytWidget/
├── 📦 core/                           # 核心系统
│   └── yt-dynamic-form.vue           # 动态表单核心引擎
├── 🔤 inputs/                         # 基础输入层
│   ├── yt-input.vue                  # 文本输入框
│   ├── yt-textarea.vue               # 多行文本域
│   └── yt-input-number.vue           # 数字输入框
├── 🎯 selectors/                      # 选择器层
│   ├── basic/                        # 基础选择器
│   │   ├── yt-select.vue            # 通用选择器
│   │   ├── yt-select-single.vue     # 单选选择器
│   │   └── yt-select-multi.vue      # 多选选择器
│   └── advanced/                     # 高级选择器
│       ├── yt-region-cascader.vue   # 地区级联选择器
│       └── yt-dict-select.vue       # 字典选择器
├── ⚡ special/                        # 特殊功能层
│   ├── datetime/                     # 日期时间组件
│   │   ├── yt-calendar.vue          # 日历组件
│   │   └── yt-datetime-picker.vue   # 日期时间选择器
│   ├── upload/                       # 上传组件
│   │   ├── yt-upload.vue            # 基础上传组件
│   │   └── yt-checkbox-with-upload.vue # 复选框+上传
│   └── map/                          # 地图组件
│       └── yt-map-selector.vue      # 地图选择器
├── 🧩 composites/                     # 复合业务层
│   ├── yt-table-form.vue             # 表格表单组件
│   └── yt-dict-select-with-other.vue # 字典+其他选择器
├── 🛠️ utils/                          # 工具函数层
│   ├── validators.ts                 # 验证工具
│   ├── formatters.ts                # 格式化工具
│   └── helpers.ts                   # 辅助工具
├── 📝 types/                          # 类型定义层
│   ├── form.ts                      # 表单相关类型
│   ├── components.ts                # 组件相关类型
│   └── index.ts                     # 类型导出
├── 📚 docs/                           # 文档系统
│   ├── api/                         # API文档
│   ├── examples/                    # 示例代码
│   ├── guides/                      # 使用指南
│   └── migration/                   # 迁移指南
├── 🧪 __tests__/                      # 测试系统
│   ├── unit/                        # 单元测试
│   ├── integration/                 # 集成测试
│   └── e2e/                         # 端到端测试
├── 📦 index.ts                        # 主导出文件
├── 📖 README.md                       # 主要文档
├── 📋 CHANGELOG.md                    # 更新日志
├── 🔄 MIGRATION-GUIDE.md              # 迁移指南
└── 🏗️ ARCHITECTURE.md                 # 架构文档（本文件）
```

## 🎯 核心组件设计

### 1. YtDynamicForm (核心引擎)

```typescript
// 核心职责
- 表单数据管理
- 组件动态渲染
- 验证规则处理
- 事件统一分发

// 设计模式
- 策略模式：组件类型策略
- 观察者模式：数据变化监听
- 工厂模式：组件实例创建
```

### 2. 分层组件设计

```typescript
// 基础层 (Inputs)
interface BaseInput {
  value: any
  disabled?: boolean
  readonly?: boolean
  placeholder?: string
  rules?: ValidationRule[]
}

// 选择层 (Selectors)
interface BaseSelector extends BaseInput {
  options?: SelectOption[]
  multiple?: boolean
  remote?: boolean
  remoteMethod?: Function
}

// 功能层 (Special)
interface SpecialComponent extends BaseInput {
  specialProps?: Record<string, any>
  customValidator?: Function
  asyncLoader?: Function
}
```

## 🔧 核心机制

### 1. 组件注册机制

```typescript
// 组件类型映射
const COMPONENT_MAP = {
  input: () => import('./inputs/yt-input.vue'),
  select: () => import('./selectors/basic/yt-select.vue'),
  RegionCascader: () => import('./selectors/advanced/yt-region-cascader.vue'),
  // ...
}

// 动态组件加载
const loadComponent = (type: string) => {
  return COMPONENT_MAP[type]?.() || null
}
```

### 2. 数据流管理

```typescript
// 数据流向
User Input → Component → DynamicForm → Parent → Store

// 数据验证流程
Input Change → Field Validation → Form Validation → Submit Validation
```

### 3. 配置驱动渲染

```typescript
// 配置结构
interface FormItem {
  type: ComponentType // 组件类型
  field: string // 字段名
  title: string // 标题
  props?: ComponentProps // 组件属性
  rules?: ValidationRule[] // 验证规则
  // ...
}

// 渲染逻辑
config.forEach((item) => {
  const Component = getComponent(item.type)
  render(Component, item.props, item.value)
})
```

## 🔄 扩展机制

### 1. 组件扩展

```typescript
// 新增组件步骤
1. 创建组件文件 (.vue)
2. 实现组件接口 (ComponentInterface)
3. 注册组件类型 (COMPONENT_MAP)
4. 添加类型定义 (types/)
5. 编写测试用例 (__tests__/)
6. 更新文档 (docs/)
```

### 2. 功能扩展

```typescript
// 插件机制 (计划中)
interface Plugin {
  name: string
  install: (app: App, options: any) => void
  components?: Record<string, Component>
  validators?: Record<string, Function>
}
```

## 🎨 样式架构

### 1. 样式层级

```scss
// 样式优先级
1. wot-design-uni 基础样式
2. ytWidget 组件样式
3. 主题定制样式
4. 用户自定义样式
```

### 2. 主题系统

```scss
// CSS 变量系统
:root {
  --yt-primary-color: #1989fa;
  --yt-success-color: #52c41a;
  --yt-warning-color: #fa8c16;
  --yt-error-color: #f5222d;
  // ...
}
```

## 📊 性能优化

### 1. 组件懒加载

```typescript
// 按需加载
const YtInput = defineAsyncComponent(() => import('./inputs/yt-input.vue'))

// 分包加载
const Selectors = {
  YtSelect: () => import('./selectors/basic/yt-select.vue'),
  // ...
}
```

### 2. 渲染优化

```typescript
// 虚拟滚动 (大表单)
- 只渲染可视区域组件
- 动态计算组件高度
- 智能预加载机制

// 防抖优化
- 输入防抖：300ms
- 验证防抖：500ms
- 提交防抖：1000ms
```

## 🧪 测试策略

### 1. 测试分层

```typescript
// 单元测试 - 组件级别
;-组件渲染测试 -
  属性传递测试 -
  事件触发测试 -
  边界条件测试 -
  // 集成测试 - 功能级别
  表单数据流测试 -
  组件交互测试 -
  验证规则测试 -
  // E2E测试 - 场景级别
  完整表单流程 -
  跨组件交互 -
  用户操作路径
```

### 2. 测试工具链

```typescript
// 测试框架
- Vitest: 单元测试
- Vue Test Utils: 组件测试
- Playwright: E2E测试

// 覆盖率要求
- 单元测试: > 80%
- 集成测试: > 70%
- E2E测试: 核心路径 100%
```

## 🔒 安全设计

### 1. 输入安全

```typescript
// XSS 防护
- 输入内容转义
- HTML 标签过滤
- 脚本注入检测

// 数据验证
- 客户端验证
- 服务端验证
- 双重验证机制
```

### 2. 权限控制

```typescript
// 字段级权限
interface FieldPermission {
  read: boolean // 可读
  write: boolean // 可写
  visible: boolean // 可见
}

// 组件级权限
interface ComponentPermission {
  access: string[] // 访问角色
  operate: string[] // 操作权限
}
```

## 📈 监控与分析

### 1. 性能监控

```typescript
// 关键指标
;-组件加载时间 - 表单渲染时间 - 用户交互响应时间 - 内存使用情况

// 监控实现
const performanceMonitor = {
  trackComponentLoad: (component: string, duration: number) => {},
  trackFormRender: (formId: string, duration: number) => {},
  trackUserInteraction: (action: string, duration: number) => {},
}
```

### 2. 错误监控

```typescript
// 错误捕获
;-组件渲染错误 - 数据验证错误 - 网络请求错误 - 用户操作错误

// 错误上报
const errorReporter = {
  captureException: (error: Error, context: any) => {},
  reportUserAction: (action: string, error: any) => {},
}
```

## 🚀 未来规划

### v2.1.0 - 功能增强

- [ ] 表单联动优化
- [ ] 自定义验证器
- [ ] 主题定制系统
- [ ] 国际化支持

### v2.2.0 - 组件扩展

- [ ] 新增 Table 组件
- [ ] 新增 Tree 组件
- [ ] 新增 Transfer 组件
- [ ] 新增 Steps 组件

### v3.0.0 - 架构升级

- [ ] 微前端支持
- [ ] 插件系统
- [ ] 可视化配置
- [ ] 云端配置管理

---

**文档版本**: v2.0.0  
**最后更新**: 2025-06-26 10:34:12 +08:00  
**维护者**: YueTong Team
