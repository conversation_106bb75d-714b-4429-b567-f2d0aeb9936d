# YtWidget 更新日志

本文档记录了 YtWidget 动态表单组件库的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本号遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [2.0.0] - 2025-06-26

### 🎉 重大更新

#### 新增 (Added)

- **🏗️ 全新架构设计**

  - 分层组件架构：Core、Inputs、Selectors、Special、Composites
  - 模块化目录结构，提升代码组织性
  - 支持按需导入和 Tree Shaking

- **📦 组件分类优化**

  - `core/` - 核心动态表单系统
  - `inputs/` - 基础输入控件（重命名自 form-controls）
  - `selectors/` - 选择器组件（重命名自 selection-components）
    - `basic/` - 基础选择器
    - `advanced/` - 高级选择器
  - `special/` - 特殊功能组件
    - `datetime/` - 日期时间组件
    - `upload/` - 上传组件
    - `map/` - 地图组件
  - `composites/` - 复合业务组件

- **🆕 RegionCascader 增强功能**

  - 支持 `rootParentCode` 配置，可指定根级区域代码
  - 向下兼容，默认值为 '222400'
  - 支持 `/urban/region/list?parentCode=222401` 接口调用

- **🔧 工具函数和类型**

  - 新增 `utils/` 工具函数模块
  - 新增 `types/` 类型定义模块
  - 新增 `ComponentTypes` 常量定义
  - 新增 `ComponentUtils` 工具函数集

- **📚 完善文档系统**

  - 全新的 README 文档
  - 架构设计文档 (ARCHITECTURE.md)
  - 迁移指南 (MIGRATION-GUIDE.md)
  - API 文档分类 (`docs/api/`)
  - 使用示例 (`docs/examples/`)
  - 开发指南 (`docs/guides/`)

- **🧪 测试框架**
  - 新增 `__tests__/` 测试目录结构
  - 支持组件单元测试

#### 改进 (Changed)

- **📁 目录结构重构**

  - 移动 `yt-input-map.vue` 到 `composites/`
  - 移动 `yt-dict-select-with-other.vue` 到 `composites/`
  - 重新组织所有组件到逻辑分类目录

- **📦 导出方式优化**

  - 支持统一导入：`import { YtDynamicForm } from '@/components/ytWidget'`
  - 支持分类导入：`import { Inputs, Selectors } from '@/components/ytWidget'`
  - 支持异步导入：`const YtInput = Inputs.YtInput()`
  - 保持向后兼容的分类导出

- **🔄 类型定义优化**
  - 在 `FormItemProps` 中添加 `rootParentCode?: string` 属性
  - 完善所有组件的 TypeScript 类型定义
  - 统一类型导出路径

#### 修复 (Fixed)

- **🐛 RegionCascader 硬编码问题**

  - 修复硬编码 '222400' 根区域代码的问题
  - 支持通过配置动态指定根区域

- **🔧 导入路径优化**
  - 解决组件导入路径混乱的问题
  - 统一组件导入入口

### 📋 迁移指南

**重要**: 此版本包含破坏性变更，主要是导入路径的调整。请参考 [迁移指南](./MIGRATION-GUIDE.md) 进行升级。

**快速迁移**:

```typescript
// v1.x
import YtDynamicForm from '@/components/ytWidget/yt-dynamic-form.vue'

// v2.0
import { YtDynamicForm } from '@/components/ytWidget'
```

---

## [1.3.0] - 2025-06-20

### 新增 (Added)

- **YtDictSelectWithOther 组件**

  - 字典选择器 + "其他"选项组合组件
  - 支持 checkbox 和 radio 选择类型
  - 支持自定义"其他"输入框

- **性能优化**
  - YtDynamicForm 组件防抖优化
  - 使用 `watchEffect` 替代多个 `watch`
  - RAF + setTimeout 双重防抖机制

### 改进 (Changed)

- **YtDynamicForm 代码优化**
  - 使用 computed 缓存表单配置
  - 优化字段变化处理逻辑
  - 改进表单验证错误提示

### 修复 (Fixed)

- 修复大表单性能问题
- 修复字段联动时的响应延迟

---

## [1.2.0] - 2025-06-15

### 新增 (Added)

- **YtInputMap 组件**

  - 表格形式的输入组件
  - 支持动态添加/删除行
  - 支持列配置和验证

- **YtRegionCascader 组件**
  - 行政区域级联选择器
  - 支持异步数据加载
  - 基于 wot-design-uni 的 col-picker

### 改进 (Changed)

- 完善组件文档
- 优化组件导入方式

---

## [1.1.0] - 2025-06-10

### 新增 (Added)

- **YtUpload 组件增强**

  - 支持图片/视频/文件上传
  - 支持拖拽上传
  - 支持上传进度显示

- **YtCheckboxWithUpload 组件**
  - 复选框控制的上传组件
  - 支持条件显示上传区域

### 改进 (Changed)

- 优化上传组件的用户交互
- 改进错误处理和提示

---

## [1.0.0] - 2025-06-01

### 🎉 首次发布

#### 核心功能

- **YtDynamicForm** - 动态表单核心组件
- **基础输入组件**

  - YtInput - 文本输入框
  - YtTextarea - 多行文本域
  - YtInputNumber - 数字输入框

- **选择器组件**

  - YtSelect - 通用选择器
  - YtSelectSingle - 单选选择器
  - YtSelectMulti - 多选选择器
  - YtDictSelect - 字典选择器

- **日期时间组件**

  - YtCalendar - 日历组件
  - YtDatetimePicker - 日期时间选择器

- **文件上传**

  - YtUpload - 基础上传组件

- **地图组件**
  - YtMapSelector - 地图选择器

#### 核心特性

- 📱 多端适配 (H5、小程序、APP)
- 🎨 基于 wot-design-uni UI 库
- 💪 完整 TypeScript 支持
- 🔧 灵活配置系统
- ⚡ 高性能优化

---

## 📋 版本规划

### 即将发布

#### [2.1.0] - 计划中

- **文档完善**

  - 完整的 API 文档
  - 更多使用示例
  - 最佳实践指南

- **开发工具**
  - 组件开发脚手架
  - 自动化测试完善

#### [2.2.0] - 计划中

- **新组件**

  - YtTable - 表格组件
  - YtTree - 树形选择器
  - YtCascader - 通用级联选择器

- **功能增强**
  - 表单联动优化
  - 自定义校验规则
  - 国际化支持

#### [3.0.0] - 远期规划

- **架构升级**
  - 组合式 API 重构
  - 微前端支持
  - 插件系统

---

## 🤝 贡献指南

我们欢迎所有形式的贡献：

1. **Bug 报告** - 发现问题请提交 Issue
2. **功能建议** - 提出新功能需求
3. **代码贡献** - 提交 Pull Request
4. **文档改进** - 完善文档内容
5. **测试用例** - 添加测试覆盖

### 提交规范

```bash
# 功能开发
git commit -m "feat: 新增 XXX 组件"

# Bug 修复
git commit -m "fix: 修复 XXX 组件 YYY 问题"

# 文档更新
git commit -m "docs: 更新 XXX 文档"

# 性能优化
git commit -m "perf: 优化 XXX 组件性能"
```

---

## 📞 支持

- **文档**: [README.md](./README-NEW.md)
- **示例**: [docs/examples/](./docs/examples/)
- **API**: [docs/api/](./docs/api/)
- **迁移**: [MIGRATION-GUIDE.md](./MIGRATION-GUIDE.md)

---

**感谢所有贡献者的支持！🎉**
