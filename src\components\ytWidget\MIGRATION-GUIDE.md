# YtWidget v2.0 迁移指南

## 📋 概览

本指南将帮助您从 YtWidget v1.x 迁移到 v2.0 的新架构。v2.0 主要进行了目录结构重构，提升了组件的组织性和可维护性。

## 🔄 主要变更

### 1. 目录结构重构

```diff
# v1.x 结构
src/components/ytWidget/
├── yt-dynamic-form.vue
├── form-controls/
├── selection-components/
├── date-time-components/
├── upload-components/
├── complex-components/
├── yt-input-map.vue           # ❌ 位置不当
├── yt-dict-select-with-other.vue # ❌ 位置不当
└── docs/

# v2.0 结构
src/components/ytWidget/
├── 📦 core/                   # ✨ 新增核心系统目录
├── 🔤 inputs/                 # ♻️ 重命名 form-controls
├── 🎯 selectors/              # ♻️ 重命名 selection-components
│   ├── basic/                 # ✨ 新增基础选择器
│   └── advanced/              # ✨ 新增高级选择器
├── ⚡ special/                # ✨ 新增特殊功能目录
│   ├── datetime/              # ♻️ 移动日期时间组件
│   ├── upload/                # ♻️ 移动上传组件
│   └── map/                   # ♻️ 移动地图组件
├── 🧩 composites/             # ✨ 新增复合组件目录
├── 🛠️ utils/                  # ✨ 新增工具函数
├── 📝 types/                  # ✨ 新增类型定义
├── 📚 docs/                   # ♻️ 重构文档系统
└── 🧪 __tests__/              # ✨ 新增测试目录
```

### 2. 导入路径变更

```typescript
// ❌ v1.x 导入方式
import YtDynamicForm from '@/components/ytWidget/yt-dynamic-form.vue'
import YtInput from '@/components/ytWidget/form-controls/yt-input.vue'
import YtRegionCascader from '@/components/ytWidget/selection-components/yt-region-cascader.vue'

// ✅ v2.0 推荐导入方式
import { YtDynamicForm, YtInput, YtRegionCascader } from '@/components/ytWidget'

// ✅ v2.0 按需导入
import { Inputs, Selectors } from '@/components/ytWidget'
const YtInput = Inputs.YtInput
const YtRegionCascader = Selectors.Advanced.YtRegionCascader
```

### 3. 组件增强

```typescript
// ✨ RegionCascader 新增 rootParentCode 支持
{
  type: 'RegionCascader',
  field: 'region',
  title: '行政区域',
  props: {
    rootParentCode: '222401'  // 🆕 支持配置根区域代码
  }
}
```

## 🛠️ 迁移步骤

### 第一步：更新导入语句

#### 1.1 批量替换导入路径

使用编辑器的全局搜索替换功能：

```typescript
// 搜索模式
from '@/components/ytWidget/yt-dynamic-form.vue'
from '@/components/ytWidget/form-controls/'
from '@/components/ytWidget/selection-components/'
from '@/components/ytWidget/date-time-components/'
from '@/components/ytWidget/upload-components/'
from '@/components/ytWidget/complex-components/'

// 替换为
from '@/components/ytWidget'
```

#### 1.2 更新具体导入

```typescript
// ❌ 旧写法
import YtDynamicForm from '@/components/ytWidget/yt-dynamic-form.vue'
import YtInput from '@/components/ytWidget/form-controls/yt-input.vue'
import YtTextarea from '@/components/ytWidget/form-controls/yt-textarea.vue'
import YtSelectSingle from '@/components/ytWidget/selection-components/yt-select-single.vue'
import YtRegionCascader from '@/components/ytWidget/selection-components/yt-region-cascader.vue'

// ✅ 新写法
import {
  YtDynamicForm,
  YtInput,
  YtTextarea,
  YtSelectSingle,
  YtRegionCascader,
} from '@/components/ytWidget'
```

### 第二步：更新类型导入

```typescript
// ❌ 旧写法
import type { FormItem } from '@/types/form'

// ✅ 新写法
import type { FormItem, FormItemProps, FormConfig } from '@/components/ytWidget/types'
```

### 第三步：更新组件配置（可选）

如果使用 RegionCascader 组件，可以利用新的 rootParentCode 配置：

```typescript
// ✅ 新增功能
const formConfig = [
  {
    type: 'RegionCascader',
    field: 'region',
    title: '行政区域',
    props: {
      rootParentCode: '222401', // 🆕 配置根区域代码
      placeholder: '请选择地区',
    },
  },
]
```

## 🔧 自动化迁移脚本

### 创建迁移脚本

创建 `scripts/migrate-v2.js` 文件：

```javascript
const fs = require('fs')
const path = require('path')
const glob = require('glob')

// 迁移配置
const MIGRATION_MAP = {
  // 组件导入映射
  "from '@/components/ytWidget/yt-dynamic-form.vue'": "from '@/components/ytWidget'",
  "from '@/components/ytWidget/form-controls/": "from '@/components/ytWidget'",
  "from '@/components/ytWidget/selection-components/": "from '@/components/ytWidget'",
  "from '@/components/ytWidget/date-time-components/": "from '@/components/ytWidget'",
  "from '@/components/ytWidget/upload-components/": "from '@/components/ytWidget'",
  "from '@/components/ytWidget/complex-components/": "from '@/components/ytWidget'",

  // 类型导入映射
  "from '@/types/form'": "from '@/components/ytWidget/types'",
}

function migrateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let hasChanges = false

  Object.entries(MIGRATION_MAP).forEach(([oldPattern, newPattern]) => {
    const regex = new RegExp(oldPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
    if (regex.test(content)) {
      content = content.replace(regex, newPattern)
      hasChanges = true
    }
  })

  if (hasChanges) {
    fs.writeFileSync(filePath, content)
    console.log(`✅ 已迁移: ${filePath}`)
  }
}

// 执行迁移
const files = glob.sync('src/**/*.{vue,ts,js}')
files.forEach(migrateFile)
console.log('🎉 迁移完成!')
```

### 运行迁移脚本

```bash
node scripts/migrate-v2.js
```

## ⚠️ 注意事项

### 1. 破坏性变更

- **导入路径变更**: 需要更新所有导入语句
- **目录结构调整**: 直接路径导入将失效
- **类型导入位置**: 类型定义移动到新位置

### 2. 向后兼容性

- **组件API**: 所有组件的 API 保持不变
- **功能特性**: 现有功能完全兼容
- **配置格式**: 表单配置格式无变化

### 3. 新功能

- **RegionCascader**: 新增 `rootParentCode` 配置
- **按需导入**: 支持更细粒度的导入控制
- **工具函数**: 新增组件工具函数

## 🧪 验证迁移

### 1. 编译检查

```bash
# 检查 TypeScript 编译
npm run type-check

# 检查构建
npm run build
```

### 2. 功能测试

```typescript
// 测试基础功能
const testConfig = [
  { type: 'input', field: 'name', title: '姓名' },
  { type: 'RegionCascader', field: 'region', title: '地区' },
]

// 确保表单正常渲染和提交
```

### 3. 性能对比

```bash
# 对比构建产物大小
npm run build:analyze
```

## 🆘 常见问题

### Q1: 导入组件时出现 "Cannot find module" 错误

**A**: 检查导入路径是否正确更新：

```typescript
// ❌ 错误
import YtInput from '@/components/ytWidget/form-controls/yt-input.vue'

// ✅ 正确
import { YtInput } from '@/components/ytWidget'
```

### Q2: TypeScript 类型错误

**A**: 更新类型导入路径：

```typescript
// ❌ 错误
import type { FormItem } from '@/types/form'

// ✅ 正确
import type { FormItem } from '@/components/ytWidget/types'
```

### Q3: RegionCascader 组件配置问题

**A**: 新版本支持 rootParentCode 配置：

```typescript
// ✅ 新功能
{
  type: 'RegionCascader',
  props: {
    rootParentCode: '222401'  // 指定根区域代码
  }
}
```

### Q4: 构建后组件无法正常工作

**A**: 检查是否有遗漏的导入更新，确保所有相关文件都已迁移。

## 📞 获取帮助

如果在迁移过程中遇到问题：

1. 查看 [完整文档](./README-NEW.md)
2. 检查 [API文档](./docs/api/)
3. 查看 [示例代码](./docs/examples/)
4. 提交 Issue 到项目仓库

---

**迁移愉快！🚀**
