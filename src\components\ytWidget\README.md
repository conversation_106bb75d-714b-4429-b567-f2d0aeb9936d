# YtWidget 动态表单组件库 v2.0

> 基于 Vue3 + TypeScript + wot-design-uni 的企业级动态表单解决方案

## ✨ 特性

- 🚀 **开箱即用** - 配置即可生成复杂表单
- 🎨 **组件丰富** - 20+ 专业表单组件
- 📱 **多端适配** - 支持 H5、小程序、APP
- 🔧 **高度可配置** - 灵活的属性配置系统
- 💪 **TypeScript** - 完整的类型定义
- 📚 **文档完善** - 详细的API文档和示例
- ⚡ **性能优化** - 防抖、缓存、异步加载
- 🧪 **测试覆盖** - 完整的单元测试

## 🚀 快速开始

### 安装依赖

```bash
# 确保已安装 wot-design-uni
npm install wot-design-uni
```

### 基础使用

```vue
<template>
  <yt-dynamic-form v-model="formData" :config="formConfig" @submit="handleSubmit" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { YtDynamicForm } from '@/components/ytWidget'
import type { FormItem } from '@/components/ytWidget/types'

const formData = ref({})
const formConfig = ref<FormItem[]>([
  {
    type: 'input',
    field: 'name',
    title: '姓名',
    required: true,
  },
  {
    type: 'RegionCascader',
    field: 'region',
    title: '所在地区',
    props: {
      rootParentCode: '222401',
    },
  },
])

const handleSubmit = (data: any) => {
  console.log('表单数据:', data)
}
</script>
```

## 📦 组件架构

### 🏗️ 分层设计

```
YtWidget 组件库
├── 🎯 Core 核心层      - 动态表单引擎
├── 🔤 Inputs 输入层    - 基础输入控件
├── 🎯 Selectors 选择层 - 选择器组件
├── ⚡ Special 功能层   - 特殊功能组件
└── 🧩 Composites 业务层 - 复合业务组件
```

### 📋 组件分类

| 分类         | 组件                                     | 描述                 |
| ------------ | ---------------------------------------- | -------------------- |
| **核心系统** | YtDynamicForm                            | 动态表单核心引擎     |
| **基础输入** | YtInput, YtTextarea, YtInputNumber       | 文本、数字输入       |
| **选择器**   | YtSelect, YtRegionCascader, YtDictSelect | 单选、多选、级联选择 |
| **日期时间** | YtCalendar, YtDatetimePicker             | 日期时间选择         |
| **文件上传** | YtUpload, YtCheckboxWithUpload           | 文件上传处理         |
| **地图定位** | YtMapSelector                            | 地图位置选择         |
| **复合组件** | YtInputMap, YtDictSelectWithOther        | 业务组合组件         |

## 🎯 核心组件

### YtDynamicForm - 动态表单核心

```typescript
interface Props {
  config: FormItem[] // 表单配置
  modelValue?: Record<string, any> // 表单数据
  showSubmitButton?: boolean // 显示提交按钮
  submitButtonText?: string // 提交按钮文字
  submitLoading?: boolean // 提交加载状态
}

interface Events {
  'update:modelValue': [value: Record<string, any>]
  submit: [data: Record<string, any>]
  uploadSuccess: [event: any]
  validate: [result: { valid: boolean; errors: string[] }]
}
```

### 表单配置 FormItem

```typescript
interface FormItem {
  type: ComponentType // 组件类型
  field: string // 字段名
  title: string // 标题
  info?: string // 提示信息
  required?: boolean | string // 必填验证
  props?: FormItemProps // 组件属性
  display?: boolean // 是否显示
  hidden?: boolean // 是否隐藏
}
```

## 🎨 组件示例

### 基础输入组件

```typescript
// 文本输入
{
  type: 'input',
  field: 'username',
  title: '用户名',
  required: '请输入用户名',
  props: {
    placeholder: '请输入用户名',
    maxlength: 50
  }
}

// 数字输入
{
  type: 'inputNumber',
  field: 'age',
  title: '年龄',
  props: {
    min: 0,
    max: 150,
    precision: 0
  }
}
```

### 选择器组件

```typescript
// 单选选择器
{
  type: 'select',
  field: 'gender',
  title: '性别',
  options: [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' }
  ]
}

// 行政区域级联选择器
{
  type: 'RegionCascader',
  field: 'region',
  title: '所在地区',
  props: {
    rootParentCode: '222401'  // 支持配置根区域
  }
}

// 字典选择器
{
  type: 'dict-select',
  field: 'category',
  title: '分类',
  props: {
    dictType: 'problem_category',
    multiple: false
  }
}
```

### 日期时间组件

```typescript
// 日期选择器
{
  type: 'calendar',
  field: 'birthday',
  title: '出生日期',
  props: {
    calendarType: 'date',
    maxDate: Date.now()
  }
}

// 日期时间选择器
{
  type: 'datetime-picker',
  field: 'appointmentTime',
  title: '预约时间',
  props: {
    pickerType: 'datetime',
    minDate: Date.now()
  }
}
```

### 文件上传组件

```typescript
// 基础上传
{
  type: 'upload',
  field: 'photos',
  title: '照片',
  props: {
    accept: 'image',
    limit: 5,
    maxSize: 10
  }
}

// 复选框+上传组合
{
  type: 'DynamicCheckboxWithUpload',
  field: 'evidence',
  title: '证据材料',
  props: {
    checkboxLabel: '有证据材料',
    uploadProps: {
      accept: 'image',
      limit: 3
    }
  }
}
```

### 地图组件

```typescript
// 地图选择器
{
  type: 'MapSelector',
  field: 'location',
  title: '位置选择',
  props: {
    mapType: 'point',
    defaultLocation: {
      latitude: 39.908,
      longitude: 116.397
    }
  }
}
```

## 🔧 高级用法

### 动态表单配置

```typescript
const formConfig = computed(() => {
  const baseConfig = [{ type: 'input', field: 'name', title: '姓名' }]

  // 根据条件动态添加字段
  if (showAdvanced.value) {
    baseConfig.push({
      type: 'RegionCascader',
      field: 'region',
      title: '地区',
    })
  }

  return baseConfig
})
```

### 表单验证

```typescript
// 内置验证
{
  type: 'input',
  field: 'email',
  title: '邮箱',
  required: '请输入邮箱',
  props: {
    rules: [
      { required: true, message: '邮箱不能为空' },
      { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }
    ]
  }
}

// 自定义验证
const validateForm = () => {
  const result = formRef.value.validateForm()
  if (result.valid) {
    // 验证通过
  } else {
    // 显示错误信息
    console.log(result.errors)
  }
}
```

### 事件处理

```typescript
// 字段变化监听
const handleFieldChange = (field: string, value: any) => {
  console.log(`字段 ${field} 变化:`, value)

  // 联动逻辑
  if (field === 'category') {
    updateSubCategory(value)
  }
}

// 表单提交
const handleSubmit = async (data: Record<string, any>) => {
  try {
    await submitForm(data)
    uni.showToast({ title: '提交成功' })
  } catch (error) {
    uni.showToast({ title: '提交失败', icon: 'error' })
  }
}
```

## 📚 API 文档

### 导入方式

```typescript
// 完整导入
import { YtDynamicForm } from '@/components/ytWidget'

// 按需导入
import { FormControls, SelectionComponents } from '@/components/ytWidget'

// 分类导入
import YtInput from '@/components/ytWidget/inputs/yt-input.vue'
import YtRegionCascader from '@/components/ytWidget/selectors/advanced/yt-region-cascader.vue'
```

### 类型定义

```typescript
// 导入类型
import type { FormItem, FormItemProps, FormConfig } from '@/components/ytWidget/types'
```

## 🎯 最佳实践

### 1. 表单配置管理

```typescript
// 推荐：使用配置文件管理复杂表单
// config/forms/user-profile.ts
export const userProfileConfig: FormItem[] = [
  {
    type: 'input',
    field: 'name',
    title: '姓名',
    required: true,
  },
  // ... 更多配置
]
```

### 2. 组件复用

```typescript
// 推荐：创建业务组件封装常用配置
// components/business/UserForm.vue
<template>
  <yt-dynamic-form :config="userFormConfig" v-model="userData" />
</template>
```

### 3. 性能优化

```typescript
// 推荐：使用 computed 缓存配置
const formConfig = computed(() => {
  return generateFormConfig(props.type)
})

// 推荐：大表单使用分页或分步骤
const currentStepConfig = computed(() => {
  return fullConfig.value.slice(stepIndex * 10, (stepIndex + 1) * 10)
})
```

## 🔄 迁移指南

### 从 v1.x 迁移到 v2.0

```typescript
// v1.x 写法
import YtDynamicForm from '@/components/ytWidget/yt-dynamic-form.vue'

// v2.0 写法
import { YtDynamicForm } from '@/components/ytWidget'
// 或
import YtDynamicForm from '@/components/ytWidget/core/yt-dynamic-form.vue'
```

详细迁移指南请查看 [迁移文档](./docs/migration/v1-to-v2.md)

## 🧪 测试

```bash
# 运行测试
npm run test

# 运行特定组件测试
npm run test -- --match "YtDynamicForm"

# 测试覆盖率
npm run test:coverage
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支: `git checkout -b feature/new-component`
3. 提交更改: `git commit -m 'Add new component'`
4. 推送分支: `git push origin feature/new-component`
5. 提交 Pull Request

详细贡献指南请查看 [CONTRIBUTING.md](./CONTRIBUTING.md)

## 📝 更新日志

### v2.0.0 (2025-06-26)

- 🎉 重构组件库架构
- ✨ 新增 RegionCascader 组件支持配置根区域
- 📚 完善文档系统
- 🔧 优化类型定义
- ⚡ 性能优化

详细更新日志请查看 [CHANGELOG.md](./CHANGELOG.md)

## 📄 许可证

MIT License

---

**Made with ❤️ by YueTong Team**
