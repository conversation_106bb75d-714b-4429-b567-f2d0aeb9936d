// {{CHENGQI:
// Action: Created; Timestamp: 2025-06-27 17:07:04 +08:00; Reason: 创建表单初始化组合式API，提供便捷的初始化方法; Principle_Applied: Vue3组合式API最佳实践, 可复用性设计;
// }}

import { ref, computed, watchEffect } from 'vue'
import { ComponentUtils } from '../index'
import type { FormItem } from '@/types/form'

/**
 * 表单初始化组合式API
 * 提供便捷的表单数据初始化、重置和验证功能
 */
export function useFormInitializer(config: FormItem[], initialValues?: Record<string, any>) {
  // 响应式表单数据
  const formData = ref<Record<string, any>>({})

  // 初始化数据缓存（用于重置功能）
  const initialData = ref<Record<string, any>>({})

  // 空值数据缓存
  const emptyData = ref<Record<string, any>>({})

  /**
   * 初始化表单数据
   */
  const initializeForm = (userValues?: Record<string, any>) => {
    const userModel = userValues || initialValues || {}

    // 使用ComponentUtils进行初始化
    const initialized = ComponentUtils.initializeFormData(config, userModel)

    formData.value = { ...initialized }
    initialData.value = { ...initialized }

    console.log('🎯 表单初始化完成:', {
      config: config.map((item) => ({ field: item.field, type: item.type })),
      userModel,
      result: formData.value,
    })
  }

  /**
   * 重置表单到空值状态
   */
  const resetToEmpty = () => {
    const empty = ComponentUtils.resetToEmpty(config)
    formData.value = { ...empty }
    emptyData.value = { ...empty }

    console.log('🔄 表单重置为空值:', formData.value)
  }

  /**
   * 重置表单到初始值状态
   */
  const resetToInitial = () => {
    formData.value = { ...initialData.value }
    console.log('🔄 表单重置为初始值:', formData.value)
  }

  /**
   * 更新单个字段值（带类型安全检查）
   */
  const updateField = (field: string, value: any) => {
    const fieldConfig = config.find((item) => item.field === field)
    if (!fieldConfig) {
      console.warn(`⚠️ 字段 [${field}] 不存在于配置中`)
      return
    }

    // 使用优先级合并确保值的正确性
    const safeValue = ComponentUtils.mergeWithDefaults(fieldConfig.type, value)
    formData.value[field] = safeValue

    console.log(`📝 字段更新 [${field}]:`, {
      type: fieldConfig.type,
      inputValue: value,
      safeValue,
    })
  }

  /**
   * 批量更新字段值
   */
  const updateFields = (updates: Record<string, any>) => {
    Object.entries(updates).forEach(([field, value]) => {
      updateField(field, value)
    })
  }

  /**
   * 检查表单是否为空
   */
  const isEmpty = computed(() => {
    return config.every((item) => {
      if (!item.field) return true
      const value = formData.value[item.field]
      return ComponentUtils.isEmpty(item.type, value)
    })
  })

  /**
   * 检查表单是否有变化（相对于初始值）
   */
  const hasChanges = computed(() => {
    return config.some((item) => {
      if (!item.field) return false
      const currentValue = formData.value[item.field]
      const initialValue = initialData.value[item.field]
      return JSON.stringify(currentValue) !== JSON.stringify(initialValue)
    })
  })

  /**
   * 获取变化的字段
   */
  const getChangedFields = computed(() => {
    const changes: Record<string, { from: any; to: any }> = {}

    config.forEach((item) => {
      if (!item.field) return

      const currentValue = formData.value[item.field]
      const initialValue = initialData.value[item.field]

      if (JSON.stringify(currentValue) !== JSON.stringify(initialValue)) {
        changes[item.field] = {
          from: initialValue,
          to: currentValue,
        }
      }
    })

    return changes
  })

  /**
   * 验证所有字段的类型
   */
  const validateTypes = () => {
    const errors: Record<string, string> = {}

    config.forEach((item) => {
      if (!item.field) return

      const value = formData.value[item.field]
      const isValid = ComponentUtils.validateValueType(item.type, value)

      if (!isValid) {
        errors[item.field] = `字段 [${item.field}] 的值类型不匹配组件类型 [${item.type}]`
      }
    })

    return {
      valid: Object.keys(errors).length === 0,
      errors,
    }
  }

  /**
   * 获取表单数据快照
   */
  const getSnapshot = () => {
    return {
      timestamp: Date.now(),
      data: { ...formData.value },
      config: config.map((item) => ({ field: item.field, type: item.type })),
    }
  }

  // 自动初始化
  watchEffect(() => {
    if (config.length > 0) {
      initializeForm()
    }
  })

  return {
    // 响应式数据
    formData,
    isEmpty,
    hasChanges,
    getChangedFields,

    // 初始化方法
    initializeForm,
    resetToEmpty,
    resetToInitial,

    // 字段操作
    updateField,
    updateFields,

    // 验证方法
    validateTypes,

    // 工具方法
    getSnapshot,

    // 访问工具函数
    utils: ComponentUtils,
  }
}

// 默认导出
export default useFormInitializer

/**
 * 便捷的创建表单初始化器
 */
export const createFormInitializer = (config: FormItem[], initialValues?: Record<string, any>) => {
  return useFormInitializer(config, initialValues)
}

/**
 * 表单快照类型
 */
export interface FormSnapshot {
  timestamp: number
  data: Record<string, any>
  config: Array<{ field?: string; type: string }>
}

/**
 * 验证结果类型
 */
export interface ValidationResult {
  valid: boolean
  errors: Record<string, string>
}
