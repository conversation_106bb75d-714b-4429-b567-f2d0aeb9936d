<script lang="ts" setup>
import { computed, defineEmits, defineProps, onMounted, ref, watch } from 'vue'
import type { FormItem } from '@/types/form'
import { useDictStore } from '@/store/dict'

interface DictSelectValue {
  otherText: string
  selectType: 'checkbox' | 'radio'
  selected: string[]
}

interface Props {
  item: FormItem
  modelValue?: DictSelectValue
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: DictSelectValue]
}>()

// 使用字典store
const dictStore = useDictStore()

// 字典数据
const dictOptions = ref<Array<{ label: string; value: string }>>([])
const isLoading = ref(false)

// 当前值
const currentValue = computed({
  get: () => {
    return (
      props.modelValue || {
        otherText: '',
        selectType: props.item.props?.selectType || 'checkbox',
        selected: [],
      }
    )
  },
  set: (value: DictSelectValue) => {
    emits('update:modelValue', value)
  },
})

// 复选框组的选中值（直接绑定到wd-checkbox-group）
const checkboxGroupValue = computed({
  get: () => currentValue.value.selected,
  set: (newSelected: string[]) => {
    // 处理取消选择"其他"的情况
    const wasOtherSelected = currentValue.value.selected.includes('99')
    const isOtherSelected = newSelected.includes('99')

    let newOtherText = currentValue.value.otherText

    // 如果取消选择"其他"且配置为不保留文本，则清空其他文本
    if (wasOtherSelected && !isOtherSelected && !props.item.props?.keepOtherTextOnUnselect) {
      newOtherText = ''
    }

    currentValue.value = {
      ...currentValue.value,
      selected: newSelected,
      otherText: newOtherText,
    }

    console.log('📝 复选框组选择变化:', newSelected, currentValue.value)
  },
})

// 是否显示其他输入框
const showOtherInput = computed(() => {
  return currentValue.value.selected.includes('99')
})

// 计算是否必填
const isRequired = computed(() => {
  return !!props.item.$required
})

// 计算错误提示信息
const getErrorMessage = () => {
  if (typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
}

// 处理其他文本输入
const handleOtherTextChange = (text: string) => {
  currentValue.value = {
    ...currentValue.value,
    otherText: text,
  }

  console.log('📝 其他文本输入:', text)
}

// 加载字典数据
const loadDictData = async () => {
  if (!props.item.props?.dictType) {
    console.warn('未配置字典类型:', props.item.field)
    return
  }

  try {
    isLoading.value = true
    console.log('🔍 加载字典数据:', props.item.props.dictType)

    // 确保字典store已初始化
    if (!dictStore.dictInfo.isSetDict) {
      console.log('🔄 字典store未初始化，开始初始化...')
      await dictStore.setDictMap()
    }

    // 等待一下确保数据完全加载
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 从store获取字典数据 - 注意：这里实际返回的是 dictMap[type]，而dictMap虽然声明为Map，但实际使用的是对象方式
    let response = dictStore.getDictByType(props.item.props.dictType)
    console.log('📋 获取字典数据 - 字典类型:', props.item.props.dictType)
    console.log('📋 获取字典数据 - 原始响应:', response)
    console.log('📋 获取字典数据 - 响应类型:', typeof response)
    console.log('📋 获取字典数据 - 是否数组:', Array.isArray(response))

    // 如果还是没有数据，再次尝试加载
    if (!response) {
      console.log('🔄 数据为空，重新加载store...')
      await dictStore.setDictMap()
      response = dictStore.getDictByType(props.item.props.dictType)
      console.log('📋 重新加载后的字典数据:', response)
    }

    if (response && Array.isArray(response) && response.length > 0) {
      dictOptions.value = response.map((item) => ({
        label: item.label || String(item.value),
        value: String(item.value),
      }))

      console.log('✅ 字典数据处理完成:', dictOptions.value)
    } else {
      console.warn('⚠️ 字典数据为空或格式错误')
      console.log('⚠️ 字典类型:', props.item.props.dictType)
      console.log('⚠️ 原始响应:', response)
      console.log('⚠️ Store状态:', dictStore.dictInfo.isSetDict)
      console.log('⚠️ 全部字典数据:', dictStore.dictInfo.dictMap)

      dictOptions.value = []

      // 显示用户友好的提示
      uni.showToast({
        title: `字典类型 ${props.item.props.dictType} 暂无数据`,
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('❌ 字典数据加载失败:', error)
    uni.showToast({
      title: '数据加载失败',
      icon: 'error',
    })
    dictOptions.value = []
  } finally {
    isLoading.value = false
  }
}

// 处理复选框组变化事件
const handleCheckboxGroupChange = (event: { value: string[] }) => {
  console.log('📝 Checkbox组变化事件:', event)
  // 通过computed setter自动处理
}

// 组件挂载时加载字典数据
onMounted(() => {
  loadDictData()
})

// 监听字典类型变化
watch(
  () => props.item.props?.dictType,
  (newDictType) => {
    if (newDictType) {
      loadDictData()
    }
  },
)

// 监听数据变化
watch(
  () => currentValue.value,
  (newValue) => {
    console.log('📊 DictSelectWithOther数据变化:', newValue)
  },
  { deep: true },
)
</script>

<template>
  <wd-cell :title="item.title" vertical>
    <!-- 加载状态 -->
    <wd-cell v-if="isLoading" title="加载中...">
      <wd-loading />
    </wd-cell>
    <!-- 数据加载完成后的内容 -->
    <template v-else>
      <!-- 复选框组 -->
      <template v-if="dictOptions.length > 0">
        <wd-checkbox-group
          v-model="checkboxGroupValue"
          :max="0"
          :min="0"
          custom-class="yt-checkbox-group"
          shape="square"
          @change="handleCheckboxGroupChange"
        >
          <wd-checkbox
            v-for="option in dictOptions"
            :key="option.value"
            :model-value="option.value"
          >
            {{ option.label }}
          </wd-checkbox>
        </wd-checkbox-group>
        <!-- 其他选项的输入框 -->
        <wd-input
          v-if="showOtherInput"
          :maxlength="200"
          :model-value="currentValue.otherText"
          :placeholder="item.props?.otherInputPlaceholder || '请输入具体内容'"
          custom-input-class="other-input"
          show-count
          @update:model-value="handleOtherTextChange"
        />
      </template>

      <!-- 无数据提示 -->
      <wd-cell v-else title="暂无选项数据" />
    </template>

    <!-- 必填提示 -->
    <wd-cell v-if="isRequired && getErrorMessage()" custom-class="error-cell">
      <wd-notice-bar :scrollable="false" :text="getErrorMessage()" wrapable />
    </wd-cell>

    <!-- 信息提示 -->
    <wd-cell v-if="item.info" custom-class="info-cell">
      <wd-notice-bar
        :scrollable="false"
        :text="item.info"
        prefix="warn-bold"
        type="info"
        wrapable
      />
    </wd-cell>
  </wd-cell>
</template>

<style lang="scss" scoped>
.other-input {
  width: 100%;

  :deep(.wd-input__inner) {
    padding: 8rpx 0;
    border: none;
  }
}

.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  align-items: center;
}

:deep(.selected-tag) {
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

:deep(.error-cell) {
  background: #fef2f2;

  .wd-notice-bar {
    background: transparent;
  }
}

:deep(.info-cell) {
  background: #f0f9ff;

  .wd-notice-bar {
    background: transparent;
  }
}

:deep(.result-cell) {
  background: #f8fafc;

  .wd-cell__title {
    font-weight: 500;
    color: #475569;
  }
}

// checkbox 组样式优化
:deep(.wd-checkbox-group) {
  padding: 0 16px;

  .wd-checkbox {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;

    &:last-child {
      border-bottom: none;
    }

    .wd-checkbox__shape {
      flex-shrink: 0;
      margin-right: 12px;
      border-color: #d1d5db;

      &.is-checked {
        background-color: #3b82f6;
        border-color: #3b82f6;
      }
    }

    .wd-checkbox__label {
      display: flex;
      flex: 1;
      align-items: center;
      font-size: 14px;
      line-height: 1.4;
      color: #374151;
    }
  }
}

// cell 点击效果
:deep(.wd-cell) {
  transition: background-color 0.2s ease;

  &:active {
    background-color: #f1f5f9;
  }
}
</style>
