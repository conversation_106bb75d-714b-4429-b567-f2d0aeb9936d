<template>
  <view class="yt-table-form">
    <!-- 主显示单元格 -->
    <wd-cell :title="item.title" :value="summaryText" is-link @click="openFormDrawer">
      <template #value>
        <view class="status-info">
          <text class="fill-status">{{ fillStatusText }}</text>
          <wd-icon name="arrow-right" size="16px" />
        </view>
      </template>
    </wd-cell>

    <!-- 右侧抽屉弹出层 -->
    <wd-popup
      v-model="showDrawer"
      :safe-area-inset-bottom="true"
      custom-class="table-form-drawer"
      custom-style=" width:100vw;height:96vh"
      position="bottom"
    >
      <view class="drawer-container">
        <!-- 头部 -->
        <view class="drawer-header">
          <wd-button custom-class="back-btn" type="text" @click="closeDrawer">
            <wd-icon name="arrow-left" size="18px" />
            返回
          </wd-button>
          <text class="drawer-title">{{ item.title }}</text>
          <wd-button
            :disabled="tableData.length >= maxRows"
            custom-class="add-btn"
            type="text"
            @click="addNewRecord"
          >
            <wd-icon name="add" size="18px" />
            添加
          </wd-button>
        </view>

        <!-- 内容区域 -->
        <scroll-view class="drawer-content" scroll-y>
          <!-- 记录列表 -->
          <template v-for="(record, recordIndex) in tableData" :key="`record-${recordIndex}`">
            <view class="record-section">
              <!-- 记录头部 -->
              <view class="record-header">
                <text class="record-title">第{{ recordIndex + 1 }}条记录</text>
                <view class="record-actions">
                  <wd-button
                    :disabled="tableData.length <= minRows"
                    custom-class="delete-btn"
                    size="small"
                    type="text"
                    @click="deleteRecord(recordIndex)"
                  >
                    <wd-icon name="delete" size="16px" />
                    删除
                  </wd-button>
                </view>
              </view>

              <!-- 表单字段 -->
              <wd-cell-group
                v-for="column in columns"
                :key="`${recordIndex}-${column.label}`"
                border
                custom-class="custom-cell-group"
                use-slot
              >
                <!-- 自定义标题插槽 -->
                <template #title>
                  <view class="custom-group-title">
                    <text class="title-text">{{ column.label }}</text>
                  </view>
                </template>

                <template
                  v-for="(fieldRule, fieldIndex) in column.rule"
                  :key="`${recordIndex}-${column.label}-${fieldIndex}`"
                >
                  <yt-form-item
                    :config="fieldRule"
                    :model-value="getFieldValue(recordIndex, fieldRule.field)"
                    @update:model-value="updateFieldValue(recordIndex, fieldRule.field, $event)"
                  />
                </template>
              </wd-cell-group>
            </view>
          </template>

          <!-- 空状态 -->
          <view v-if="tableData.length === 0" class="empty-state">
            <wd-icon name="inbox" size="48px" />
            <text class="empty-text">暂无数据</text>
            <wd-button type="primary" @click="addNewRecord">添加第一条记录</wd-button>
          </view>

          <!-- 底部提示 -->
          <view v-if="item.info" class="info-section">
            <wd-notice-bar
              :scrollable="false"
              :text="item.info"
              prefix="warn-bold"
              type="info"
              wrapable
            />
          </view>
        </scroll-view>

        <!-- 底部操作栏 -->
        <view class="drawer-footer">
          <wd-button block type="primary" @click="saveAndClose">保存并返回</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import type { FormItem, TableColumn } from '@/types/form'
import YtFormItem from '../core/yt-form-item.vue'
// 导入组件工具类和类型常量
import { ComponentUtils } from '../index'

// 组件属性定义
interface Props {
  /** 表单项配置 */
  item: FormItem
  /** 表格数据，数组格式 */
  modelValue?: Record<string, any>[]
  /** 最小行数 */
  minRows?: number
  /** 最大行数 */
  maxRows?: number
}

const props = withDefaults(defineProps<Props>(), {
  minRows: 1,
  maxRows: 99,
})

// 事件定义
const emits = defineEmits<{
  'update:modelValue': [value: Record<string, any>[]]
}>()

// 响应式数据
const showDrawer = ref(false)

// 表格数据计算属性
const tableData = computed({
  get: () => {
    const data = props.modelValue
    // 确保返回数组格式
    if (!data || !Array.isArray(data)) {
      return []
    }
    return data
  },
  set: (value: Record<string, any>[]) => {
    emits('update:modelValue', value)
  },
})

// 表格列配置
const columns = computed(() => {
  return (props.item.props?.columns as TableColumn[]) || []
})

// 填写状态文本
const fillStatusText = computed(() => {
  const total = tableData.value.length
  if (total === 0) {
    return '未填写'
  }

  // 计算已填写的记录数
  const filledCount = tableData.value.filter((record) => {
    return columns.value.some((column) =>
      column.rule?.some((fieldRule) => {
        const value = record[fieldRule.field]
        return !ComponentUtils.isEmpty(fieldRule.type, value)
      }),
    )
  }).length

  return `${filledCount}/${total} 项已填`
})

// 摘要文本
const summaryText = computed(() => {
  if (tableData.value.length === 0) {
    return '点击添加记录'
  }
  return `共${tableData.value.length}条记录`
})

// 创建空行数据 - 重构版本，使用 ComponentUtils
const createEmptyRecord = (): Record<string, any> => {
  const record: Record<string, any> = {}

  columns.value.forEach((column) => {
    if (column.rule && Array.isArray(column.rule)) {
      column.rule.forEach((fieldRule: FormItem) => {
        // 🚀 使用 ComponentUtils 获取更准确的初始值
        record[fieldRule.field] = getFieldInitialValue(fieldRule)
      })
    }
  })

  console.log('📋 创建空记录:', record)
  return record
}

// 🚀 新增：获取字段初始值 - 优先级处理
const getFieldInitialValue = (fieldRule: FormItem): any => {
  // 1. 优先使用字段配置中的 defaultValue
  if (fieldRule.props?.defaultValue !== undefined) {
    const userValue = fieldRule.props.defaultValue
    console.log(`📋 使用字段默认值 [${fieldRule.field}]:`, userValue)
    return ComponentUtils.mergeWithDefaults(fieldRule.type, userValue)
  }

  // 2. 使用 ComponentUtils 获取类型安全的空值
  const emptyValue = ComponentUtils.getEmptyValue(fieldRule.type)
  console.log(`📋 使用类型空值 [${fieldRule.field}] (${fieldRule.type}):`, emptyValue)

  return emptyValue
}

// 🚀 保留原有 getDefaultValue 方法作为向后兼容（标记为废弃）
const getDefaultValue = (fieldRule: FormItem): any => {
  console.warn('[getDefaultValue] 此方法已废弃，请使用 getFieldInitialValue')
  return getFieldInitialValue(fieldRule)
}

// 获取字段值 - 改进版本
const getFieldValue = (recordIndex: number, field: string): any => {
  const record = tableData.value[recordIndex]
  if (record && record[field] !== undefined) {
    return record[field]
  }

  // 如果字段不存在，尝试从配置中获取默认值
  const fieldRule = findFieldRule(field)
  return fieldRule ? getFieldInitialValue(fieldRule) : ''
}

// 🚀 新增：查找字段规则配置
const findFieldRule = (field: string): FormItem | null => {
  for (const column of columns.value) {
    if (column.rule) {
      const foundRule = column.rule.find((rule: FormItem) => rule.field === field)
      if (foundRule) return foundRule
    }
  }
  return null
}

// 更新字段值
const updateFieldValue = (recordIndex: number, field: string, newValue: any) => {
  const newData = [...tableData.value]

  // 确保记录存在
  while (newData.length <= recordIndex) {
    newData.push(createEmptyRecord())
  }

  // 🚀 类型验证和值处理
  const fieldRule = findFieldRule(field)
  if (fieldRule) {
    // 验证值类型是否正确
    if (!ComponentUtils.validateValueType(fieldRule.type, newValue)) {
      console.warn(`⚠️ 字段 [${field}] 值类型不正确:`, {
        expectedType: fieldRule.type,
        actualValue: newValue,
        actualType: typeof newValue,
      })

      // 尝试使用 mergeWithDefaults 进行类型转换
      newValue = ComponentUtils.mergeWithDefaults(fieldRule.type, newValue)
    }
  }

  // 更新字段值
  newData[recordIndex] = { ...newData[recordIndex], [field]: newValue }
  tableData.value = newData

  console.log(`📝 表格字段更新: 记录${recordIndex}, 字段${field}:`, {
    newValue,
    fieldType: fieldRule?.type,
    isEmpty: fieldRule ? ComponentUtils.isEmpty(fieldRule.type, newValue) : 'unknown',
  })
}

// 添加新记录
const addNewRecord = () => {
  if (tableData.value.length >= props.maxRows) {
    uni.showToast({
      title: `最多只能添加${props.maxRows}条记录`,
      icon: 'none',
    })
    return
  }

  const newRecord = createEmptyRecord()
  const newData = [...tableData.value, newRecord]
  tableData.value = newData

  console.log(`➕ 添加新记录[${newData.length - 1}]:`, newRecord)

  uni.showToast({
    title: '添加成功',
    icon: 'success',
  })
}

// 删除记录
const deleteRecord = (recordIndex: number) => {
  if (tableData.value.length <= props.minRows) {
    uni.showToast({
      title: `至少保留${props.minRows}条记录`,
      icon: 'none',
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: `确定要删除第${recordIndex + 1}条记录吗？`,
    confirmText: '删除',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        const newData = [...tableData.value]
        newData.splice(recordIndex, 1)
        tableData.value = newData

        console.log(`🗑️ 删除记录[${recordIndex}]`)

        uni.showToast({
          title: '删除成功',
          icon: 'success',
        })
      }
    },
  })
}

// 打开表单抽屉
const openFormDrawer = () => {
  showDrawer.value = true
}

// 关闭抽屉
const closeDrawer = () => {
  showDrawer.value = false
}

// 校验方法 - 增强版本，使用 ComponentUtils.isEmpty
const validate = async (): Promise<boolean> => {
  // 如果数组为空，直接返回 true（允许为空）
  if (tableData.value.length === 0) {
    console.log('📋 tableForm 为空，校验通过')
    return true
  }

  console.log(`📋 开始校验 tableForm，共 ${tableData.value.length} 条记录`)

  // 校验每条记录的每个字段
  for (let i = 0; i < tableData.value.length; i++) {
    const record = tableData.value[i]
    console.log(`📋 校验第 ${i + 1} 条记录:`, record)

    for (const column of columns.value) {
      for (const fieldRule of column.rule || []) {
        const value = record[fieldRule.field]
        const fieldName = fieldRule.label || fieldRule.title || fieldRule.field

        // 🚀 使用 ComponentUtils.isEmpty 进行更准确的空值判断
        const isEmpty = ComponentUtils.isEmpty(fieldRule.type, value)

        // required 校验改为 $required 字段，并提供更具体的错误提示
        if (fieldRule.$required && isEmpty) {
          console.log(
            `❌ 必填字段校验失败: 第${i + 1}条记录的${fieldName} = ${value} (类型: ${fieldRule.type})`,
          )
          const specificErrorMessage =
            typeof fieldRule.$required === 'string'
              ? `第${i + 1}条记录：${fieldRule.$required}`
              : `第${i + 1}条记录：${fieldName}不能为空`
          uni.showToast({ title: specificErrorMessage, icon: 'none', duration: 3000 })
          return false
        }

        // pattern 校验
        if (fieldRule.pattern && !isEmpty && !fieldRule.pattern.test(value)) {
          console.log(`❌ 格式校验失败: 第${i + 1}条记录的${fieldName} = ${value}`)
          const formatErrorMessage = fieldRule.message || `第${i + 1}条记录：${fieldName}格式不正确`
          uni.showToast({ title: formatErrorMessage, icon: 'none', duration: 3000 })
          return false
        }

        // validator 校验
        if (typeof fieldRule.validator === 'function') {
          try {
            const result = fieldRule.validator(value, fieldRule)
            if (result instanceof Promise) {
              await result
            } else if (result !== true) {
              console.log(`❌ 自定义校验失败: 第${i + 1}条记录的${fieldName} = ${value}`)
              const customErrorMessage =
                fieldRule.message || `第${i + 1}条记录：${fieldName}校验未通过`
              uni.showToast({ title: customErrorMessage, icon: 'none', duration: 3000 })
              return false
            }
          } catch (err: any) {
            console.log(`❌ 自定义校验异常: 第${i + 1}条记录的${fieldName}`, err)
            const exceptionErrorMessage =
              err?.message || fieldRule.message || `第${i + 1}条记录：${fieldName}校验失败`
            uni.showToast({ title: exceptionErrorMessage, icon: 'none', duration: 3000 })
            return false
          }
        }

        console.log(
          `✅ 字段校验通过: 第${i + 1}条记录的${fieldName} = ${value} (isEmpty: ${isEmpty})`,
        )
      }
    }
  }

  console.log('📋 tableForm 所有记录校验通过')
  return true
}

defineExpose({ validate })

// 保存并关闭
const saveAndClose = async () => {
  const valid = await validate()
  if (!valid) return
  uni.showToast({
    title: '保存成功',
    icon: 'success',
  })
  closeDrawer()
}
</script>

<style lang="scss" scoped>
.yt-table-form {
  width: 100%;
}

.status-info {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.fill-status {
  font-size: 26rpx;
  color: #666;
}

// 抽屉样式
:deep(.table-form-drawer) {
  z-index: 9999;
}

// 通用图标样式
:deep(.wd-icon) {
  color: #007aff;
}

.drawer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #fff;
  border-bottom: 2rpx solid #eee;

  .drawer-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  :deep(.back-btn),
  :deep(.add-btn) {
    min-width: auto;
    padding: 0;
  }
}

.drawer-content {
  flex: 1;
  //padding: 24rpx;
}

.record-section {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
  background: #fff;
  border-radius: 12rpx;

  .record-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }

  :deep(.delete-btn) {
    min-width: auto;
    padding: 0;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  text-align: center;

  .empty-text {
    margin-bottom: 32rpx;
    font-size: 28rpx;
    color: #999;
  }
}

// 空状态图标样式
.empty-state .wd-icon {
  margin-bottom: 24rpx;
  color: #ccc;
}

// 删除按钮图标样式
.record-header :deep(.delete-btn) .wd-icon {
  color: #ff4d4f;
}

.info-section {
  padding: 0 24rpx;
  margin-top: 32rpx;
}

.drawer-footer {
  padding: 24rpx 32rpx;
  background: #fff;
  border-top: 2rpx solid #eee;
}

// 优化cell-group样式
:deep(.wd-cell-group) {
  overflow: hidden;
  border-radius: 12rpx;
}

// 自定义 cell-group 标题样式
:deep(.custom-cell-group) {
  margin-bottom: 24rpx;

  .wd-cell-group__title {
    padding: 0;
    background: transparent;
  }
}

.custom-group-title {
  display: flex;
  align-items: center;
  padding: 0rpx 24rpx;
  border-left: #667eea 4rpx solid;

  //background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  //border-radius: 12rpx 12rpx 0 0;

  .title-icon {
    margin-right: 12rpx;
    color: #fff;
  }

  .title-text {
    flex: 1;
    font-size: 28rpx;
    font-weight: 600;
    line-height: 1.4;
    color: #333333;
  }

  .title-badge {
    margin-left: 8rpx;

    .required-mark {
      padding: 2rpx 8rpx;
      font-size: 24rpx;
      font-weight: bold;
      color: #ff6b6b;
      background: #ffffffe6;
      border-radius: 6rpx;
    }
  }
}

// 优化按钮样式
:deep(.wd-button) {
  border-radius: 8rpx;
}
</style>
