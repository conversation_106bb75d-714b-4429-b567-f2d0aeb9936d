// {{CHENGQI:
// Action: Created; Timestamp: 2025-06-26 11:46:37 +08:00; Reason: 创建组件注册中心，参考form-create设计; Principle_Applied: 插件化架构, 高解耦设计;
// }}

import type { AsyncComponentLoader, Component } from 'vue'

/**
 * 组件注册选项
 */
interface ComponentRegisterOptions {
  /** 组件别名 */
  alias?: string[]
  /** 平台支持 */
  platforms?: ('h5' | 'mp-weixin' | 'mp-alipay' | 'app')[]
  /** 是否预加载 */
  preload?: boolean
  /** 组件版本 */
  version?: string
  /** 组件描述 */
  description?: string
}

/**
 * 平台组件映射
 */
interface PlatformComponentMap {
  h5?: AsyncComponentLoader | Component
  'mp-weixin'?: AsyncComponentLoader | Component
  'mp-alipay'?: AsyncComponentLoader | Component
  app?: AsyncComponentLoader | Component
  default?: AsyncComponentLoader | Component
}

/**
 * 组件元数据
 */
interface ComponentMeta {
  type: string
  component: AsyncComponentLoader | Component | PlatformComponentMap
  options: ComponentRegisterOptions
  loaded?: boolean
  instance?: Component
}

/**
 * 组件注册中心
 * 参考 form-create 的设计思想，提供统一的组件管理机制
 */
class ComponentRegistry {
  private components = new Map<string, ComponentMeta>()
  private aliases = new Map<string, string>()
  private loadedCache = new Map<string, Component>()
  private preloadComponentList: string[] = []

  constructor() {
    this.initDefaultComponents()
  }

  /**
   * 注册组件
   * @param type 组件类型
   * @param component 组件或组件加载器
   * @param options 注册选项
   */
  register(
    type: string,
    component: AsyncComponentLoader | Component | PlatformComponentMap,
    options: ComponentRegisterOptions = {},
  ): void {
    const meta: ComponentMeta = {
      type,
      component,
      options,
      loaded: false,
    }

    this.components.set(type, meta)

    // 注册别名
    if (options.alias) {
      options.alias.forEach((alias) => {
        this.aliases.set(alias, type)
      })
    }

    // 添加到预加载列表
    if (options.preload) {
      this.preloadComponentList.push(type)
    }
  }

  /**
   * 批量注册组件
   */
  registerBatch(componentMap: Record<string, any>): void {
    Object.entries(componentMap).forEach(([type, component]) => {
      this.register(type, component)
    })
  }

  /**
   * 条件注册 - 根据平台注册不同组件
   */
  registerConditional(
    type: string,
    platformComponents: PlatformComponentMap,
    options: ComponentRegisterOptions = {},
  ): void {
    this.register(type, platformComponents, options)
  }

  /**
   * 获取组件 - 异步加载并缓存
   */
  async getComponent(type: string): Promise<Component | null> {
    // 解析别名
    const actualType = this.aliases.get(type) || type

    // 检查缓存
    if (this.loadedCache.has(actualType)) {
      return this.loadedCache.get(actualType)!
    }

    const meta = this.components.get(actualType)
    if (!meta) {
      console.warn(`⚠️ 未找到组件: ${type}`)
      return null
    }

    try {
      let component: Component

      // 处理平台组件映射
      if (this.isPlatformComponentMap(meta.component)) {
        const platformComponent = this.resolvePlatformComponent(meta.component)
        component = await this.loadComponentInstance(platformComponent)
      } else {
        component = await this.loadComponentInstance(meta.component)
      }

      // 缓存组件
      this.loadedCache.set(actualType, component)
      meta.loaded = true
      meta.instance = component

      console.log(`✅ 组件加载成功: ${actualType}`)
      return component
    } catch (error) {
      console.error(`❌ 组件加载失败: ${actualType}`, error)
      return null
    }
  }

  /**
   * 同步获取组件 - 用于微信小程序兼容
   */
  getComponentSync(type: string): Component | null {
    const actualType = this.aliases.get(type) || type
    return this.loadedCache.get(actualType) || null
  }

  /**
   * 检查组件是否存在
   */
  hasComponent(type: string): boolean {
    const actualType = this.aliases.get(type) || type
    return this.components.has(actualType)
  }

  /**
   * 获取组件类型映射 - 用于微信小程序静态渲染
   */
  getComponentTypeMap(): Record<string, string> {
    const typeMap: Record<string, string> = {}

    this.components.forEach((meta, type) => {
      // 为微信小程序生成组件名映射
      typeMap[type] = this.convertToKebabCase(type)
    })

    return typeMap
  }

  /**
   * 预加载组件
   */
  async preloadComponents(): Promise<void> {
    const promises = this.preloadComponentList.map((type) => this.getComponent(type))
    await Promise.allSettled(promises)
    console.log('🚀 预加载组件完成')
  }

  /**
   * 获取所有已注册的组件类型
   */
  getRegisteredTypes(): string[] {
    return Array.from(this.components.keys())
  }

  /**
   * 获取组件元数据
   */
  getComponentMeta(type: string): ComponentMeta | undefined {
    const actualType = this.aliases.get(type) || type
    return this.components.get(actualType)
  }

  // 私有方法

  private async loadComponentInstance(
    component: AsyncComponentLoader | Component,
  ): Promise<Component> {
    if (typeof component === 'function') {
      try {
        const loaded = await (component as AsyncComponentLoader)()
        return loaded.default || loaded
      } catch (error) {
        console.error('组件加载失败:', error)
        throw error
      }
    }
    return component as Component
  }

  private isPlatformComponentMap(component: any): component is PlatformComponentMap {
    return (
      component &&
      typeof component === 'object' &&
      ('h5' in component || 'mp-weixin' in component || 'default' in component)
    )
  }

  private resolvePlatformComponent(
    platformMap: PlatformComponentMap,
  ): AsyncComponentLoader | Component {
    // #ifdef H5
    if (platformMap.h5) return platformMap.h5
    // #endif

    // #ifdef MP-WEIXIN
    if (platformMap['mp-weixin']) return platformMap['mp-weixin']
    // #endif

    // #ifdef MP-ALIPAY
    if (platformMap['mp-alipay']) return platformMap['mp-alipay']
    // #endif

    // #ifdef APP
    if (platformMap.app) return platformMap.app
    // #endif

    return platformMap.default || (() => Promise.reject(new Error('No platform component found')))
  }

  private convertToKebabCase(str: string): string {
    return str
      .replace(/([A-Z])/g, '-$1')
      .toLowerCase()
      .replace(/^-/, '')
  }

  /**
   * 初始化默认组件
   */
  private initDefaultComponents(): void {
    // 基础输入组件
    this.register('input', () => import('../inputs/yt-input.vue'), {
      preload: true,
      description: '文本输入框',
    })

    this.register('textarea', () => import('../inputs/yt-textarea.vue'), {
      preload: true,
      description: '多行文本域',
    })

    this.register('inputNumber', () => import('../inputs/yt-input-number.vue'), {
      description: '数字输入框',
    })

    // 选择器组件
    this.register('select', () => import('../selectors/basic/yt-select-single.vue'), {
      preload: true,
      description: '下拉选择器',
    })

    this.register('select-single', () => import('../selectors/basic/yt-select-single.vue'), {
      alias: ['selectSingle'],
      description: '单选选择器',
    })

    this.register('select-multi', () => import('../selectors/basic/yt-select-multi.vue'), {
      alias: ['selectMulti'],
      description: '多选选择器',
    })

    this.register('checkbox', () => import('../selectors/basic/yt-checkbox.vue'), {
      preload: true,
      alias: ['Checkbox'],
      description: '复选框组件',
    })

    // 高级选择器
    this.register('RegionCascader', () => import('../selectors/advanced/yt-region-cascader.vue'), {
      alias: ['regionCascader', 'region'],
      description: '地区级联选择器',
    })

    this.register('dict-select', () => import('../selectors/advanced/yt-dict-select.vue'), {
      alias: ['DictSelect', 'dictSelect'],
      description: '字典选择器',
    })

    // 日期时间组件
    this.register('calendar', () => import('../special/datetime/yt-calendar.vue'), {
      description: '日历组件',
    })

    this.register('datetime-picker', () => import('../special/datetime/yt-datetime-picker.vue'), {
      alias: ['timePicker', 'datePicker'],
      description: '日期时间选择器',
    })

    // 特殊功能组件
    this.register('upload', () => import('../special/upload/yt-upload.vue'), {
      description: '文件上传组件',
    })

    this.register(
      'DynamicCheckboxWithUpload',
      () => import('../special/upload/yt-checkbox-with-upload.vue'),
      {
        description: '复选框+上传组件',
      },
    )

    this.register('MapSelector', () => import('../special/map/yt-map-selector.vue'), {
      description: '地图选择器',
    })

    this.register('MapDraw', () => import('../special/map/yt-map-selector.vue'), {
      alias: ['mapDraw'],
      description: '地图绘制组件',
    })

    // 复合组件
    this.register('tableForm', () => import('../composites/yt-table-form.vue'), {
      description: '表格表单组件',
    })

    this.register(
      'DictSelectWithOther',
      () => import('../composites/yt-dict-select-with-other.vue'),
      {
        description: '字典+其他选择器',
      },
    )

    // 反馈组件
    this.register('NoticeBar', () => import('../special/feedback/yt-notice-bar.vue'), {
      alias: ['noticeBar'],
      description: '通知栏组件',
    })

    this.register('eAlert', () => import('../special/feedback/yt-notice-bar.vue'), {
      alias: ['alert'],
      description: '警告提示组件',
    })

    this.register('elAlert', () => import('../special/feedback/yt-notice-bar.vue'), {
      alias: ['elementAlert'],
      description: 'Element Alert警告提示组件',
    })

    // 分割线组件
    this.register('divider', () => import('../special/feedback/yt-divider.vue'), {
      alias: ['Divider'],
      description: '分割线组件',
    })

    this.register('elDivider', () => import('../special/feedback/yt-divider.vue'), {
      alias: ['elementDivider'],
      description: 'Element Divider分割线组件',
    })
  }
}

// 创建全局单例
export const componentRegistry = new ComponentRegistry()

// 导出类型
export type { ComponentRegisterOptions, PlatformComponentMap, ComponentMeta }
export { ComponentRegistry }

// 默认导出
export default componentRegistry
