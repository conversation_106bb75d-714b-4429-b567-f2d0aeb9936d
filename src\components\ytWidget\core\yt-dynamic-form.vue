<template>
  <view class="yt-dynamic-form">
    <wd-form ref="formRef" :model="formModel">
      <wd-cell-group>
        <template v-for="(item, index) in formConfig" :key="index">
          <!-- 添加条件渲染 -->
          <template v-if="shouldShowField(item)">
            <yt-form-item
              v-model="formModel[item.field]"
              :config="item"
              :disabled="
                (fieldStatus[item.field]?.disabled ?? false) || formDisabled || item.disabled
              "
              :readonly="formReadonly || item.readonly"
              :required="fieldStatus[item.field]?.required ?? item.$required"
              @update:model-value="handleFieldValueUpdate(item.field, $event)"
            />
          </template>
        </template>

        <!-- 提交按钮 -->
        <wd-cell v-if="showSubmitButton">
          <wd-button :loading="submitLoading" type="primary" @click="handleSubmit">
            {{ submitButtonText }}
          </wd-button>
        </wd-cell>
      </wd-cell-group>
    </wd-form>
  </view>
</template>

<script lang="ts" setup>
import { computed, nextTick, onUnmounted, provide, ref, toRaw, watch } from 'vue'
import type { FormItem } from '@/types/form'
import YtFormItem from './yt-form-item.vue'

interface Props {
  // 表单配置
  config: FormItem[]
  // 表单数据模型
  modelValue?: Record<string, any>
  // 是否显示提交按钮
  showSubmitButton?: boolean
  // 提交按钮文本
  submitButtonText?: string
  // 提交状态
  submitLoading?: boolean
  // 表单是否禁用
  formDisabled?: boolean
  // 表单是否只读
  formReadonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  config: () => [],
  modelValue: () => ({}),
  showSubmitButton: false,
  submitButtonText: '提交',
  submitLoading: false,
  formDisabled: false,
  formReadonly: false,
})

// 精简事件定义，只保留实际使用的事件
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  submit: [data: Record<string, any>]
  validate: [result: { valid: boolean; errors: string[] }]
}>()

// 🚀 使用 computed 优化表单配置 - 自动缓存，避免重复计算
const formConfig = computed(() => props.config)

const formModel = ref<Record<string, any>>({})
const formRef = ref()
const isUpdating = ref(false)

// 🚀 新增：tableForm 实例映射管理
const tableFormRefs = ref<Map<string, any>>(new Map())

// 🚀 新增：注册 tableForm 实例的方法
const registerTableFormRef = (field: string, ref: any) => {
  if (ref) {
    tableFormRefs.value.set(field, ref)
    console.log(`📋 注册 tableForm 实例: ${field}`, ref)
  } else {
    tableFormRefs.value.delete(field)
    console.log(`📋 注销 tableForm 实例: ${field}`)
  }
}

// 新增：用于存储每个字段的动态状态
const fieldStatus = ref<
  Record<string, { hidden?: boolean; display?: boolean; disabled?: boolean; required?: boolean }>
>({})

// 判断条件函数
function checkCondition(val: any, value: any, condition: string): boolean {
  switch (condition) {
    case '==':
      return val === value
    case '!=':
      return val !== value
    case '>':
      return val > value
    case '>=':
      return val >= value
    case '<':
      return val < value
    case '<=':
      return val <= value
    case 'in':
      return Array.isArray(value) && value.includes(val)
    case 'notIn':
      return Array.isArray(value) && !value.includes(val)
    case 'empty':
      return (
        val === '' || val === null || val === undefined || (Array.isArray(val) && val.length === 0)
      )
    case 'notEmpty':
      return !(
        val === '' ||
        val === null ||
        val === undefined ||
        (Array.isArray(val) && val.length === 0)
      )
    default:
      return false
  }
}

// 监听表单数据变化，自动处理control联动
watch(
  formModel,
  () => {
    // 初始化所有字段状态
    const statusMap: Record<
      string,
      { hidden?: boolean; display?: boolean; disabled?: boolean; required?: boolean }
    > = {}
    formConfig.value.forEach((item) => {
      statusMap[item.field] = {}
    })
    // 遍历所有有control的字段
    formConfig.value.forEach((item) => {
      if (!item.control) return
      item.control.forEach((ctrl) => {
        // 不支持handle
        let match = false
        if (ctrl.condition) {
          match = checkCondition(formModel.value[item.field], ctrl.value, ctrl.condition)
        } else if ('value' in ctrl) {
          match = formModel.value[item.field] === ctrl.value
        }
        // 控制目标字段
        ctrl.rule.forEach((targetField) => {
          if (!statusMap[targetField]) statusMap[targetField] = {}
          switch (ctrl.method) {
            case 'hidden':
            case 'if': // if等同于hidden
              statusMap[targetField].hidden = !match
              break
            case 'display':
              statusMap[targetField].display = match
              break
            case 'disabled':
              statusMap[targetField].disabled = !match
              break
            case 'required':
              statusMap[targetField].required = match
              break
          }
        })
      })
    })
    fieldStatus.value = statusMap
  },
  { deep: true, immediate: true },
)

// 修改shouldShowField，优先用control结果
const shouldShowField = (item: FormItem): boolean => {
  const status = fieldStatus.value[item.field] || {}
  // 优先hidden/if
  if (status.hidden === true) return false
  // display为false时隐藏
  if (status.display === false) return false
  // 兼容旧condition函数
  if (item.condition) {
    try {
      return item.condition(formModel.value)
    } catch {
      return true
    }
  }
  return true
}

// 只做同步，不清理隐藏字段，保证回显完整
watch(
  () => props.modelValue,
  (newModelValue) => {
    if (!isUpdating.value) {
      formModel.value = { ...newModelValue }
      console.log('📥 同步外部数据变化:', newModelValue)
    }
  },
  { deep: true, immediate: true },
)

// 🚀 优化的防抖更新机制 - 使用 RAF + setTimeout 组合
let rafId: number | null = null
let timeoutId: number | null = null

// 清理所有隐藏字段的数据
function cleanHiddenFields(model: Record<string, any>) {
  formConfig.value.forEach((item) => {
    if (!shouldShowField(item) && model[item.field] !== undefined) {
      delete model[item.field]
    }
  })
}

// 优化 handleFieldValueUpdate，emit 前清理隐藏字段
const handleFieldValueUpdate = (field: string, value: any) => {
  console.log(`📝 字段变化: ${field}`, value)

  // 立即更新内部模型
  formModel.value[field] = value

  // 清除之前的定时器
  if (rafId) {
    cancelAnimationFrame(rafId)
    rafId = null
  }
  if (timeoutId) {
    clearTimeout(timeoutId)
    timeoutId = null
  }

  // 🚀 使用 RAF + setTimeout 双重防抖 - 更平滑的更新
  rafId = requestAnimationFrame(() => {
    timeoutId = setTimeout(async () => {
      if (isUpdating.value) return

      isUpdating.value = true

      try {
        await nextTick()

        // 更新表单数据
        const updatedData = { ...formModel.value }
        // 清理隐藏字段
        cleanHiddenFields(updatedData)
        emit('update:modelValue', updatedData)
        console.log('📤 防抖emit update:modelValue:', updatedData)
      } finally {
        isUpdating.value = false
        rafId = null
        timeoutId = null
      }
    }, 8) // 8ms 防抖，更快响应
  })
}

// 🚀 使用 computed 缓存必填字段计算
const requiredFields = computed(() => formConfig.value.filter((item) => item.$required))

// 🚀 优化的表单验证 - 更高效的错误收集
const validateForm = async () => {
  const errors: string[] = []
  let allValid = true

  console.log('📋 开始表单校验...')

  // 🚀 新增：校验 tableForm 类型字段
  for (const item of formConfig.value) {
    // 只验证显示的字段
    if (!shouldShowField(item)) continue

    if (item.type === 'tableForm') {
      const tableFormInstance = tableFormRefs.value.get(item.field)
      console.log(`📋 校验 tableForm 字段: ${item.field}`, tableFormInstance)

      if (tableFormInstance && typeof tableFormInstance.validate === 'function') {
        try {
          const valid = await tableFormInstance.validate()
          if (!valid) {
            allValid = false
            const tableFormErrorMessage = `表格表单"${item.title}"校验失败`
            errors.push(tableFormErrorMessage)
            console.log(`❌ ${tableFormErrorMessage}`)
            // tableForm 组件内部已经显示了具体的错误提示，这里不需要重复显示
            return false // 立即返回，避免重复提示
          } else {
            console.log(`✅ 表格表单"${item.title}"校验通过`)
          }
        } catch (error) {
          allValid = false
          const tableFormExceptionMessage = `表格表单"${item.title}"校验异常`
          errors.push(tableFormExceptionMessage)
          console.error(`💥 ${tableFormExceptionMessage}:`, error)
          uni.showToast({
            title: tableFormExceptionMessage,
            icon: 'none',
            duration: 3000,
          })
          return false
        }
      } else {
        console.warn(`⚠️ tableForm 实例未找到: ${item.field}`)
        // 如果实例未找到，可能是组件还未加载完成，暂时跳过
      }
    }
  }

  // 原有必填校验 - 只验证显示的字段
  for (const field of requiredFields.value) {
    if (!shouldShowField(field)) continue

    const value = formModel.value[field.field]
    const isEmpty =
      value === '' ||
      value === null ||
      value === undefined ||
      (Array.isArray(value) && value.length === 0) ||
      (typeof value === 'object' && value !== null && Object.keys(value).length === 0)

    if (isEmpty) {
      const errorMsg =
        typeof field.$required === 'string' ? field.$required : `${field.title}不能为空`
      errors.push(errorMsg)
      allValid = false
    }
  }

  const isValid = errors.length === 0 && allValid
  emit('validate', { valid: isValid, errors })

  // 🚀 改进的错误提示 - 更用户友好
  if (!isValid && errors.length > 0) {
    uni.showToast({
      title: errors[0],
      icon: 'none',
      duration: 3000,
    })
  }

  console.log(`📋 表单校验完成: ${isValid ? '✅ 通过' : '❌ 失败'}`, { errors, allValid })
  return isValid
}

// 🚀 获取结构完整的表单数据（隐藏字段为默认空值）
function getFullFormData() {
  const result: Record<string, any> = {}
  formConfig.value.forEach((item) => {
    if (shouldShowField(item)) {
      result[item.field] = formModel.value[item.field]
    } else {
      result[item.field] = getDefaultValueByType(item)
    }
  })
  return result
}

// handleSubmit 提交时用结构完整数据
const handleSubmit = () => {
  console.log('提交表单数据：', formModel.value)
  const submitData = getFullFormData()
  if (validateForm()) {
    emit('submit', submitData)
  }
}

// 🚀 批量数据设置方法 - 减少响应式触发
const setFormData = async (data: Record<string, any>) => {
  isUpdating.value = true

  try {
    // 🚀 使用 Object.assign 批量更新
    Object.assign(formModel.value, data)
    await nextTick()
  } finally {
    isUpdating.value = false
  }
}

// 获取表单数据 - 使用toRaw避免响应式包装
const getFormData = () => toRaw(formModel.value)

// 🚀 优化的默认值计算 - 使用映射表提升性能
const DEFAULT_VALUES = {
  select: (item: FormItem) => (item.props?.multiple ? [] : ''),
  calendar: () => Date.now(),
  timePicker: () => '12:00',
  datePicker: () => '',
  'datetime-picker': () => '',
  inputNumber: (item: FormItem) => {
    // 如果允许空值，默认为空字符串
    if (item.props?.allowNull) return ''
    // 如果设置了明确的min值，使用min值
    if (item.props?.min !== undefined) return item.props.min
    // 否则默认为空字符串，让组件自己处理
    return ''
  },
  RegionCascader: () => [],
  upload: () => [],
  DynamicCheckboxWithUpload: () => ({ checked: false, images: [] }),
  default: () => '',
} as const

const getDefaultValueByType = (item: FormItem): any => {
  const getValue =
    DEFAULT_VALUES[item.type as keyof typeof DEFAULT_VALUES] || DEFAULT_VALUES.default
  return typeof getValue === 'function' ? getValue(item) : getValue
}

// 🚀 优化的重置表单 - 使用 reduce 和批量更新
const resetForm = async () => {
  isUpdating.value = true

  try {
    const resetData = formConfig.value.reduce(
      (acc, item) => {
        acc[item.field] = getDefaultValueByType(item)
        return acc
      },
      {} as Record<string, any>,
    )

    formModel.value = resetData
    await nextTick()
  } finally {
    isUpdating.value = false
  }
}

// 🚀 优化的清理函数
const cleanup = () => {
  if (rafId) {
    cancelAnimationFrame(rafId)
    rafId = null
  }
  if (timeoutId) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
}

onUnmounted(cleanup)

// 暴露方法给父组件
defineExpose({
  validateForm,
  setFormData,
  getFormData,
  resetForm,
  formModel,
  formRef,
  cleanup,
  registerTableFormRef,
})

// 🚀 新增：向子组件提供注册方法
provide('registerTableFormRef', registerTableFormRef)

// 每次联动后自动重置所有被隐藏字段的数据为默认空值
function resetHiddenFields() {
  formConfig.value.forEach((item) => {
    if (!shouldShowField(item)) {
      formModel.value[item.field] = getDefaultValueByType(item)
    }
  })
}

// 在fieldStatus变化后自动重置隐藏字段
watch(fieldStatus, () => {
  resetHiddenFields()
})
</script>

<style lang="scss" scoped>
.yt-dynamic-form {
  width: 100%;

  .wd-cell-group {
    overflow: hidden;
    border-radius: 8px;
  }
}
</style>
