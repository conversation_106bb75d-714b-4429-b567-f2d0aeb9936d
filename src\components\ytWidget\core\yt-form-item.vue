<!--
// {{CHENGQI:
// Action: Modified; Timestamp: 2025-06-26 13:33:25 +08:00; Reason: 修复微信小程序条件编译中缺失的组件支持，添加所有组件的静态导入和渲染; Principle_Applied: 平台兼容性, 完整性;
// }}
-->
<template>
  <view class="yt-form-item">
    <!-- 微信小程序兼容：使用静态组件渲染 -->
    <!-- #ifdef MP-WEIXIN -->
    <!-- 基础输入组件 -->
    <!-- 智能处理 input 类型 - 如果 props.type 为 textarea 则使用 yt-textarea -->
    <template v-if="config.type === 'input'">
      <yt-textarea
        v-if="config.props?.type === 'textarea'"
        v-model="currentValue"
        :item="config"
        :disabled="disabled"
        :readonly="readonly"
        @update:model-value="handleValueChange"
      />
      <yt-input
        v-else
        v-model="currentValue"
        :item="config"
        :disabled="disabled"
        :readonly="readonly"
        @update:model-value="handleValueChange"
      />
    </template>

    <yt-input-number
      v-else-if="config.type === 'inputNumber'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 选择器组件 -->
    <yt-select-single
      v-else-if="config.type === 'select' && !config.props?.multiple"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <yt-select-multi
      v-else-if="config.type === 'select' && config.props?.multiple"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <yt-checkbox
      v-else-if="config.type === 'checkbox'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <yt-region-cascader
      v-else-if="config.type === 'RegionCascader'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 字典选择器组件 -->
    <yt-dict-select-with-other
      v-else-if="config.type === 'DictSelectWithOther'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <yt-dict-select
      v-else-if="config.type === 'dict-select' || config.type === 'DictSelect'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 日期时间组件 -->
    <yt-calendar
      v-else-if="config.type === 'calendar'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <yt-datetime-picker
      v-else-if="isDateTimeType(config.type)"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 文件上传组件 -->
    <yt-upload
      v-else-if="config.type === 'upload'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <yt-checkbox-with-upload
      v-else-if="config.type === 'DynamicCheckboxWithUpload'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 地图组件 -->
    <yt-map-selector
      v-else-if="config.type === 'MapSelector' || config.type === 'MapDraw'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 复合组件 -->
    <yt-table-form
      v-else-if="config.type === 'tableForm'"
      ref="tableFormRef"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 反馈组件 -->
    <yt-notice-bar
      v-else-if="
        config.type === 'NoticeBar' || config.type === 'eAlert' || config.type === 'elAlert'
      "
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 分割线组件 -->
    <yt-divider
      v-else-if="config.type === 'elDivider'"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <!-- 未知组件类型 -->
    <wd-cell v-else :title="config.title || '未知组件'" :value="`类型: ${config.type}`" />
    <!-- #endif -->

    <!-- H5/APP：使用动态组件渲染 -->
    <!-- #ifndef MP-WEIXIN -->
    <component
      :is="dynamicComponent"
      v-if="dynamicComponent"
      v-model="currentValue"
      :item="config"
      :disabled="disabled"
      :readonly="readonly"
      @update:model-value="handleValueChange"
    />

    <wd-loading v-else-if="loading" size="20px" />
    <wd-cell v-else :title="config.title || '组件加载失败'" :value="`类型: ${config.type}`" />
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
import { computed, ref, watch, onMounted, onUnmounted, inject } from 'vue'
import { componentRegistry } from './yt-component-registry'
import type { FormItem } from '@/types/form'

// 导入所有组件（微信小程序需要静态导入）
import YtInput from '../inputs/yt-input.vue'
import YtTextarea from '../inputs/yt-textarea.vue'
import YtInputNumber from '../inputs/yt-input-number.vue'
import YtSelectSingle from '../selectors/basic/yt-select-single.vue'
import YtSelectMulti from '../selectors/basic/yt-select-multi.vue'
import YtCheckbox from '../selectors/basic/yt-checkbox.vue'
import YtCalendar from '../special/datetime/yt-calendar.vue'
import YtDatetimePicker from '../special/datetime/yt-datetime-picker.vue'
import YtTableForm from '../composites/yt-table-form.vue'
import YtRegionCascader from '../selectors/advanced/yt-region-cascader.vue'
import YtUpload from '../special/upload/yt-upload.vue'
import YtCheckboxWithUpload from '../special/upload/yt-checkbox-with-upload.vue'
import YtMapSelector from '../special/map/yt-map-selector.vue'
import YtDictSelectWithOther from '../composites/yt-dict-select-with-other.vue'
import YtDictSelect from '../selectors/advanced/yt-dict-select.vue'
import YtNoticeBar from '../special/feedback/yt-notice-bar.vue'
import YtDivider from '../special/feedback/yt-divider.vue'

interface Props {
  config: FormItem
  modelValue?: any
  disabled?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
})

// 精简事件定义，只保留必要的事件
const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

// 响应式数据
const loading = ref(false)
const dynamicComponent = ref<any>(null)
const tableFormRef = ref<any>(null)

// 🚀 新增：注入父表单的 registerTableFormRef 方法
const registerTableFormRef = inject<(field: string, ref: any) => void>(
  'registerTableFormRef',
  () => {},
)

// 当前值的双向绑定
const currentValue = computed({
  get: () => props.modelValue,
  set: (value: any) => {
    emit('update:modelValue', value)
  },
})

// 工具函数
const isDateTimeType = (type: string): boolean => {
  return ['datetime-picker', 'timePicker', 'datePicker'].includes(type)
}

// 动态组件加载（仅 H5/APP 使用）
const loadDynamicComponent = async () => {
  // #ifndef MP-WEIXIN
  if (!componentRegistry.hasComponent(props.config.type)) {
    console.warn(`⚠️ 未注册的组件类型: ${props.config.type}`)
    return
  }

  loading.value = true
  try {
    const component = await componentRegistry.getComponent(props.config.type)
    if (component) {
      dynamicComponent.value = component
    }
  } catch (error) {
    console.error(`❌ 动态组件加载失败: ${props.config.type}`, error)
  } finally {
    loading.value = false
  }
  // #endif
}

// 事件处理器 - 统一数据结构，自动包含字段信息
const handleValueChange = (value: any) => {
  const oldValue = currentValue.value
  emit('update:modelValue', value)
}

// 验证逻辑
const validateValue = () => {
  const value = currentValue.value

  if (props.config.$required) {
    const isEmpty =
      value === '' ||
      value === null ||
      value === undefined ||
      (Array.isArray(value) && value.length === 0) ||
      (typeof value === 'object' && value !== null && Object.keys(value).length === 0)

    if (isEmpty) {
      return false
    }
  }

  return true
}

// 监听配置变化，重新加载组件
watch(
  () => props.config.type,
  () => {
    loadDynamicComponent()
  },
  { immediate: true },
)

// 组件挂载时加载动态组件
onMounted(() => {
  loadDynamicComponent()
})

// 🚀 新增：tableForm 实例注册和注销
watch(tableFormRef, (newRef) => {
  if (props.config.type === 'tableForm') {
    registerTableFormRef(props.config.field, newRef)
  }
})

onUnmounted(() => {
  if (props.config.type === 'tableForm') {
    registerTableFormRef(props.config.field, null)
  }
})

// 暴露验证方法
defineExpose({
  validateValue,
  currentValue,
})
</script>

<style lang="scss" scoped>
.yt-form-item {
  width: 100%;
}
</style>
