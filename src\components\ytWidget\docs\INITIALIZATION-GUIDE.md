# YtWidget 组件数据初始化指南

> **版本**: v2.1.0  
> **更新时间**: 2025-06-27 17:07:04 +08:00  
> **核心特性**: 传入值优先级、类型安全空值、自动初始化

## 📋 概述

YtWidget 组件库提供了完善的数据初始化机制，支持：

- ✅ **传入值优先**：用户传入的值始终优先使用
- ✅ **类型安全空值**：每种组件类型有对应的空值格式
- ✅ **自动类型转换**：确保数据格式符合组件要求
- ✅ **批量初始化**：支持整个表单的统一初始化
- ✅ **响应式状态**：基于Vue3组合式API设计

## 🎯 核心API

### ComponentUtils (工具函数)

```typescript
import { ComponentUtils } from '@/components/ytWidget'

// 1. 获取组件类型的空值
const emptyValue = ComponentUtils.getEmptyValue('input') // ''
const emptyArray = ComponentUtils.getEmptyValue('upload') // []
const emptyObject = ComponentUtils.getEmptyValue('DynamicCheckboxWithUpload')
// { checked: false, images: [] }

// 2. 合并用户值与默认值（优先级处理）
const finalValue = ComponentUtils.mergeWithDefaults('select-multi', userValue)

// 3. 批量初始化表单数据
const formData = ComponentUtils.initializeFormData(formConfig, userValues)

// 4. 重置到空值状态
const emptyForm = ComponentUtils.resetToEmpty(formConfig)
```

### useFormInitializer (组合式API)

```typescript
import { useFormInitializer } from '@/components/ytWidget/composables/useFormInitializer'

const {
  formData, // 响应式表单数据
  isEmpty, // 是否为空
  hasChanges, // 是否有变化
  initializeForm, // 初始化方法
  resetToEmpty, // 重置为空
  updateField, // 更新字段
} = useFormInitializer(formConfig, initialValues)
```

## 📊 组件类型空值对照表

| 组件类型                    | 空值                                                    | 示例           |
| --------------------------- | ------------------------------------------------------- | -------------- |
| `input`                     | `''`                                                    | 空字符串       |
| `textarea`                  | `''`                                                    | 空字符串       |
| `inputNumber`               | `null`                                                  | 数字空值       |
| `select`                    | `''`                                                    | 单选空值       |
| `select-multi`              | `[]`                                                    | 多选空数组     |
| `checkbox`                  | `[]`                                                    | 复选框组空数组 |
| `calendar`                  | `null`                                                  | 日期空值       |
| `datetime-picker`           | `null`                                                  | 时间空值       |
| `upload`                    | `[]`                                                    | 文件列表空数组 |
| `RegionCascader`            | `[]`                                                    | 级联选择空数组 |
| `DynamicCheckboxWithUpload` | `{checked: false, images: []}`                          | 结构化空值     |
| `DictSelectWithOther`       | `{otherText: '', selectType: 'checkbox', selected: []}` | 字典+其他空值  |

## 🚀 使用场景示例

### 场景1：基础使用（空值初始化）

```vue
<template>
  <yt-dynamic-form v-model="formData" :config="formConfig" />
</template>

<script setup lang="ts">
import { useFormInitializer } from '@/components/ytWidget/composables/useFormInitializer'

const formConfig = [
  { type: 'input', field: 'name', title: '姓名' },
  { type: 'select-multi', field: 'hobbies', title: '爱好' },
  { type: 'upload', field: 'avatar', title: '头像' },
]

// 自动初始化为空值
const { formData } = useFormInitializer(formConfig)

// 结果：
// formData.value = {
//   name: '',
//   hobbies: [],
//   avatar: []
// }
</script>
```

### 场景2：传入初始值（优先级覆盖）

```vue
<script setup lang="ts">
const userInitialValues = {
  name: '张三',
  hobbies: ['reading', 'gaming'], // 用户值优先
  avatar: [], // 即使传入空数组也会被保留
}

const { formData } = useFormInitializer(formConfig, userInitialValues)

// 结果：
// formData.value = {
//   name: '张三',        // 用户传入值
//   hobbies: ['reading', 'gaming'], // 用户传入值
//   avatar: []          // 用户传入值（虽然是空数组）
// }
</script>
```

### 场景3：动态更新和重置

```vue
<template>
  <view>
    <yt-dynamic-form v-model="formData" :config="formConfig" />

    <wd-button @click="handleReset">重置为空</wd-button>
    <wd-button @click="handleResetToInitial">重置为初始值</wd-button>
    <wd-button @click="handleUpdateField">更新字段</wd-button>

    <text>表单是否为空: {{ isEmpty }}</text>
    <text>表单是否有变化: {{ hasChanges }}</text>
  </view>
</template>

<script setup lang="ts">
const { formData, isEmpty, hasChanges, resetToEmpty, resetToInitial, updateField } =
  useFormInitializer(formConfig, { name: '初始姓名' })

const handleReset = () => {
  resetToEmpty() // 重置为类型安全的空值
}

const handleResetToInitial = () => {
  resetToInitial() // 重置为初始传入值
}

const handleUpdateField = () => {
  updateField('name', '新姓名') // 类型安全的字段更新
}
</script>
```

### 场景4：复杂表单初始化

```vue
<script setup lang="ts">
const complexFormConfig = [
  { type: 'input', field: 'username', title: '用户名' },
  { type: 'inputNumber', field: 'age', title: '年龄' },
  { type: 'select-multi', field: 'skills', title: '技能' },
  { type: 'DynamicCheckboxWithUpload', field: 'agreement', title: '协议确认' },
  { type: 'RegionCascader', field: 'address', title: '地址' },
  { type: 'calendar', field: 'birthday', title: '生日' },
]

// 部分初始值
const partialValues = {
  username: '用户123',
  age: 25,
  skills: ['Vue', 'TypeScript'],
  // agreement、address、birthday将使用默认空值
}

const { formData, validateTypes } = useFormInitializer(complexFormConfig, partialValues)

// 结果：
// formData.value = {
//   username: '用户123',              // 用户值
//   age: 25,                        // 用户值
//   skills: ['Vue', 'TypeScript'],  // 用户值
//   agreement: { checked: false, images: [] }, // 默认空值
//   address: [],                    // 默认空值
//   birthday: null                  // 默认空值
// }

// 类型验证
const validation = validateTypes()
console.log('类型验证结果:', validation)
</script>
```

### 场景5：表单数据监控

```vue
<script setup lang="ts">
import { watch } from 'vue'

const { formData, getChangedFields, getSnapshot } = useFormInitializer(formConfig, initialValues)

// 监控表单变化
watch(
  getChangedFields,
  (changes) => {
    console.log('字段变化:', changes)
    // 输出格式：
    // {
    //   name: { from: '旧值', to: '新值' },
    //   age: { from: 20, to: 25 }
    // }
  },
  { deep: true },
)

// 保存快照
const saveSnapshot = () => {
  const snapshot = getSnapshot()
  localStorage.setItem('formSnapshot', JSON.stringify(snapshot))
}
</script>
```

## 🔧 高级功能

### 自定义类型转换

```typescript
// 自定义处理特殊业务类型
const customMerge = (type: string, userValue: any) => {
  if (type === 'custom-business-type') {
    // 自定义处理逻辑
    return userValue || { customProp: '', businessData: [] }
  }

  return ComponentUtils.mergeWithDefaults(type, userValue)
}
```

### 批量操作

```typescript
const { formData, updateFields } = useFormInitializer(formConfig)

// 批量更新多个字段
updateFields({
  name: '新姓名',
  age: 30,
  skills: ['React', 'Node.js'],
})
```

## ⚠️ 注意事项

### 1. 类型安全

- 数字输入框的空值是 `null`，不是 `0` 或 `''`
- 多选组件的值必须是数组，即使是空值也是 `[]`
- 复合组件有固定的对象结构，不要随意修改

### 2. 性能优化

- 大表单建议使用 `initializeFormData` 一次性初始化
- 避免频繁调用 `updateField`，建议使用 `updateFields` 批量更新
- 使用 `watchEffect` 时注意避免无限循环

### 3. 向后兼容

- `ComponentUtils.getDefaultValue` 已标记为废弃，建议使用 `getEmptyValue`
- 现有代码可以无缝升级，所有API保持向后兼容

## 📈 性能对比

| 操作         | 传统方式     | 新方式               | 性能提升       |
| ------------ | ------------ | -------------------- | -------------- |
| 表单初始化   | 手动逐个设置 | `initializeFormData` | 3-5x           |
| 类型安全检查 | 运行时错误   | 编译时+运行时检查    | 安全性大幅提升 |
| 数据重置     | 手动清空     | `resetToEmpty`       | 2x             |
| 变化监控     | 手动对比     | `getChangedFields`   | 4x             |

## 🎉 总结

新的数据初始化机制为 YtWidget 组件库提供了：

1. **开发效率提升**：自动化初始化，减少手动代码
2. **类型安全保障**：编译时和运行时双重类型检查
3. **用户体验优化**：传入值优先，符合直觉
4. **维护成本降低**：统一的初始化规范和工具

立即开始使用新的初始化功能，让您的表单开发更加高效！
