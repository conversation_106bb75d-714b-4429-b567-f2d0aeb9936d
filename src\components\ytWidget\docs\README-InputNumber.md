# YtInputNumber 计数器组件

基于 [Wot Design Uni InputNumber](https://wot-design-uni.netlify.app/component/input-number.html) 的二次封装组件，提供表单中的数值输入功能。

## 功能特性

- ✅ 完整的 TypeScript 类型支持
- ✅ 双向数据绑定 (v-model)
- ✅ 支持表单验证配置
- ✅ 支持最小值/最大值限制
- ✅ 支持步长设置
- ✅ 支持小数精度控制
- ✅ 支持禁用状态
- ✅ 支持无输入框模式
- ✅ 支持禁用输入框（只能点击按钮）
- ✅ 支持允许空值
- ✅ 支持长按加减
- ✅ 支持异步变更控制

## 基本用法

```vue
<template>
  <yt-input-number v-model="count" :item="countConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtInputNumber from '@/components/ytWidget/yt-input-number.vue'
import type { FormItem } from '@/types/form'

const count = ref(1)

const countConfig: FormItem = {
  type: 'inputNumber',
  field: 'count',
  title: '数量',
  info: '请输入数量',
  $required: false,
  props: {
    min: 1,
    max: 100,
    step: 1,
    precision: 0
  }
}
</script>
```

## 配置选项

### FormItem 配置

```typescript
interface FormItem {
  type: 'inputNumber'
  field: string // 字段名
  title: string // 标签文本
  info?: string // 占位符文本
  $required?: boolean | string // 是否必填
  props?: {
    // 数值控制
    min?: number // 最小值（仅在明确设置时生效）
    max?: number // 最大值（仅在明确设置时生效）
    step?: number // 步长（仅在明确设置时生效）
    precision?: number // 小数精度（仅在明确设置时生效）

    // 状态控制
    disabled?: boolean // 是否禁用
    allowNull?: boolean // 是否允许空值，默认false

    // 输入控制
    disableInput?: boolean // 禁用输入框，默认false
    withoutInput?: boolean // 不显示输入框，默认false
    disablePlus?: boolean // 禁用增加按钮，默认false
    disableMinus?: boolean // 禁用减少按钮，默认false

    // 样式配置
    inputWidth?: string // 输入框宽度，默认'36px'
    placeholder?: string // 占位符

    // 交互控制
    longPress?: boolean // 支持长按，默认false
    stepStrictly?: boolean // 严格步数倍数，默认false
    immediateChange?: boolean // 立即响应输入变化，默认true
    updateOnInit?: boolean // 初始化时更新v-model，默认true

    // 输入框配置
    inputType?: 'number' | 'digit' // 输入框类型，默认'digit'
    adjustPosition?: boolean // 键盘弹起时自动上推页面，默认true

    // 回调函数
    beforeChange?: (value: number | string) => boolean | Promise<boolean> // 异步变更控制
  }
}
```

## 默认值行为

组件的初始值遵循以下优先级：

1. **明确传入的 modelValue**：如果父组件传入了具体值，则使用该值
2. **allowNull 为 true**：允许空值时，初始值为空字符串 `''`
3. **设置了 min 值**：如果明确设置了 `min` 属性，初始值为 `min` 值
4. **其他情况**：初始值为空字符串 `''`，让 wot-design-uni 组件处理默认行为

> ⚠️ **重要提醒**: 组件不会强制设置默认值为1，除非明确配置了 `min: 1`

## 使用示例

### 基础计数器

```vue
<yt-input-number
  v-model="basicValue"
  :item="{
    title: '基础计数器',
    type: 'inputNumber',
    field: 'basic',
  }"
/>
```

### 设置范围和步长

```vue
<yt-input-number
  v-model="rangeValue"
  :item="{
    title: '数量选择',
    type: 'inputNumber',
    field: 'range',
    props: {
      min: 1,
      max: 10,
      step: 2,
      placeholder: '1-10之间，步长为2',
    },
  }"
/>
```

### 小数精度

```vue
<yt-input-number
  v-model="priceValue"
  :item="{
    title: '价格设置',
    type: 'inputNumber',
    field: 'price',
    props: {
      precision: 2,
      step: 0.1,
      min: 0,
      placeholder: '保留2位小数',
    },
  }"
/>
```

### 无输入框模式

```vue
<yt-input-number
  v-model="buttonOnlyValue"
  :item="{
    title: '点击调节',
    type: 'inputNumber',
    field: 'buttonOnly',
    props: {
      withoutInput: true,
      min: 1,
      max: 5,
    },
  }"
/>
```

### 禁用输入框

```vue
<yt-input-number
  v-model="disableInputValue"
  :item="{
    title: '只能点击按钮',
    type: 'inputNumber',
    field: 'disableInput',
    props: {
      disableInput: true,
      min: 1,
    },
  }"
/>
```

### 允许空值

```vue
<yt-input-number
  v-model="nullableValue"
  :item="{
    title: '可选数量',
    type: 'inputNumber',
    field: 'nullable',
    props: {
      allowNull: true,
      placeholder: '不限制', // 当allowNull为true时，默认placeholder为'不限'
      min: 0,
    },
  }"
/>
```

### 长按支持

```vue
<yt-input-number
  v-model="longPressValue"
  :item="{
    title: '支持长按',
    type: 'inputNumber',
    field: 'longPress',
    props: {
      longPress: true, // 需要显式设置为true
      min: 0,
      max: 100,
    },
  }"
/>
```

### 禁用单个按钮

```vue
<!-- 禁用增加按钮 -->
<yt-input-number
  v-model="noPlusValue"
  :item="{
    title: '无增加按钮',
    type: 'inputNumber',
    field: 'noPlus',
    props: {
      disablePlus: true,
      max: 10,
    },
  }"
/>

<!-- 禁用减少按钮 -->
<yt-input-number
  v-model="noMinusValue"
  :item="{
    title: '无减少按钮',
    type: 'inputNumber',
    field: 'noMinus',
    props: {
      disableMinus: true,
      min: 1,
    },
  }"
/>
```

### 非立即更新模式

```vue
<yt-input-number
  v-model="delayedValue"
  :item="{
    title: '失焦时更新',
    type: 'inputNumber',
    field: 'delayed',
    props: {
      immediateChange: false, // 仅在失焦和按钮点击时触发更新
    },
  }"
/>
```

### 严格步数模式

```vue
<yt-input-number
  v-model="strictValue"
  :item="{
    title: '严格步数倍数',
    type: 'inputNumber',
    field: 'strict',
    props: {
      step: 5,
      stepStrictly: true, // 强制输入值为step的倍数
      min: 0,
    },
  }"
/>
```

### 输入框类型配置

```vue
<yt-input-number
  v-model="numberTypeValue"
  :item="{
    title: '数字输入类型',
    type: 'inputNumber',
    field: 'numberType',
    props: {
      inputType: 'number', // 使用number类型而非默认的digit
      precision: 2,
    },
  }"
/>
```

### 异步变更控制

```vue
<yt-input-number
  v-model="asyncValue"
  :item="{
    title: '异步验证',
    type: 'inputNumber',
    field: 'async',
    props: {
      beforeChange: async (value) => {
        // 模拟异步验证
        await new Promise((resolve) => setTimeout(resolve, 500))
        if (value > 50) {
          uni.showToast({ title: '值不能超过50', icon: 'none' })
          return false
        }
        return true
      },
    },
  }"
/>
```

## 事件处理

```vue
<template>
  <yt-input-number
    v-model="value"
    :item="config"
    @change="handleChange"
    @focus="handleFocus"
    @blur="handleBlur"
  />
</template>

<script setup>
const handleChange = (value) => {
  console.log('数值变化:', value)
}

const handleFocus = (event) => {
  console.log('获得焦点:', event)
}

const handleBlur = (event) => {
  console.log('失去焦点:', event)
}
</script>
```

## 表单集成

在动态表单中使用：

```vue
<template>
  <wd-form :model="formData">
    <template v-for="item in formItems" :key="item.field">
      <yt-input-number
        v-if="item.type === 'inputNumber'"
        v-model="formData[item.field]"
        :item="item"
      />
    </template>
  </wd-form>
</template>
```

## Props

| 属性名      | 类型             | 必填 | 默认值 | 说明                   |
| ----------- | ---------------- | ---- | ------ | ---------------------- |
| item        | FormItem         | 是   | -      | 表单项配置对象         |
| modelValue  | number \| string | 否   | 1      | 计数器的值             |
| label       | string           | 否   | ''     | 标签文本（优先级最高） |
| placeholder | string           | 否   | ''     | 占位符（优先级最高）   |
| required    | boolean          | 否   | false  | 是否必填（优先级最高） |
| min         | number           | 否   | -      | 最小值（优先级最高）   |
| max         | number           | 否   | -      | 最大值（优先级最高）   |
| step        | number           | 否   | -      | 步长（优先级最高）     |
| precision   | number           | 否   | -      | 精度（优先级最高）     |

## Events

| 事件名            | 说明           | 参数                    |
| ----------------- | -------------- | ----------------------- |
| update:modelValue | 值变化时触发   | value: number \| string |
| change            | 值变化时触发   | value: number \| string |
| focus             | 获得焦点时触发 | event: FocusEvent       |
| blur              | 失去焦点时触发 | event: BlurEvent        |

## 样式定制

组件支持通过CSS变量进行样式定制，具体请参考 [Wot Design Uni 主题定制](https://wot-design-uni.netlify.app/guide/theme.html)。

## 注意事项

1. **数值类型**: modelValue 支持 number 和 string 类型，但建议使用 number
2. **精度控制**: 使用 precision 属性时，确保 step 值与精度匹配
3. **范围验证**: min/max 会自动约束输入值的范围
4. **空值处理**: 启用 allowNull 时，可以传入空字符串
5. **异步变更**: beforeChange 函数可以返回 Promise 进行异步验证
