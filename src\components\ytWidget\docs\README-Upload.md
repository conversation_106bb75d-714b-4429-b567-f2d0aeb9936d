# YtUpload 上传组件

基于 [Wot Design Uni Upload](https://wot-design-uni.netlify.app/component/upload.html) 的二次封装组件，提供文件上传功能。

## 功能特性

- ✅ **自动上传**: 选择图片后立即自动上传到服务器
- ✅ **图片回显**: 上传成功后自动显示图片预览
- ✅ **实时状态**: 显示上传进度、成功状态和错误提示
- ✅ 完整的 TypeScript 类型支持
- ✅ 双向数据绑定 (v-model)
- ✅ 支持表单验证配置
- ✅ 支持多种文件类型 (图片/视频/文档/全部)
- ✅ 支持单选和多选上传
- ✅ 支持最大上传数量限制
- ✅ 支持文件大小限制
- ✅ 支持文件扩展名过滤
- ✅ 支持禁用状态
- ✅ 支持覆盖上传模式
- ✅ 支持自定义上传按钮
- ✅ 支持上传前验证
- ✅ 支持预览拦截
- ✅ 支持自定义上传方法
- ✅ 完整的事件回调处理

## 基本用法

```vue
<template>
  <yt-upload v-model="fileList" :item="uploadConfig" />
</template>

<script setup>
import { ref } from 'vue'
import YtUpload from '@/components/ytWidget/yt-upload.vue'
import type { FormItem } from '@/types/form'

const fileList = ref([])

const uploadConfig: FormItem = {
  type: 'upload',
  field: 'files',
  title: '文件上传',
  info: '请选择文件',
  $required: false,
  props: {
    // action 可选，不配置则自动使用 VITE_SERVER_BASEURL + '/infra/file/upload'
    accept: 'image',
    multiple: true,
    limit: 5,
    maxSize: 10
  }
}
</script>
```

## 自动上传和图片回显

组件默认启用自动上传功能，工作流程如下：

### 📱 上传流程

1. **选择图片**: 用户点击上传按钮选择图片
2. **自动上传**: 选择完成后立即开始上传到 `${VITE_SERVER_BASEURL}/infra/file/upload`
3. **进度显示**: 实时显示上传进度条
4. **成功回显**: 上传成功后自动显示图片预览
5. **双向绑定**: 文件信息自动同步到 v-model 数据

### 🎯 核心特性

```javascript
// 自动上传 (默认开启)
autoUpload: true // 选择文件后立即上传

// 图片回显 (自动处理)
// 上传成功后，wot-design-uni自动处理图片预览
// 支持点击预览大图、删除、重新上传

// 状态管理 (实时更新)
// - 上传进度条
// - 成功/失败状态图标
// - 错误提示
```

### 📡 服务器响应格式

上传接口应返回以下格式的数据：

```json
{
  "code": 200,
  "data": {
    "url": "https://example.com/uploads/image.jpg",
    "name": "image.jpg",
    "size": 1024
  },
  "message": "上传成功"
}
```

### 🔧 禁用自动上传

如需禁用自动上传，手动控制上传时机：

```vue
<yt-upload
  v-model="files"
  :item="{
    title: '手动上传',
    props: {
      autoUpload: false, // 禁用自动上传
    },
  }"
  ref="uploadRef"
/>

<!-- 手动触发上传 -->
<wd-button @click="$refs.uploadRef.submit()">开始上传</wd-button>
```

## 默认上传地址

组件会自动使用环境变量构建默认上传地址：

- **默认地址**: `${VITE_SERVER_BASEURL}/infra/file/upload`
- **环境变量**: 使用 `getEvnBaseUrl()` 函数获取当前环境的服务器地址
- **自定义地址**: 如果需要使用自定义上传地址，可以在 `props.action` 中指定

```typescript
// 默认行为 - 自动构建上传地址
const defaultConfig = {
  type: 'upload',
  field: 'files',
  title: '文件上传',
  props: {
    // 不配置 action，使用默认地址
    accept: 'image',
  },
}

// 自定义上传地址
const customConfig = {
  type: 'upload',
  field: 'files',
  title: '文件上传',
  props: {
    action: 'https://custom-api.com/upload', // 自定义上传地址
    accept: 'image',
  },
}
```

## 配置选项

### FormItem 配置

```typescript
interface FormItem {
  type: 'upload'
  field: string // 字段名
  title: string // 标签文本
  info?: string // 占位符文本
  $required?: boolean | string // 是否必填
  props?: {
    // 基础配置
    action?: string // 上传地址
    accept?: 'image' | 'video' | 'media' | 'file' | 'all' // 文件类型
    multiple?: boolean // 是否多选，默认false
    limit?: number // 最大上传数量，默认9
    disabled?: boolean // 是否禁用
    reupload?: boolean // 是否覆盖上传

    // 文件限制
    maxSize?: number // 文件大小限制(MB)，默认10
    extension?: string[] // 文件扩展名过滤

    // 显示配置
    imageMode?: string // 图片显示模式，默认'aspectFill'
    placeholder?: string // 占位符文本

    // 视频相关 (当accept为video/media时)
    compressed?: boolean // 是否压缩视频，默认true
    maxDuration?: number // 视频最大时长(秒)，默认60
    camera?: 'front' | 'back' // 摄像头方向，默认'back'

    // 高级配置
    successStatus?: number // 成功状态码，默认200
    autoUpload?: boolean // 是否自动上传，默认true
    beforeUpload?: (params) => boolean | Promise<boolean> // 上传前钩子
    beforePreview?: (params) => boolean | Promise<boolean> // 预览前钩子
    uploadMethod?: (params) => Promise<any> // 自定义上传方法
  }
}
```

## 文件类型说明

| accept值 | 说明       | 平台支持       |
| -------- | ---------- | -------------- |
| image    | 图片文件   | 全平台         |
| video    | 视频文件   | 全平台         |
| media    | 图片和视频 | 仅微信小程序   |
| file     | 文档文件   | 仅微信小程序   |
| all      | 全部类型   | 微信小程序和H5 |

## 使用示例

### 基础图片上传

```vue
<yt-upload
  v-model="images"
  :item="{
    title: '图片上传',
    type: 'upload',
    field: 'images',
    info: '请选择图片',
    props: {
      accept: 'image',
      limit: 3,
      maxSize: 5,
    },
  }"
/>
```

### 多文件上传

```vue
<yt-upload
  v-model="files"
  :item="{
    title: '多文件上传',
    type: 'upload',
    field: 'files',
    props: {
      accept: 'image',
      multiple: true,
      limit: 9,
      maxSize: 10,
    },
  }"
/>
```

### 视频上传

```vue
<yt-upload
  v-model="videos"
  :item="{
    title: '视频上传',
    type: 'upload',
    field: 'videos',
    props: {
      accept: 'video',
      limit: 1,
      maxSize: 100,
      maxDuration: 300, // 5分钟
      compressed: true,
    },
  }"
/>
```

### 文件扩展名限制

```vue
<yt-upload
  v-model="documents"
  :item="{
    title: '文档上传',
    type: 'upload',
    field: 'documents',
    props: {
      accept: 'file',
      extension: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
      multiple: true,
      limit: 5,
    },
  }"
/>
```

### 自定义上传按钮

```vue
<yt-upload
  v-model="customFiles"
  :item="{
    title: '自定义样式',
    type: 'upload',
    field: 'customFiles',
    props: {
      limit: 3,
    },
  }"
>
  <view class="custom-btn">
    <wd-icon name="add-circle" size="24px" />
    <text>选择文件</text>
  </view>
</yt-upload>
```

### 上传前验证

```vue
<yt-upload
  v-model="validatedFiles"
  :item="{
    title: '验证上传',
    type: 'upload',
    field: 'validatedFiles',
    props: {
      beforeUpload: async ({ files, fileList }) => {
        const file = files[0]
        if (file.size > 1024 * 1024) {
          // 1MB
          uni.showToast({ title: '文件过大', icon: 'none' })
          return false
        }
        return true
      },
    },
  }"
/>
```

### 预览拦截

```vue
<yt-upload
  v-model="previewFiles"
  :item="{
    title: '预览控制',
    type: 'upload',
    field: 'previewFiles',
    props: {
      beforePreview: ({ file, index, imgList }) => {
        return new Promise((resolve) => {
          uni.showModal({
            title: '确认预览',
            content: '是否预览该图片？',
            success: (res) => resolve(res.confirm),
          })
        })
      },
    },
  }"
/>
```

### 自定义上传方法

```vue
<yt-upload
  v-model="customUploadFiles"
  :item="{
    title: '自定义上传',
    type: 'upload',
    field: 'customUploadFiles',
    props: {
      autoUpload: false,
      uploadMethod: async (files) => {
        // 自定义上传逻辑
        const formData = new FormData()
        files.forEach((file) => {
          formData.append('files', file)
        })

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        return response.json()
      },
    },
  }"
/>
```

## 事件处理

```vue
<template>
  <yt-upload
    v-model="files"
    :item="config"
    @change="handleChange"
    @success="handleSuccess"
    @fail="handleFail"
    @progress="handleProgress"
    @remove="handleRemove"
    @oversize="handleOversize"
  />
</template>

<script setup>
const handleChange = (fileList) => {
  console.log('文件列表变化:', fileList)
}

const handleSuccess = (event) => {
  console.log('上传成功:', event)
  uni.showToast({ title: '上传成功', icon: 'success' })
}

const handleFail = (event) => {
  console.log('上传失败:', event)
  uni.showToast({ title: '上传失败', icon: 'error' })
}

const handleProgress = (event) => {
  console.log('上传进度:', event)
}

const handleRemove = (event) => {
  console.log('删除文件:', event)
}

const handleOversize = (event) => {
  console.log('文件超大:', event)
  uni.showToast({ title: '文件大小超出限制', icon: 'none' })
}
</script>
```

## 表单集成

在动态表单中使用：

```vue
<template>
  <wd-form :model="formData">
    <template v-for="item in formItems" :key="item.field">
      <yt-upload v-if="item.type === 'upload'" v-model="formData[item.field]" :item="item" />
    </template>
  </wd-form>
</template>
```

## Props

| 属性名      | 类型     | 必填 | 默认值 | 说明                   |
| ----------- | -------- | ---- | ------ | ---------------------- |
| item        | FormItem | 是   | -      | 表单项配置对象         |
| modelValue  | Array    | 否   | []     | 文件列表               |
| label       | string   | 否   | ''     | 标签文本（优先级最高） |
| placeholder | string   | 否   | ''     | 占位符（优先级最高）   |
| required    | boolean  | 否   | false  | 是否必填（优先级最高） |
| action      | string   | 否   | ''     | 上传地址（优先级最高） |
| accept      | string   | 否   | -      | 文件类型（优先级最高） |
| multiple    | boolean  | 否   | -      | 是否多选（优先级最高） |
| limit       | number   | 否   | -      | 数量限制（优先级最高） |
| maxSize     | number   | 否   | -      | 大小限制（优先级最高） |

## Events

| 事件名            | 说明               | 参数                   |
| ----------------- | ------------------ | ---------------------- |
| update:modelValue | 文件列表变化时触发 | fileList: UploadFile[] |
| change            | 文件列表变化时触发 | fileList: UploadFile[] |
| success           | 上传成功时触发     | event: SuccessEvent    |
| fail              | 上传失败时触发     | event: FailEvent       |
| progress          | 上传进度时触发     | event: ProgressEvent   |
| chooseerror       | 选择文件错误时触发 | event: ErrorEvent      |
| remove            | 删除文件时触发     | event: RemoveEvent     |
| oversize          | 文件超大时触发     | event: OversizeEvent   |

## Slots

| 插槽名        | 说明               | 作用域参数      |
| ------------- | ------------------ | --------------- |
| default       | 自定义上传按钮     | -               |
| preview-cover | 自定义预览覆盖内容 | { file, index } |

## 样式定制

组件支持通过CSS变量进行样式定制，具体请参考 [Wot Design Uni 主题定制](https://wot-design-uni.netlify.app/guide/theme.html)。

## 注意事项

1. **默认上传地址**: 组件会自动使用 `${VITE_SERVER_BASEURL}/infra/file/upload` 作为默认上传地址
2. **平台兼容性**: 不同的accept类型在不同平台有不同的支持度
3. **文件大小**: maxSize单位为MB，实际限制还需考虑平台和服务器限制
4. **微信小程序隐私**: 在微信小程序中使用需要配置隐私协议
5. **自定义上传地址**: 如需自定义，请在 props.action 中指定
6. **文件类型**: extension过滤仅在支持的平台生效
7. **视频封面**: 视频上传时建议手动处理封面图

## 微信小程序隐私配置

在微信小程序中使用upload组件需要在`app.json`中配置隐私协议：

```json
{
  "permission": {
    "scope.writePhotosAlbum": {
      "desc": "用于保存图片到相册"
    }
  },
  "requiredPrivateInfos": ["chooseImage", "chooseMedia", "chooseVideo"]
}
```
