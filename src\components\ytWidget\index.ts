// YtWidget 动态表单组件库 v2.0 - 主导出文件
// =====================================================

// 🎯 核心系统
export { default as YtDynamicForm } from './core/yt-dynamic-form.vue'
export { default as YtFormItem } from './core/yt-form-item.vue'
export { componentRegistry } from './core/yt-component-registry'

// 🔤 基础输入组件
export { default as YtInput } from './inputs/yt-input.vue'
export { default as YtTextarea } from './inputs/yt-textarea.vue'
export { default as YtInputNumber } from './inputs/yt-input-number.vue'

// 🎯 选择器组件
// 基础选择器
export { default as YtSelect } from './selectors/basic/yt-select.vue'
export { default as YtSelectSingle } from './selectors/basic/yt-select-single.vue'
export { default as YtSelectMulti } from './selectors/basic/yt-select-multi.vue'
export { default as YtCheckbox } from './selectors/basic/yt-checkbox.vue'

// 高级选择器
export { default as YtRegionCascader } from './selectors/advanced/yt-region-cascader.vue'
export { default as YtDictSelect } from './selectors/advanced/yt-dict-select.vue'

// ⚡ 特殊功能组件
// 日期时间
export { default as YtCalendar } from './special/datetime/yt-calendar.vue'
export { default as YtDatetimePicker } from './special/datetime/yt-datetime-picker.vue'

// 文件上传
export { default as YtUpload } from './special/upload/yt-upload.vue'
export { default as YtCheckboxWithUpload } from './special/upload/yt-checkbox-with-upload.vue'

// 地图组件
export { default as YtMapSelector } from './special/map/yt-map-selector.vue'

// 反馈组件
export { default as YtNoticeBar } from './special/feedback/yt-notice-bar.vue'
export { default as YtDivider } from './special/feedback/yt-divider.vue'

// 🧩 复合业务组件
export { default as YtTableForm } from './composites/yt-table-form.vue'
export { default as YtDictSelectWithOther } from './composites/yt-dict-select-with-other.vue'

// 🛠️ 组合式API导出
export {
  useFormInitializer,
  createFormInitializer,
  type FormSnapshot,
  type ValidationResult,
} from './composables/useFormInitializer'

// 📝 类型定义导出
export type {
  FormItem,
  FormItemProps,
  FormConfig,
  FormSelectOption,
  TableColumn,
  UploadFile,
} from '@/types/form'

// 📦 分类导出 - 支持按需加载和Tree Shaking
export const Core = {
  YtDynamicForm: () => import('./core/yt-dynamic-form.vue'),
}

export const Inputs = {
  YtInput: () => import('./inputs/yt-input.vue'),
  YtTextarea: () => import('./inputs/yt-textarea.vue'),
  YtInputNumber: () => import('./inputs/yt-input-number.vue'),
}

export const Selectors = {
  // 基础选择器
  Basic: {
    YtSelect: () => import('./selectors/basic/yt-select.vue'),
    YtSelectSingle: () => import('./selectors/basic/yt-select-single.vue'),
    YtSelectMulti: () => import('./selectors/basic/yt-select-multi.vue'),
    YtCheckbox: () => import('./selectors/basic/yt-checkbox.vue'),
  },
  // 高级选择器
  Advanced: {
    YtRegionCascader: () => import('./selectors/advanced/yt-region-cascader.vue'),
    YtDictSelect: () => import('./selectors/advanced/yt-dict-select.vue'),
  },
}

export const Special = {
  // 日期时间
  DateTime: {
    YtCalendar: () => import('./special/datetime/yt-calendar.vue'),
    YtDatetimePicker: () => import('./special/datetime/yt-datetime-picker.vue'),
  },
  // 文件上传
  Upload: {
    YtUpload: () => import('./special/upload/yt-upload.vue'),
    YtCheckboxWithUpload: () => import('./special/upload/yt-checkbox-with-upload.vue'),
  },
  // 地图相关
  Map: {
    YtMapSelector: () => import('./special/map/yt-map-selector.vue'),
  },
  // 反馈组件
  Feedback: {
    YtNoticeBar: () => import('./special/feedback/yt-notice-bar.vue'),
    YtDivider: () => import('./special/feedback/yt-divider.vue'),
  },
}

export const Composites = {
  YtTableForm: () => import('./composites/yt-table-form.vue'),
  YtDictSelectWithOther: () => import('./composites/yt-dict-select-with-other.vue'),
}

// 🚀 便捷导出集合 - 向后兼容
export const FormControls = Inputs
export const SelectionComponents = { ...Selectors.Basic, ...Selectors.Advanced }
export const DateTimeComponents = Special.DateTime
export const UploadComponents = Special.Upload
export const ComplexComponents = { ...Special.Map, ...Composites }

// 🎯 组件类型常量
export const ComponentTypes = {
  // 核心
  DYNAMIC_FORM: 'DynamicForm',

  // 基础输入
  INPUT: 'input',
  TEXTAREA: 'textarea',
  INPUT_NUMBER: 'inputNumber',

  // 选择器
  SELECT: 'select',
  SELECT_SINGLE: 'select-single',
  SELECT_MULTI: 'select-multi',
  CHECKBOX: 'checkbox',
  REGION_CASCADER: 'RegionCascader',
  DICT_SELECT: 'dict-select',

  // 特殊功能
  CALENDAR: 'calendar',
  DATETIME_PICKER: 'datetime-picker',
  UPLOAD: 'upload',
  MAP_SELECTOR: 'MapSelector',
  NOTICE_BAR: 'NoticeBar',
  DIVIDER: 'divider',

  // 复合组件
  CHECKBOX_WITH_UPLOAD: 'DynamicCheckboxWithUpload',
  INPUT_MAP: 'tableForm',
  DICT_SELECT_WITH_OTHER: 'DictSelectWithOther',
} as const

// 🔧 工具函数
export const ComponentUtils = {
  /**
   * 根据组件类型获取类型安全的空值
   */
  getEmptyValue: (type: string): any => {
    const emptyValues: Record<string, any> = {
      // 基础输入组件 - 空字符串
      [ComponentTypes.INPUT]: '',
      [ComponentTypes.TEXTAREA]: '',

      // 数字输入 - null（更符合数字输入的语义）
      [ComponentTypes.INPUT_NUMBER]: null,

      // 单选选择器 - 空字符串
      [ComponentTypes.SELECT]: '',
      [ComponentTypes.SELECT_SINGLE]: '',
      [ComponentTypes.DICT_SELECT]: '',

      // 多选选择器 - 空数组
      [ComponentTypes.SELECT_MULTI]: [],

      // 复选框组 - 空数组（统一数组格式）
      [ComponentTypes.CHECKBOX]: [],

      // 🚀 级联选择器 - 层级对象格式
      [ComponentTypes.REGION_CASCADER]: {},

      // 日期时间组件 - null
      [ComponentTypes.CALENDAR]: null,
      [ComponentTypes.DATETIME_PICKER]: null,

      // 上传组件 - 空数组
      [ComponentTypes.UPLOAD]: [],

      // 地图组件 - null
      [ComponentTypes.MAP_SELECTOR]: null,

      // 反馈组件 - 空字符串或null
      [ComponentTypes.NOTICE_BAR]: '',
      [ComponentTypes.DIVIDER]: null,

      // 复合组件 - 结构化空值
      [ComponentTypes.CHECKBOX_WITH_UPLOAD]: {
        checked: false,
        images: [],
      },
      [ComponentTypes.INPUT_MAP]: [],
      [ComponentTypes.DICT_SELECT_WITH_OTHER]: {
        otherText: '',
        selectType: 'checkbox',
        selected: [],
      },
    }

    return emptyValues[type] !== undefined ? emptyValues[type] : null
  },

  /**
   * 根据组件类型获取默认值 - 重构版本，现在返回空值
   * @deprecated 使用 getEmptyValue 替代
   */
  getDefaultValue: (type: string) => {
    console.warn('[ComponentUtils.getDefaultValue] 此方法已废弃，请使用 getEmptyValue')
    return ComponentUtils.getEmptyValue(type)
  },

  /**
   * 合并用户值与默认值（优先级处理）
   * @param type 组件类型
   * @param userValue 用户传入的值（可选）
   * @returns 合并后的值，优先使用用户值
   */
  mergeWithDefaults: (type: string, userValue?: any): any => {
    const emptyValue = ComponentUtils.getEmptyValue(type)

    // 用户值存在且不为undefined时，优先使用用户值
    if (userValue !== undefined && userValue !== null) {
      // 对于特殊类型，需要进行类型验证和转换
      switch (type) {
        case ComponentTypes.SELECT_MULTI:
        case ComponentTypes.UPLOAD:
        case ComponentTypes.CHECKBOX:
          // 确保多选/数组类型返回数组
          return Array.isArray(userValue) ? userValue : [userValue]

        // 🚀 区域级联选择器 - 层级对象格式处理
        case ComponentTypes.REGION_CASCADER: {
          // 确保返回层级对象格式
          if (typeof userValue === 'object' && userValue !== null && !Array.isArray(userValue)) {
            // 验证层级对象的基本结构
            const validLevels = ['town', 'village', 'community']
            const filteredValue: any = {}

            validLevels.forEach((level) => {
              if (userValue[level] && typeof userValue[level] === 'string') {
                filteredValue[level] = userValue[level]
              }
            })

            return filteredValue
          }

          // 如果是数组格式（向后兼容），转换为层级对象
          if (Array.isArray(userValue)) {
            console.warn(
              '[ComponentUtils.mergeWithDefaults] RegionCascader: 检测到数组格式，自动转换为层级对象格式',
            )
            const levels = ['town', 'village', 'community']
            const hierarchyValue: any = {}

            userValue.forEach((value, index) => {
              if (levels[index] && value) {
                hierarchyValue[levels[index]] = value
              }
            })

            return hierarchyValue
          }

          return emptyValue
        }

        case ComponentTypes.INPUT_NUMBER: {
          // 确保数字输入返回数字或null
          if (userValue === '' || userValue === null) return null
          const numValue = Number(userValue)
          return isNaN(numValue) ? null : numValue
        }

        case ComponentTypes.CHECKBOX_WITH_UPLOAD: {
          // 确保复合组件返回正确结构
          if (typeof userValue === 'object' && userValue !== null) {
            return {
              checked: userValue.checked !== undefined ? userValue.checked : false,
              images: Array.isArray(userValue.images) ? userValue.images : [],
            }
          }
          return emptyValue
        }

        case ComponentTypes.DICT_SELECT_WITH_OTHER: {
          // 确保字典+其他选择器返回正确结构
          if (typeof userValue === 'object' && userValue !== null) {
            return {
              otherText: userValue.otherText !== undefined ? userValue.otherText : '',
              selectType: userValue.selectType !== undefined ? userValue.selectType : 'checkbox',
              selected: Array.isArray(userValue.selected) ? userValue.selected : [],
            }
          }
          return emptyValue
        }

        default:
          return userValue
      }
    }

    // 用户值不存在时，返回空值
    return emptyValue
  },

  /**
   * 批量初始化表单数据
   * @param config 表单配置数组
   * @param userModel 用户传入的初始数据（可选）
   * @returns 初始化后的表单数据对象
   */
  initializeFormData: (config: any[], userModel: Record<string, any> = {}): Record<string, any> => {
    const formData: Record<string, any> = {}

    config.forEach((item) => {
      if (item.field) {
        // 获取用户传入的值
        const userValue = userModel[item.field]

        // 使用优先级合并
        formData[item.field] = ComponentUtils.mergeWithDefaults(item.type, userValue)

        console.log(`📋 初始化字段 [${item.field}]:`, {
          type: item.type,
          userValue,
          finalValue: formData[item.field],
        })
      }
    })

    return formData
  },

  /**
   * 重置表单数据到空值状态
   * @param config 表单配置数组
   * @returns 重置后的空值数据对象
   */
  resetToEmpty: (config: any[]): Record<string, any> => {
    const emptyData: Record<string, any> = {}

    config.forEach((item) => {
      if (item.field) {
        emptyData[item.field] = ComponentUtils.getEmptyValue(item.type)
      }
    })

    return emptyData
  },

  /**
   * 检查值是否为空（根据组件类型判断）
   * @param type 组件类型
   * @param value 要检查的值
   * @returns 是否为空
   */
  isEmpty: (type: string, value: any): boolean => {
    const emptyValue = ComponentUtils.getEmptyValue(type)

    // 基本空值检查
    if (value === null || value === undefined) return true

    // 根据类型进行详细检查
    switch (type) {
      case ComponentTypes.INPUT:
      case ComponentTypes.TEXTAREA:
      case ComponentTypes.SELECT:
      case ComponentTypes.SELECT_SINGLE:
      case ComponentTypes.DICT_SELECT:
        return value === ''

      case ComponentTypes.INPUT_NUMBER:
        return value === null || value === ''

      case ComponentTypes.SELECT_MULTI:
      case ComponentTypes.UPLOAD:
      case ComponentTypes.CHECKBOX:
        return Array.isArray(value) && value.length === 0

      // 🚀 区域级联选择器 - 层级对象格式检查
      case ComponentTypes.REGION_CASCADER: {
        if (typeof value !== 'object' || value === null) return true

        // 检查是否所有层级都为空
        const levels = ['town', 'village', 'community']
        return levels.every((level) => !value[level] || value[level] === '')
      }

      case ComponentTypes.CHECKBOX_WITH_UPLOAD:
        return value?.checked === false && (!value?.images || value.images.length === 0)

      case ComponentTypes.DICT_SELECT_WITH_OTHER:
        return (
          Array.isArray(value?.selected) && value.selected.length === 0 && value?.otherText === ''
        )

      default:
        // 深度比较空值
        return JSON.stringify(value) === JSON.stringify(emptyValue)
    }
  },

  /**
   * 验证值的类型是否正确
   * @param type 组件类型
   * @param value 要验证的值
   * @returns 类型是否正确
   */
  validateValueType: (type: string, value: any): boolean => {
    switch (type) {
      case ComponentTypes.INPUT:
      case ComponentTypes.TEXTAREA:
      case ComponentTypes.SELECT:
      case ComponentTypes.SELECT_SINGLE:
      case ComponentTypes.DICT_SELECT:
        return typeof value === 'string'

      case ComponentTypes.INPUT_NUMBER:
        return value === null || typeof value === 'number'

      case ComponentTypes.SELECT_MULTI:
      case ComponentTypes.UPLOAD:
      case ComponentTypes.CHECKBOX:
        return Array.isArray(value)

      // 🚀 区域级联选择器 - 层级对象格式验证
      case ComponentTypes.REGION_CASCADER: {
        if (value === null || value === undefined) return true // 空值是允许的

        if (typeof value !== 'object' || Array.isArray(value)) return false

        // 验证层级对象的结构
        const validLevels = ['town', 'village', 'community']
        const valueKeys = Object.keys(value)

        // 检查是否只包含有效的层级字段
        const hasValidStructure = valueKeys.every((key) => validLevels.includes(key))

        // 检查值是否都是字符串类型（如果存在的话）
        const hasValidValues = valueKeys.every(
          (key) =>
            value[key] === undefined || value[key] === null || typeof value[key] === 'string',
        )

        return hasValidStructure && hasValidValues
      }

      case ComponentTypes.CALENDAR:
      case ComponentTypes.DATETIME_PICKER:
        return value === null || typeof value === 'number' || typeof value === 'string'

      case ComponentTypes.CHECKBOX_WITH_UPLOAD:
        return (
          typeof value === 'object' &&
          value !== null &&
          'checked' in value &&
          'images' in value &&
          typeof value.checked === 'boolean' &&
          Array.isArray(value.images)
        )

      case ComponentTypes.DICT_SELECT_WITH_OTHER:
        return (
          typeof value === 'object' &&
          value !== null &&
          'otherText' in value &&
          'selectType' in value &&
          'selected' in value &&
          typeof value.otherText === 'string' &&
          (value.selectType === 'checkbox' || value.selectType === 'radio') &&
          Array.isArray(value.selected)
        )

      default:
        return true // 未知类型不验证
    }
  },

  /**
   * 检查组件类型是否为选择器类型
   */
  isSelector: (type: string) => {
    return [
      ComponentTypes.SELECT,
      ComponentTypes.SELECT_SINGLE,
      ComponentTypes.SELECT_MULTI,
      ComponentTypes.REGION_CASCADER,
      ComponentTypes.DICT_SELECT,
    ].includes(type as any)
  },

  /**
   * 检查组件类型是否为输入类型
   */
  isInput: (type: string) => {
    return [ComponentTypes.INPUT, ComponentTypes.TEXTAREA, ComponentTypes.INPUT_NUMBER].includes(
      type as any,
    )
  },

  /**
   * 获取组件分类
   */
  getCategory: (type: string) => {
    if (ComponentUtils.isInput(type)) return 'inputs'
    if (ComponentUtils.isSelector(type)) return 'selectors'
    if ([ComponentTypes.CALENDAR, ComponentTypes.DATETIME_PICKER].includes(type as any))
      return 'datetime'
    if ([ComponentTypes.UPLOAD].includes(type as any)) return 'upload'
    if ([ComponentTypes.MAP_SELECTOR].includes(type as any)) return 'map'
    return 'composites'
  },
}

// 📊 组件统计信息
export const ComponentStats = {
  totalComponents: 17,
  categories: {
    core: 1,
    inputs: 3,
    selectors: 6,
    special: 7,
    composites: 2,
  },
  version: '2.0.0',
  lastUpdate: '',
}
