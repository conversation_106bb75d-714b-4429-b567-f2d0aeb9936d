<script lang="ts" setup>
import { computed, defineEmits, defineProps } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: string | number
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: string | number]
}>()

// 🚀 使用 computed setter/getter 模式 - 更优雅，无循环问题
const inputValue = computed({
  get: () => props.modelValue || '',
  set: (value: string | number) => {
    emits('update:modelValue', value)
  },
})

// 计算是否必填
const isRequired = computed(() => {
  if (typeof props.item.$required === 'boolean') {
    return props.item.$required
  }
  return !!props.item.$required // 字符串值转布尔
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})
</script>

<template>
  <wd-input
    v-model="inputValue"
    :clearable="item.props?.clearable"
    :disabled="item.props?.disabled"
    :error-message="errorMessage"
    :label="item.title"
    :maxlength="item.props?.maxlength"
    :minlength="item.props?.minlength"
    :placeholder="item.props?.placeholder"
    :prefix-icon="item.props?.prefixIcon"
    :prop="item.field"
    :readonly="item.props?.readonly"
    :required="isRequired"
    :show-password="item.props?.showPassword"
    :suffix-icon="item.props?.suffixIcon"
    custom-input-class="yt-input-textAlign"
    label-width="50%"
  />
  <view v-if="item.info" class="notice-bar">
    <wd-notice-bar :scrollable="false" :text="item.info" prefix="warn-bold" type="info" wrapable />
  </view>
</template>

<style lang="scss" scoped>
.yt-input-textAlign {
  text-align: left;
}
.notice-bar {
  padding: 20rpx;
}
</style>
