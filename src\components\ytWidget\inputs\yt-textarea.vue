<script lang="ts" setup>
import { computed, defineEmits, defineProps } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: string
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: string]
}>()

// 🚀 使用 computed setter/getter 模式 - 更优雅，无循环问题
const textareaValue = computed({
  get: () => props.modelValue || '',
  set: (value: string) => {
    emits('update:modelValue', value)
  },
})

// 计算是否必填
const isRequired = computed(() => {
  if (typeof props.item.$required === 'boolean') {
    return props.item.$required
  }
  return !!props.item.$required // 字符串值转布尔
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})
</script>

<template>
  <wd-cell :title="item.title" vertical class="textarea-cell">
    <wd-textarea
      v-model="textareaValue"
      :autosize="item.props?.autosize"
      :clearable="item.props?.clearable"
      :disabled="item.props?.disabled"
      :error-message="errorMessage"
      :maxlength="item.props?.maxlength"
      :minlength="item.props?.minlength"
      :placeholder="item.props?.placeholder || '请输入内容...'"
      :prop="item.field"
      :readonly="item.props?.readonly"
      :required="isRequired"
      :rows="item.props?.rows || 4"
      :show-count="item.props?.showCount !== false"
      vertical
      class="beautiful-textarea"
    />
  </wd-cell>
</template>

<style lang="scss" scoped>
// 样式已移至全局 src/style/index.scss 中
</style>
