# 行政区域选择器 - 层级数据结构说明

## 概述

行政区域选择器 (`yt-region-cascader`) 专门使用层级数据结构，支持三级行政区域选择：镇/街道 → 村/社区 → 小区/组。

## 层级数据结构

```typescript
interface RegionHierarchy {
  town?: string // 镇/街道代码
  village?: string // 村/社区代码
  community?: string // 小区/组代码
}
```

## 使用方法

### 1. 基本配置

```typescript
const formItem: FormItem = {
  type: 'region-cascader',
  key: 'residential_complex',
  title: '居住区域',
  props: {
    rootParentCode: '222400', // 根级区域代码
    placeholder: '请选择居住区域',
  },
}
```

### 2. 数据绑定

```vue
<template>
  <yt-region-cascader v-model="formData.residential_complex" :item="regionFormItem" />
</template>

<script setup>
import { ref } from 'vue'

// 层级数据格式
const formData = ref({
  residential_complex: {
    town: '222401001000',
    village: '222401001001',
    community: '222401001001',
  },
})
</script>
```

### 3. 处理选择结果

```typescript
// 监听变化
watch(
  () => formData.value.residential_complex,
  (newValue) => {
    if (newValue) {
      console.log('选中的镇:', newValue.town)
      console.log('选中的村:', newValue.village)
      console.log('选中的社区:', newValue.community)
    }
  },
)
```

## 配置选项

| 属性             | 类型      | 默认值     | 说明                 |
| ---------------- | --------- | ---------- | -------------------- |
| `rootParentCode` | `string`  | `'222400'` | 根级区域代码         |
| `autoComplete`   | `boolean` | `true`     | 是否自动补全初始数据 |
| `placeholder`    | `string`  | `'请选择'` | 占位符文本           |
| `disabled`       | `boolean` | `false`    | 是否禁用             |
| `readonly`       | `boolean` | `false`    | 是否只读             |

## 层级映射

选择器按以下层级顺序映射：

1. **第一级** → `town` (镇/街道)
2. **第二级** → `village` (村/社区)
3. **第三级** → `community` (小区/组)

## 数据示例

### 初始化数据

```typescript
// 完整的三级选择
const fullData = {
  town: '222401001000',
  village: '222401001001',
  community: '222401001001',
}

// 部分选择（只选到村级）
const partialData = {
  town: '222401001000',
  village: '222401001001',
}

// 空数据
const emptyData = {}
```

### 选择后返回的数据

组件会根据用户的实际选择返回对应的层级数据：

```typescript
// 用户选择了完整的三级
{
  town: '222401001000',
  village: '222401001001',
  community: '222401001001'
}

// 用户只选择了两级
{
  town: '222401001000',
  village: '222401001001'
}
```

## 完整示例

```vue
<template>
  <div class="region-demo">
    <h3>行政区域选择</h3>
    <yt-region-cascader v-model="regionData" :item="formItem" />

    <div class="result">
      <h4>选择结果：</h4>
      <div v-if="regionData.town">
        <p>
          <strong>镇/街道：</strong>
          {{ regionData.town }}
        </p>
      </div>
      <div v-if="regionData.village">
        <p>
          <strong>村/社区：</strong>
          {{ regionData.village }}
        </p>
      </div>
      <div v-if="regionData.community">
        <p>
          <strong>小区/组：</strong>
          {{ regionData.community }}
        </p>
      </div>
      <pre>{{ JSON.stringify(regionData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

// 初始数据
const regionData = ref({
  town: '222401001000',
  village: '222401001001',
  community: '222401001001',
})

// 表单配置
const formItem = {
  type: 'region-cascader',
  key: 'region',
  title: '居住区域',
  props: {
    rootParentCode: '222400',
    placeholder: '请选择居住区域',
  },
}

// 监听数据变化
watch(
  regionData,
  (newValue) => {
    console.log('区域选择发生变化:', newValue)
  },
  { deep: true },
)
</script>

<style scoped>
.region-demo {
  padding: 20px;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
```

## 显示格式

组件会智能显示选中的层级信息：

- 显示格式：`某某镇 / 某某村 / 某某社区`
- 根据实际选择的层级数量动态调整显示内容
- 自动添加层级标识（镇、村、社区）便于识别

## 注意事项

1. **数据类型**：`v-model` 必须绑定 `RegionHierarchy` 对象类型
2. **初始化数据**：支持完整或部分的层级数据作为初始值
3. **选择行为**：用户可以在任意层级完成选择，不强制选择到最后一级
4. **API 兼容性**：后端接口保持不变，组件内部处理数据转换
5. **性能优化**：自动限制最大选择层级为 3 级，避免无限级联

## 更新日志

- **2025-07-01 16:23:32 +08:00**: 简化为单一层级数据结构
  - 移除多模式支持，专注层级结构
  - 优化初始化和选择逻辑
  - 改进显示格式和用户体验
  - 确保数据一致性
