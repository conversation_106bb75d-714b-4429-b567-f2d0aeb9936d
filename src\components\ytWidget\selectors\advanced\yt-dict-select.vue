<script lang="ts" setup>
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import { type DictDataType, getDictOptions } from '@/utils/dict'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: string | number | string[] | number[]
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: string | number | string[] | number[]]
  cancel: []
  open: []
  close: []
  clear: []
}>()

// 字典数据
const dictData = ref<DictDataType[]>([])
const loading = ref(false)

// 计算选择器类型 (checkbox/radio/select)
const selectType = computed(() => {
  const configuredType = props.item.props?.selectType

  // 如果有配置 selectType，验证并返回
  if (configuredType) {
    const validTypes = ['checkbox', 'radio', 'select']
    if (validTypes.includes(configuredType)) {
      return configuredType
    }
    console.warn(
      `⚠️ YtDictSelect: 无效的 selectType "${configuredType}"，支持的类型: ${validTypes.join(', ')}`,
    )
  }

  // 如果没有配置 selectType，根据 multiple 属性判断（向后兼容）
  return props.item.props?.multiple ? 'checkbox' : 'radio'
})

// 计算是否为多选模式
const isMultipleMode = computed(() => {
  // 只有 checkbox 为多选，radio 和 select 都为单选
  // 如果没有 selectType 字段则默认为单选
  const type = selectType.value
  return type === 'checkbox'
})

// 计算 wd-select-picker 组件的 type 属性
// wd-select-picker 只支持 'checkbox' 和 'radio'，不支持 'select'
const pickerType = computed(() => {
  const type = selectType.value
  // 将 'select' 映射为 'radio'，因为它们都是单选模式
  return type === 'select' ? 'radio' : type
})

// 使用ref和watch实现双向绑定
const getInitialValue = () => {
  const isMultiple = isMultipleMode.value
  const defaultValue = isMultiple ? [] : ''

  if (props.modelValue !== undefined && props.modelValue !== null) {
    // 如果有传入值，根据模式进行处理
    if (isMultiple) {
      // 多选模式：确保返回数组
      const result = Array.isArray(props.modelValue)
        ? props.modelValue
        : props.modelValue
          ? [props.modelValue]
          : []
      return result
    } else {
      // 单选模式：返回单个值
      const result = Array.isArray(props.modelValue)
        ? props.modelValue.length > 0
          ? props.modelValue[0]
          : ''
        : props.modelValue
      return result
    }
  }

  return defaultValue
}

const selectValue = ref(getInitialValue())

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    const isMultiple = isMultipleMode.value

    if (newVal !== undefined && newVal !== null) {
      if (isMultiple) {
        // 多选模式：确保是数组
        selectValue.value = Array.isArray(newVal) ? newVal : newVal ? [newVal] : []
      } else {
        // 单选模式：确保是单个值
        selectValue.value = Array.isArray(newVal) ? (newVal.length > 0 ? newVal[0] : '') : newVal
      }
    } else {
      selectValue.value = isMultiple ? [] : ''
    }
  },
)

// 监听选择器类型变化，重新设置初始值
watch(isMultipleMode, (newIsMultiple) => {
  const currentValue = selectValue.value

  if (newIsMultiple) {
    // 切换到多选模式
    if (!Array.isArray(currentValue)) {
      selectValue.value = currentValue ? [currentValue] : []
    }
  } else {
    // 切换到单选模式
    if (Array.isArray(currentValue)) {
      selectValue.value = currentValue.length > 0 ? currentValue[0] : ''
    }
  }
})

// 监听输入值变化
watch(selectValue, (newVal) => {
  emits('update:modelValue', newVal)
})

// 处理选择事件 - wd-select-picker 的 confirm 事件
const handleConfirm = (event: any) => {
  // wd-select-picker 返回的数据结构: {value, selectedItems}
  let newValue

  if (event && event.value !== undefined) {
    // 从事件对象中取值
    newValue = event.value
  } else {
    // 直接传递的值
    newValue = event
  }

  // 确保多选时是数组，单选时是单个值
  if (isMultipleMode.value) {
    // 多选模式：确保值是数组
    if (!Array.isArray(newValue)) {
      newValue = newValue ? [newValue] : []
    }
  } else {
    // 单选模式：如果是数组，取第一个值
    if (Array.isArray(newValue)) {
      newValue = newValue.length > 0 ? newValue[0] : ''
    }
  }

  selectValue.value = newValue
  emits('update:modelValue', newValue)
}

// 处理change事件
const handleChange = (event: any) => {
  handleConfirm(event)
}

// 计算是否必填
const isRequired = computed(() => {
  return !!props.item.$required
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (isRequired.value && typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})

// 计算占位符
const placeholder = computed(() => {
  return props.item.props?.placeholder || '请选择'
})

// 获取字典类型
const dictType = computed(() => {
  // 从 item.props.dictType 或 item.dictType 中获取字典类型
  return props.item.props?.dictType || props.item.dictType || ''
})

// 计算字典选项
const dictColumns = computed(() => {
  const columns = [...dictData.value]

  // 如果允许空值，添加空选项
  if (props.item.props?.allowEmpty) {
    columns.unshift({
      value: '',
      label: props.item.props?.emptyText || '无',
      dictType: dictType.value,
      colorType: '',
      cssClass: 'dict-empty-option',
    })
  }

  // 转换为 wd-select-picker 需要的格式
  const result = columns.map((option) => ({
    label: option.label,
    value: option.value,
    disabled: false, // 字典数据默认不禁用
  }))

  return result
})

// 加载字典数据
const loadDictData = async () => {
  if (!dictType.value) {
    console.warn('⚠️ YtDictSelect: dictType 不能为空')
    return
  }

  try {
    loading.value = true

    const options = getDictOptions(dictType.value)
    dictData.value = options || []
  } catch (error) {
    console.error('❌ YtDictSelect 字典数据加载失败:', error)
    dictData.value = []

    uni.showToast({
      title: '字典数据加载失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

// 监听字典类型变化，重新加载数据
watch(
  dictType,
  () => {
    if (dictType.value) {
      loadDictData()
    }
  },
  { immediate: true },
)

// 计算标题
const title = computed(() => {
  return props.item.props?.title || `选择${props.item.title}`
})

// 计算是否可搜索
const filterable = computed(() => {
  return props.item.props?.filterable || false
})

// 计算搜索占位符
const filterPlaceholder = computed(() => {
  return props.item.props?.filterPlaceholder || '搜索'
})

// 计算是否显示清空按钮
const clearable = computed(() => {
  return props.item.props?.clearable || false
})

// 计算尺寸
const size = computed(() => {
  return props.item.props?.size || 'medium'
})
</script>

<template>
  <wd-select-picker
    v-model="selectValue"
    :align-right="true"
    :clearable="clearable"
    :close-on-click-modal="item.props?.closeOnClickModal !== false"
    :columns="dictColumns"
    :custom-class="item.props?.customClass"
    :custom-content-class="item.props?.customContentClass"
    :custom-label-class="item.props?.customLabelClass"
    :custom-value-class="item.props?.customValueClass"
    :disabled="item.props?.disabled"
    :ellipsis="item.props?.ellipsis || false"
    :error="!!errorMessage"
    :filter-placeholder="filterPlaceholder"
    :filterable="filterable"
    :label="item.title"
    :label-width="item.props?.labelWidth || '30%'"
    :placeholder="placeholder"
    :readonly="item.props?.readonly"
    :required="isRequired"
    :safe-area-inset-bottom="item.props?.safeAreaInsetBottom !== false"
    :scroll-into-view="item.props?.scrollIntoView !== false"
    :show-confirm="item.props?.showConfirm !== false"
    :size="size"
    :title="title"
    :type="pickerType"
    :z-index="item.props?.zIndex || 15"
    @cancel="() => $emit('cancel')"
    @change="handleChange"
    @clear="() => $emit('clear')"
    @close="() => $emit('close')"
    @confirm="handleConfirm"
    @open="() => $emit('open')"
  />
</template>

<style lang="scss" scoped>
:deep(.dict-empty-option) {
  font-style: italic;
  color: #999;
}

// 自定义选择器样式
:deep(.wd-select-picker) {
  .wd-cell__value {
    text-align: left;
  }
}
</style>
