<script lang="ts" setup>
import { computed, defineEmits, defineProps, nextTick, onMounted, ref, watchEffect } from 'vue'
import type { FormItem } from '@/types/form'
import { getRegionList, type RegionData } from '@/service/region/RegionAPI'

// 🚀 层级结构接口
interface RegionHierarchy {
  town?: string
  village?: string
  community?: string
}

interface Props {
  item: FormItem
  modelValue?: RegionHierarchy
}

interface ColumnOption {
  value: string
  label: string
  level?: 'town' | 'village' | 'community'
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: RegionHierarchy]
}>()

// 🚀 简化的响应式数据 - 移除冗余状态
const cascaderValue = ref<string[]>([])
const columns = ref<ColumnOption[][]>([])
const loading = ref(false)
const isInitialized = ref(false)
const isInternalUpdate = ref(false) // 防止循环触发的标志位

// 🚀 层级映射配置
const levelMapping = ['town', 'village', 'community'] as const

// 🚀 将层级对象转换为数组格式
const hierarchyToArray = (hierarchy: RegionHierarchy): string[] => {
  const result: string[] = []
  if (hierarchy.town) result.push(hierarchy.town)
  if (hierarchy.village) result.push(hierarchy.village)
  if (hierarchy.community) result.push(hierarchy.community)
  return result
}

// 🚀 将数组格式转换为层级对象
const arrayToHierarchy = (values: string[]): RegionHierarchy => {
  const result: RegionHierarchy = {}
  values.forEach((value, index) => {
    const level = levelMapping[index]
    if (level) {
      result[level] = value
    }
  })
  return result
}

// 🚀 同步查找选项标签（关键改进）
const findLabelByValue = (value: string, columnIndex: number): string => {
  const columnData = columns.value[columnIndex]
  if (columnData) {
    const item = columnData.find((item) => item.value === value)
    if (item) {
      return item.label
    }
  }

  // 备用：从所有 columns 中搜索
  for (const column of columns.value) {
    const item = column.find((item) => item.value === value)
    if (item) {
      return item.label
    }
  }

  console.warn(`Could not find label for value: ${value}`)
  return value // 返回原值作为备用
}

// 🚀 重构的监听逻辑 - 避免循环触发
watchEffect(async () => {
  if (!props.modelValue || !isInitialized.value || isInternalUpdate.value) {
    return
  }

  const newValue = hierarchyToArray(props.modelValue)
  const currentStr = JSON.stringify(cascaderValue.value)
  const newStr = JSON.stringify(newValue)

  if (currentStr !== newStr) {
    console.log('External modelValue changed:', newValue)
    isInternalUpdate.value = true

    // 重置到基础状态，让 auto-complete 重新补全
    const firstColumnData = await fetchRegionData(rootParentCode.value, 0)
    if (firstColumnData.length > 0) {
      columns.value = [firstColumnData]
      cascaderValue.value = newValue
      // auto-complete 会自动触发 column-change 来补全数据
    }

    nextTick(() => {
      isInternalUpdate.value = false
    })
  }
})

// 计算属性
const isRequired = computed(() => !!props.item.$required)
const errorMessage = computed(() => {
  if (isRequired.value && typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})
const placeholder = computed(() => props.item.props?.placeholder || props.item.info || '请选择')
const rootParentCode = computed(() => props.item.props?.rootParentCode || '222401')
const autoComplete = computed(() => props.item.props?.autoComplete !== false)

// 🚀 获取当前选择层级的标识
const getCurrentLevel = (index: number): 'town' | 'village' | 'community' | undefined => {
  return levelMapping[index]
}

// 请求区域数据
const fetchRegionData = async (parentCode?: string, level?: number): Promise<ColumnOption[]> => {
  try {
    console.log('Fetching region data, parentCode:', parentCode, 'level:', level)
    const data = await getRegionList(parentCode)

    if (data && Array.isArray(data)) {
      return data.map((region: RegionData) => ({
        value: region.code || region.id,
        label: region.name,
        level: getCurrentLevel(level || 0),
      }))
    } else {
      console.warn('Unexpected API response format:', data)
      return []
    }
  } catch (error) {
    console.error('Failed to fetch region data:', error)
    uni.showToast({
      title: '获取地区数据失败',
      icon: 'none',
    })
    return []
  }
}

// 🚀 重构的初始化方法 - 遵循 ColPicker 设计理念
const initializeComponent = async () => {
  loading.value = true
  try {
    // 1. 总是先加载第一列数据
    const firstColumnData = await fetchRegionData(rootParentCode.value, 0)
    if (firstColumnData.length > 0) {
      columns.value = [firstColumnData]
    }

    // 2. 如果有初始值，直接设置，让 auto-complete 自动补全后续列
    if (props.modelValue) {
      const initialValues = hierarchyToArray(props.modelValue)
      console.log('Setting initial values for auto-complete:', initialValues)
      cascaderValue.value = initialValues
      // 📋 关键：auto-complete 会检测到 columns.length < value.length，自动触发 column-change
    } else {
      cascaderValue.value = []
    }

    isInitialized.value = true
    console.log('Component initialized with auto-complete mechanism')
  } catch (error) {
    console.error('Failed to initialize component:', error)
  } finally {
    loading.value = false
  }
}

// 处理列变化（级联加载）
const handleColumnChange = async (option: any) => {
  const { selectedItem, index, resolve, finish } = option
  console.log('Column change:', { selectedItem, index, level: getCurrentLevel(index) })

  try {
    // 检查是否已达到最大层级
    if (index >= levelMapping.length - 1) {
      console.log('Reached maximum hierarchy level (community), finishing selection')
      finish(true)
      return
    }

    // 获取下级数据
    const childData = await fetchRegionData(selectedItem.value, index + 1)

    if (childData && childData.length > 0) {
      console.log(
        `Loading level ${index + 1} (${getCurrentLevel(index + 1)}) data:`,
        childData.length,
        'items',
      )
      resolve(childData)
    } else {
      console.log('No child data found, finishing selection')
      finish(true)
    }
  } catch (error) {
    console.error('Failed to load child data:', error)
    uni.showToast({
      title: '数据加载失败，请重试',
      icon: 'none',
    })
    finish(false)
  }
}

// 🚀 更新 modelValue - 添加内部更新标志
const updateModelValue = (newVal: string[]) => {
  const hierarchyValue = arrayToHierarchy(newVal)
  console.log('Updating modelValue with hierarchy:', hierarchyValue)

  isInternalUpdate.value = true
  emits('update:modelValue', hierarchyValue)

  nextTick(() => {
    isInternalUpdate.value = false
  })
}

// 处理确认事件
const handleConfirm = ({
  value,
  selectedItems,
}: {
  value: string[]
  selectedItems: ColumnOption[]
}) => {
  console.log('Region cascader confirm:', { value, selectedItems })

  // 构建层级信息用于调试
  const hierarchyInfo: RegionHierarchy = {}
  selectedItems.forEach((item, index) => {
    const level = getCurrentLevel(index)
    if (level) {
      hierarchyInfo[level] = item.value
    }
  })
  console.log('Selected hierarchy:', hierarchyInfo)

  // 更新内部状态和外部 modelValue
  cascaderValue.value = value
  updateModelValue(value)

  // 显示选中的完整路径
  const fullPath = selectedItems.map((item) => item.label).join(' / ')
  console.log('Selected region path:', fullPath)
}

// 处理取消事件
const handleCancel = () => {
  console.log('Region cascader cancelled')
}

// 🚀 重构的显示格式化 - 只显示 label，不拼接"镇""村""社区"
const displayFormat = (selectedItems?: ColumnOption[]) => {
  // 1. 优先使用 ColPicker 提供的 selectedItems
  if (selectedItems && selectedItems.length > 0) {
    const levelLabels = selectedItems.map((item) => item.label)
    const result = levelLabels.join(' / ')
    console.log('DisplayFormat with selectedItems:', result)
    return result
  }

  // 2. 备用方案：根据当前 cascaderValue 和 columns 同步计算
  if (cascaderValue.value.length > 0 && columns.value.length > 0) {
    const levelLabels = cascaderValue.value.map((value, index) => {
      const label = findLabelByValue(value, index)
      return label
    })
    const result = levelLabels.join(' / ')
    console.log('DisplayFormat with fallback method:', result)
    return result
  }

  console.log('DisplayFormat: no data available')
  return ''
}

// 组件挂载时初始化
onMounted(() => {
  console.log('Region cascader mounted in hierarchy mode')
  initializeComponent()
})
</script>

<template>
  <wd-col-picker
    :align-right="true"
    :auto-complete="autoComplete"
    :before-confirm="item.props?.beforeConfirm"
    :close-on-click-modal="item.props?.closeOnClickModal !== false"
    :column-change="handleColumnChange"
    :columns="columns"
    :disabled="item.props?.disabled || false"
    :display-format="displayFormat"
    :ellipsis="item.props?.ellipsis || false"
    :error="item.props?.error || (isRequired && cascaderValue.length === 0 && errorMessage !== '')"
    :label="item.title"
    :label-width="item.props?.labelWidth || '33%'"
    :line-height="item.props?.lineHeight"
    :loading="loading"
    :model-value="cascaderValue"
    :placeholder="placeholder"
    :readonly="item.props?.readonly || false"
    :required="isRequired"
    :safe-area-inset-bottom="item.props?.safeAreaInsetBottom !== false"
    :title="item.props?.title"
    :z-index="item.props?.zIndex || 15"
    @cancel="handleCancel"
    @confirm="handleConfirm"
  />
</template>

<style lang="scss" scoped>
.yt-region-cascader {
  text-align: left;
}
</style>
