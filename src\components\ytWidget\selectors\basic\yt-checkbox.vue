<template>
  <view class="yt-checkbox">
    <!-- 使用Cell模式的复选框组 -->
    <wd-checkbox-group
      v-model="groupValue"
      :cell="true"
      :checked-color="item.props?.checkedColor || '#4D80F0'"
      :disabled="disabled || readonly"
      :inline="item.props?.inline"
      :max="item.props?.max || 0"
      :min="item.props?.min || 0"
      :shape="item.props?.shape || 'square'"
      :size="item.props?.size"
      custom-class="yt-checkbox-group"
      @change="handleGroupChange"
    >
      <wd-checkbox
        v-for="option in normalizedOptions"
        :key="option.value"
        :disabled="option.disabled || disabled || readonly"
        :model-value="option.value"
      >
        {{ option.label }}
      </wd-checkbox>
    </wd-checkbox-group>
    <!-- 提示信息 -->
    <view v-if="item.info" class="yt-checkbox__info">
      {{ item.info }}
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { FormItem } from '@/types/form'

interface CheckboxOption {
  label: string
  value: any
  disabled: boolean
}

interface Props {
  item: FormItem
  modelValue?: any
  disabled?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: any[]]
}>()

// 标准化选项数据
const normalizedOptions = computed((): CheckboxOption[] => {
  if (!props.item.options || !Array.isArray(props.item.options)) {
    return []
  }

  return props.item.options.map((option): CheckboxOption => {
    if (typeof option === 'object' && option !== null && 'label' in option) {
      return {
        label: String(option.label || option.value),
        value: option.value,
        disabled: Boolean(option.disabled) || false,
      }
    }
    return {
      label: String(option),
      value: option,
      disabled: false,
    }
  })
})

// 复选框组的值 - 统一使用数组格式
const groupValue = computed({
  get: () => {
    // 确保返回数组
    if (Array.isArray(props.modelValue)) {
      return props.modelValue
    }

    // 如果传入的是非数组值，尝试转换为数组
    if (props.modelValue !== null && props.modelValue !== undefined) {
      // 如果是布尔值或其他单个值，检查是否匹配选项
      if (normalizedOptions.value.length > 0) {
        const matchedOption = normalizedOptions.value.find((option) => {
          // 处理布尔值情况
          if (typeof props.modelValue === 'boolean') {
            return (
              option.value === props.modelValue ||
              (props.modelValue === true && option.value) ||
              (props.modelValue === false && !option.value)
            )
          }
          // 处理其他值类型
          return option.value === props.modelValue
        })

        if (matchedOption) {
          return [matchedOption.value]
        }
      }

      // 兜底：直接包装为数组
      return [props.modelValue]
    }

    return []
  },
  set: (value: any[]) => {
    emit('update:modelValue', value)
  },
})

// 处理复选框组变化
const handleGroupChange = ({ value }: { value: any[] }) => {
  console.log('📝 复选框组选择变化:', value)
  emit('update:modelValue', value)
}

// 暴露给父组件的属性和方法
defineExpose({
  normalizedOptions,
  groupValue,
})
</script>

<style lang="scss" scoped>
.yt-checkbox {
  &__title {
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  &__item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;

    &:last-child {
      border-bottom: none;
    }
  }

  &__empty {
    padding: 16px;
    color: #999;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px dashed #ddd;
    border-radius: 4px;

    text {
      font-size: 14px;
    }
  }

  &__info {
    padding: 8px 16px;
    font-size: 12px;
    color: #999;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
  }
}

// 复选框组样式优化
:deep(.wd-checkbox-group) {
  .wd-checkbox {
    display: flex;
    align-items: center;
    width: 100%;

    &:not(.wd-checkbox--cell) {
      padding: 8px 0;
    }

    .wd-checkbox__shape {
      flex-shrink: 0;
      margin-right: 12px;
    }

    .wd-checkbox__label {
      display: flex;
      flex: 1;
      align-items: center;
      font-size: 14px;
      line-height: 1.4;
      color: #374151;
    }
  }
}

// Cell 模式样式
:deep(.wd-cell-group) {
  .wd-checkbox-group {
    .wd-checkbox {
      padding: 12px 16px;
      border-bottom: 1px solid #f1f5f9;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
