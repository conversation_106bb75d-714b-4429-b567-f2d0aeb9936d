<script lang="ts" setup>
import { computed, defineEmits, defineProps, ref, watchEffect } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: string[] | number[]
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: string[] | number[]]
}>()

// 🚀 使用 ref + watchEffect 优化多选逻辑
const selectValue = ref<any[]>([])
const isUpdating = ref(false)

// 🚀 使用 watchEffect 自动追踪依赖，更简洁
watchEffect(() => {
  // 从外部同步到内部
  if (!isUpdating.value) {
    const newValue = Array.isArray(props.modelValue) ? [...props.modelValue] : []
    const currentStr = JSON.stringify(selectValue.value.sort())
    const newStr = JSON.stringify(newValue.sort())

    if (currentStr !== newStr) {
      selectValue.value = newValue
    }
  }
})

// 🚀 优化的更新方法 - 使用防抖
let updateTimer: number | null = null
const updateModelValue = (newVal: any[]) => {
  if (updateTimer) {
    clearTimeout(updateTimer)
  }

  updateTimer = setTimeout(() => {
    isUpdating.value = true
    console.log('Multi select value changed:', newVal)
    emits('update:modelValue', newVal)
    isUpdating.value = false
    updateTimer = null
  }, 16) // 16ms 防抖
}

// 处理值变化
const handleValueChange = (newVal: any[]) => {
  selectValue.value = newVal
  updateModelValue(newVal)
}

// 计算是否必填
const isRequired = computed(() => {
  return !!props.item.$required
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (isRequired.value && typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})

// 计算占位符
const placeholder = computed(() => {
  return props.item.props?.placeholder || '请选择'
})

// 处理选择项数据
const columns = computed(() => {
  if (!props.item.options) {
    return []
  }

  return props.item.options.map((option) => ({
    label: option.label,
    value: option.value,
    disabled: option.disabled || false,
  }))
})
</script>

<template>
  <wd-select-picker
    :model-value="selectValue"
    :clearable="item.props?.clearable"
    :columns="columns"
    :disabled="item.props?.disabled"
    :label="item.title"
    :placeholder="placeholder"
    align-right
    type="checkbox"
    @update:model-value="handleValueChange"
  />
</template>

<style lang="scss" scoped>
.yt-select-multi {
  text-align: left;
}
</style>
