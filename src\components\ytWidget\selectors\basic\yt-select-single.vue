<script lang="ts" setup>
import { computed, defineEmits, defineProps } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: string | number
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: string | number]
}>()

// 🚀 使用 computed setter/getter 模式 - 更优雅，无循环问题
const selectValue = computed({
  get: () => props.modelValue || '',
  set: (value: string | number) => {
    console.log('Single select value changed:', value)
    emits('update:modelValue', value)
  },
})

// 计算是否必填
const isRequired = computed(() => {
  return !!props.item.$required
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (isRequired.value && typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})

// 计算占位符
const placeholder = computed(() => {
  return props.item.props?.placeholder || '请选择'
})

// 处理选择项数据
const columns = computed(() => {
  if (!props.item.options) {
    return []
  }

  return props.item.options.map((option) => ({
    label: option.label,
    value: option.value,
    disabled: option.disabled || false,
  }))
})
</script>

<template>
  <wd-select-picker
    v-model="selectValue"
    :clearable="item.props?.clearable"
    :columns="columns"
    :disabled="item.props?.disabled"
    :label="item.title"
    :placeholder="placeholder"
    align-right
    type="radio"
  />
</template>

<style lang="scss" scoped>
.yt-select-single {
  text-align: left;
}
</style>
