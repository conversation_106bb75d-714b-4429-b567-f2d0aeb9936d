<script lang="ts" setup>
import { computed, defineEmits, defineProps, ref, watch } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: string | number | string[] | number[]
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: string | number | string[] | number[]]
}>()

// 使用ref和watch实现双向绑定
const getInitialValue = () => {
  const isMultiple = props.item.props?.multiple
  const defaultValue = isMultiple ? [] : ''

  if (props.modelValue !== undefined && props.modelValue !== null) {
    // 如果有传入值，根据模式进行处理
    if (isMultiple) {
      // 多选模式：确保返回数组
      return Array.isArray(props.modelValue)
        ? props.modelValue
        : props.modelValue
          ? [props.modelValue]
          : []
    } else {
      // 单选模式：返回单个值
      return Array.isArray(props.modelValue)
        ? props.modelValue.length > 0
          ? props.modelValue[0]
          : ''
        : props.modelValue
    }
  }

  return defaultValue
}

const selectValue = ref(getInitialValue())

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    const isMultiple = props.item.props?.multiple

    if (newVal !== undefined && newVal !== null) {
      if (isMultiple) {
        // 多选模式：确保是数组
        selectValue.value = Array.isArray(newVal) ? newVal : newVal ? [newVal] : []
      } else {
        // 单选模式：确保是单个值
        selectValue.value = Array.isArray(newVal) ? (newVal.length > 0 ? newVal[0] : '') : newVal
      }
    } else {
      selectValue.value = isMultiple ? [] : ''
    }
  },
)

// 监听输入值变化
watch(selectValue, (newVal) => {
  emits('update:modelValue', newVal)
})

// 处理选择事件 - wd-select-picker 的 confirm 事件
const handleConfirm = (event: any) => {
  console.log('handleConfirm event:', event) // 调试用

  // wd-select-picker 返回的数据结构: {value, selectedItems}
  let newValue

  if (event && event.value !== undefined) {
    // 从事件对象中取值
    newValue = event.value
  } else {
    // 直接传递的值
    newValue = event
  }

  // 确保多选时是数组，单选时是单个值
  if (props.item.props?.multiple) {
    // 多选模式：确保值是数组
    if (!Array.isArray(newValue)) {
      newValue = newValue ? [newValue] : []
    }
  } else {
    // 单选模式：如果是数组，取第一个值
    if (Array.isArray(newValue)) {
      newValue = newValue.length > 0 ? newValue[0] : ''
    }
  }

  selectValue.value = newValue
  emits('update:modelValue', newValue)
}

// 处理change事件
const handleChange = (event: any) => {
  console.log('handleChange event:', event) // 调试用
  handleConfirm(event)
}

// 计算是否必填
const isRequired = computed(() => {
  return !!props.item.$required
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (isRequired.value && typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})

// 计算占位符
const placeholder = computed(() => {
  return props.item.props?.placeholder || '请选择'
})

// 计算选择器类型 (checkbox/radio)
const selectType = computed(() => {
  return props.item.props?.multiple ? 'checkbox' : 'radio'
})

// 处理选择项数据，转换为wd-select-picker需要的columns格式
const columns = computed(() => {
  if (!props.item.options) {
    console.log('No options found in item:', props.item)
    return []
  }

  // wd-select-picker需要的格式
  const result = props.item.options.map((option) => ({
    label: option.label,
    value: option.value,
    disabled: option.disabled || false,
  }))

  console.log('Computed columns:', result)
  console.log('Item options:', props.item.options)

  return result
})
</script>

<template>
  <wd-select-picker
    v-model="selectValue"
    :clearable="item.props?.clearable"
    :columns="columns"
    :disabled="item.props?.disabled"
    :label="item.title"
    :placeholder="placeholder"
    :type="selectType"
    align-right
    @change="handleChange"
    @confirm="handleConfirm"
  />
</template>

<style lang="scss" scoped>
.yt-select-textAlign {
  text-align: left;
}
</style>
