<script lang="ts" setup>
import { computed, defineEmits, defineProps, ref, watchEffect } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: number | number[] | string | string[]
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: number | number[] | string | string[]]
}>()

// 🚀 使用 ref + watchEffect 优化响应式逻辑
const calendarValue = ref<any>('')
const isUpdating = ref(false)

// 获取默认值
const getDefaultValue = () => {
  const calendarType = props.item.props?.calendarType || 'date'
  if (['dates', 'daterange', 'weekrange', 'monthrange', 'datetimerange'].includes(calendarType)) {
    return []
  } else if (['datetime', 'datetimerange'].includes(calendarType)) {
    return ''
  } else {
    return Date.now()
  }
}

// 🚀 使用 watchEffect 自动追踪依赖
watchEffect(() => {
  if (!isUpdating.value) {
    const newValue =
      props.modelValue !== undefined && props.modelValue !== null
        ? props.modelValue
        : getDefaultValue()

    if (JSON.stringify(calendarValue.value) !== JSON.stringify(newValue)) {
      calendarValue.value = newValue
    }
  }
})

// 🚀 优化的更新方法 - 使用防抖
let updateTimer: number | null = null
const updateModelValue = (newVal: any) => {
  if (updateTimer) {
    clearTimeout(updateTimer)
  }

  updateTimer = setTimeout(() => {
    isUpdating.value = true
    console.log('Calendar value changed:', newVal)
    emits('update:modelValue', newVal)
    isUpdating.value = false
    updateTimer = null
  }, 16) // 16ms 防抖
}

// 计算是否必填
const isRequired = computed(() => {
  return !!props.item.$required
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (isRequired.value && typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})

// 计算日历类型
const calendarType = computed(() => {
  return props.item.props?.calendarType || 'date'
})

// 计算最小日期（默认为当前日期往前推6个月）
const minDate = computed(() => {
  if (props.item.props?.minDate) {
    return props.item.props.minDate
  }
  const sixMonthsAgo = new Date()
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
  return sixMonthsAgo.getTime()
})

// 计算最大日期（默认为当前日期往后推6个月）
const maxDate = computed(() => {
  if (props.item.props?.maxDate) {
    return props.item.props.maxDate
  }
  const sixMonthsLater = new Date()
  sixMonthsLater.setMonth(sixMonthsLater.getMonth() + 6)
  return sixMonthsLater.getTime()
})

// 处理确认事件
const handleConfirm = ({ value }: { value: any }) => {
  console.log('Calendar confirm:', value)
  calendarValue.value = value
  updateModelValue(value)
}

// 处理取消事件
const handleCancel = () => {
  console.log('Calendar cancelled')
}

// 处理值变化事件
const handleChange = ({ value }: { value: any }) => {
  console.log('Calendar change:', value)
}
</script>

<template>
  <wd-calendar
    :model-value="calendarValue"
    :allow-same-day="item.props?.allowSameDay || false"
    :clearable="item.props?.clearable !== false"
    :disabled="item.props?.disabled || false"
    :error="isRequired && !calendarValue && errorMessage !== ''"
    :first-day-of-week="item.props?.firstDayOfWeek || 0"
    :hide-second="item.props?.hideSecond || false"
    :label="item.title"
    :max-date="maxDate"
    :min-date="minDate"
    :readonly="item.props?.readonly || false"
    :required="isRequired"
    :show-confirm="item.props?.showConfirm !== false"
    :show-type-switch="item.props?.showTypeSwitch || false"
    :time-filter="item.props?.timeFilter"
    :type="calendarType"
    :with-cell="item.props?.withCell !== false"
    align-right
    @cancel="handleCancel"
    @change="handleChange"
    @confirm="handleConfirm"
  />
</template>

<style lang="scss" scoped>
.yt-calendar {
  text-align: left;
}
</style>
