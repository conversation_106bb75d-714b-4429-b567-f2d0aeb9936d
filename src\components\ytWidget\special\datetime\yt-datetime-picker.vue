<script lang="ts" setup>
import { computed, defineEmits, defineProps, ref, watchEffect } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: number | string | number[] | string[]
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: number | string | number[] | string[]]
}>()

// 🚀 使用 ref + watchEffect 优化响应式逻辑
const pickerValue = ref<any>('')
const isUpdating = ref(false)

// 🚀 类型判断辅助函数
const isTimestampType = (type: string): boolean => {
  return ['datetime', 'date', 'year-month', 'year'].includes(type)
}

// 🚀 判断是否为范围选择模式
const isRangeMode = computed(() => {
  return Array.isArray(props.modelValue)
})

// 🚀 格式转换：将外部值转换为组件内部使用的格式
const formatValueForPicker = (value: any, type: string): any => {
  if (value === undefined || value === null || value === '') {
    return getDefaultValue()
  }

  // 范围选择模式
  if (Array.isArray(value)) {
    if (value.length === 0) {
      return []
    }

    if (type === 'time') {
      // time 类型范围选择，保持字符串格式
      return value.map((v) => (typeof v === 'string' ? v : '12:00'))
    } else {
      // 时间戳类型范围选择，确保都是数字
      return value.map((v) => {
        if (typeof v === 'number') return v
        if (typeof v === 'string' && v) {
          // 尝试解析时间字符串为时间戳
          const date = new Date(v)
          return isNaN(date.getTime()) ? '' : date.getTime()
        }
        return ''
      })
    }
  }

  // 单值模式
  if (type === 'time') {
    // time 类型使用字符串格式
    if (typeof value === 'string') {
      // 验证时间格式 HH:mm
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
      return timeRegex.test(value) ? value : '12:00'
    }
    // 如果传入时间戳，转换为时间字符串
    if (typeof value === 'number') {
      const date = new Date(value)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }
    return '12:00'
  } else {
    // 其他类型使用时间戳
    if (typeof value === 'number') {
      return value
    }
    if (typeof value === 'string' && value) {
      // 尝试解析时间字符串为时间戳
      const date = new Date(value)
      return isNaN(date.getTime()) ? '' : date.getTime()
    }
    return ''
  }
}

// 🚀 格式转换：将组件内部值转换为输出格式（时间格式字符串）
const formatValueForOutput = (value: any, type: string): any => {
  if (value === undefined || value === null || value === '') {
    return isRangeMode.value ? [] : ''
  }

  // 时间戳转换为格式化字符串的辅助函数
  const formatTimestamp = (timestamp: number, formatType: string): string => {
    const date = new Date(timestamp)

    switch (formatType) {
      case 'time':
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      case 'date':
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
      case 'datetime':
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
      case 'year-month':
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`
      case 'year':
        return `${date.getFullYear()}`
      default:
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
    }
  }

  // 范围选择模式
  if (Array.isArray(value)) {
    if (value.length === 0) {
      return []
    }

    if (type === 'time') {
      // time 类型范围选择，输出字符串数组
      return value.map((v) => {
        if (typeof v === 'string') return v
        if (typeof v === 'number') return formatTimestamp(v, 'time')
        return '12:00'
      })
    } else {
      // 其他类型范围选择，输出格式化时间字符串数组
      return value.map((v) => {
        if (typeof v === 'number') return formatTimestamp(v, type)
        if (typeof v === 'string') {
          // 如果已经是字符串，尝试解析后重新格式化以确保格式一致
          const date = new Date(v)
          return isNaN(date.getTime())
            ? formatTimestamp(Date.now(), type)
            : formatTimestamp(date.getTime(), type)
        }
        return formatTimestamp(Date.now(), type)
      })
    }
  }

  // 单值模式
  if (type === 'time') {
    // time 类型输出字符串
    if (typeof value === 'string') return value
    if (typeof value === 'number') return formatTimestamp(value, 'time')
    return '12:00'
  } else {
    // 其他类型输出格式化时间字符串
    if (typeof value === 'number') return formatTimestamp(value, type)
    if (typeof value === 'string') {
      // 如果已经是字符串，尝试解析后重新格式化以确保格式一致
      const date = new Date(value)
      return isNaN(date.getTime())
        ? formatTimestamp(Date.now(), type)
        : formatTimestamp(date.getTime(), type)
    }
    return formatTimestamp(Date.now(), type)
  }
}

// 🚀 获取默认值
const getDefaultValue = () => {
  const pickerType = props.item.props?.type || 'time'

  // 范围选择模式
  if (isRangeMode.value) {
    return []
  }

  // 单值模式 - 不设置默认值，返回空值
  if (pickerType === 'time') {
    return ''
  } else {
    return ''
  }
}

// 🚀 使用 watchEffect 自动追踪依赖
watchEffect(() => {
  if (!isUpdating.value) {
    const pickerType = props.item.props?.type || 'time'
    const formattedValue = formatValueForPicker(props.modelValue, pickerType)

    if (JSON.stringify(pickerValue.value) !== JSON.stringify(formattedValue)) {
      pickerValue.value = formattedValue
      console.log('DateTime picker value updated:', {
        originalValue: props.modelValue,
        formattedValue,
        type: pickerType,
        isRangeMode: isRangeMode.value,
      })
    }
  }
})

// 🚀 优化的更新方法 - 使用防抖
let updateTimer: number | null = null
const updateModelValue = (newVal: any) => {
  if (updateTimer) {
    clearTimeout(updateTimer)
  }

  updateTimer = setTimeout(() => {
    isUpdating.value = true
    const pickerType = props.item.props?.type || 'time'
    const outputValue = formatValueForOutput(newVal, pickerType)

    console.log('DateTime picker value changed:', {
      internalValue: newVal,
      outputValue,
      type: pickerType,
      isRangeMode: isRangeMode.value,
    })

    emits('update:modelValue', outputValue)
    isUpdating.value = false
    updateTimer = null
  }, 16) // 16ms 防抖
}

// 计算是否必填
const isRequired = computed(() => {
  return !!props.item.$required
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (isRequired.value && typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})

// 计算picker类型
const pickerType = computed(() => {
  return props.item.props?.type || 'time'
})

// 计算最小日期（默认为1949年1月1日）
const minDate = computed(() => {
  if (props.item.props?.minDate) {
    return props.item.props.minDate
  }
  // 设置最小日期为1949年1月1日
  const minDate1949 = new Date(1949, 0, 1) // 月份从0开始，所以0表示1月
  return minDate1949.getTime()
})

// 计算最大日期（默认为当前日期往后推10年）
const maxDate = computed(() => {
  if (props.item.props?.maxDate) {
    return props.item.props.maxDate
  }
  const tenYearsLater = new Date()
  tenYearsLater.setFullYear(tenYearsLater.getFullYear() + 10)
  return tenYearsLater.getTime()
})

// 计算占位符
const placeholder = computed(() => {
  return props.item.props?.placeholder || props.item.info || '请选择'
})

// 处理确认事件
const handleConfirm = ({ value }: { value: any }) => {
  console.log('DateTime picker confirm:', value)
  pickerValue.value = value
  updateModelValue(value)
}

// 处理取消事件
const handleCancel = () => {
  console.log('DateTime picker cancelled')
}

// 处理切换事件（范围选择模式）
const handleToggle = (value: any) => {
  console.log('DateTime picker toggle:', value)
}
</script>

<template>
  <wd-datetime-picker
    :align-right="item.props?.alignRight || true"
    :before-confirm="item.props?.beforeConfirm"
    :cancel-button-text="item.props?.cancelButtonText || '取消'"
    :close-on-click-modal="item.props?.closeOnClickModal !== false"
    :columns-height="item.props?.columnsHeight || 231"
    :confirm-button-text="item.props?.confirmButtonText || '确定'"
    :default-value="''"
    :disabled="item.props?.disabled || false"
    :display-format="item.props?.displayFormat"
    :display-format-tab-label="item.props?.displayFormatTabLabel"
    :ellipsis="item.props?.ellipsis || false"
    :error="item.props?.error || (isRequired && !pickerValue && errorMessage !== '')"
    :filter="item.props?.filter"
    :formatter="item.props?.formatter"
    :immediate-change="item.props?.immediateChange || false"
    :label="item.title"
    :label-width="item.props?.labelWidth || '33%'"
    :loading="item.props?.loading || false"
    :loading-color="item.props?.loadingColor || '#4D80F0'"
    :max-date="maxDate"
    :max-hour="item.props?.maxHour || 23"
    :max-minute="item.props?.maxMinute || 59"
    :min-date="minDate"
    :min-hour="item.props?.minHour || 0"
    :min-minute="item.props?.minMinute || 0"
    :model-value="pickerValue"
    :placeholder="placeholder"
    :prop="item.props?.prop"
    :readonly="item.props?.readonly || false"
    :required="isRequired"
    :rules="item.props?.rules || []"
    :safe-area-inset-bottom="item.props?.safeAreaInsetBottom !== false"
    :size="item.props?.size"
    :title="item.props?.title"
    :type="pickerType"
    :z-index="item.props?.zIndex || 15"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    @toggle="handleToggle"
  />
</template>

<style lang="scss" scoped>
.yt-datetime-picker {
  text-align: left;
}
</style>
