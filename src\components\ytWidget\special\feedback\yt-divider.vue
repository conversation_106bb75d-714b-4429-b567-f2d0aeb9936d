<!--
// {{CHENGQI:
// Action: Created; Timestamp: 2025-06-26 16:01:07 +08:00; Reason: 创建Divider分割线组件，支持elDivider类型; Principle_Applied: 组件完整性, 用户体验;
// }}
-->
<template>
  <view class="yt-divider">
    <wd-divider
      :color="actualColor"
      :hairline="actualHairline"
      :dashed="actualDashed"
      :content-position="actualContentPosition"
      :vertical="actualVertical"
      :class="[
        'custom-divider',
        {
          'el-divider-vertical': actualVertical,
          'el-divider-horizontal': !actualVertical,
        },
      ]"
    >
      <!-- 分割线内容 -->
      <template v-if="hasContent && !actualVertical">
        {{ actualContent }}
      </template>
    </wd-divider>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  /** 表单项配置 */
  item: FormItem
  /** 当前值 */
  modelValue?: any
  /** 标签文本 */
  label?: string
  /** 占位符 */
  placeholder?: string
  /** 是否必填 */
  required?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
  required: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
  change: [value: any]
}>()

// 计算实际使用的属性值
const actualColor = computed(() => {
  return props.item?.props?.color || ''
})

const actualHairline = computed(() => {
  return props.item?.props?.hairline !== undefined ? props.item.props.hairline : true
})

const actualDashed = computed(() => {
  return props.item?.props?.dashed !== undefined ? props.item.props.dashed : false
})

const actualContentPosition = computed(() => {
  return props.item?.props?.contentPosition || 'center'
})

const actualVertical = computed(() => {
  return props.item?.props?.vertical !== undefined ? props.item.props.vertical : false
})

// 处理内容文本
const actualContent = computed(() => {
  // 支持多种数据来源
  let content = ''

  // 1. 从children属性获取（form-create格式）
  if (props.item?.children) {
    if (Array.isArray(props.item.children)) {
      content = props.item.children.join(' ')
    } else {
      content = String(props.item.children)
    }
  }

  // 2. 从其他属性获取
  if (!content) {
    content = props.item?.props?.text || props.item?.info || props.item?.title || ''
  }

  return content
})

const hasContent = computed(() => {
  return actualContent.value.trim().length > 0
})
</script>

<style lang="scss" scoped>
.yt-divider {
  width: 100%;
  margin: 16rpx 0;
}

// 自定义分割线样式
:deep(.custom-divider) {
  margin: 16rpx 0;

  &.el-divider-horizontal {
    width: 100%;
  }

  &.el-divider-vertical {
    display: inline-block;
    margin: 0 16rpx;
    vertical-align: middle;
  }
}

// 分割线文本样式
:deep(.wd-divider__text) {
  padding: 0 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  background: #fff;
}

// 虚线样式优化
:deep(.wd-divider--dashed .wd-divider__line) {
  border-style: dashed;
}

// 垂直分割线样式优化
:deep(.wd-divider--vertical) {
  height: 1em;
  margin: 0 16rpx;
}
</style>
