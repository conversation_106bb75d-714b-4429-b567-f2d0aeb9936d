<!--
// {{CHENGQI:
// Action: Created; Timestamp: 2025-06-26 15:05:28 +08:00; Reason: 创建NoticeBar通知栏组件，支持eAlert类型; Principle_Applied: 组件完整性, 用户体验;
// }}
-->
<template>
  <view class="yt-notice-bar">
    <wd-notice-bar
      :background-color="actualBackgroundColor"
      :class="[
        'custom-notice-bar',
        {
          'el-alert-dark': isElAlertDark,
          'el-alert-success': isElAlertSuccess,
          'el-alert-warning': isElAlertWarning,
          'el-alert-error': isElAlertError,
          'el-alert-info': isElAlertInfo,
        },
      ]"
      :closable="actualClosable"
      :color="actualColor"
      :delay="actualDelay"
      :direction="actualDirection"
      :prefix="actualPrefix"
      :scrollable="actualScrollable"
      :speed="actualSpeed"
      :text="actualText"
      :type="actualType"
      :wrapable="actualWrapable"
      @click="handleClick"
      @close="handleClose"
      @next="handleNext"
    >
      <!-- 支持插槽内容 -->
      <template v-if="item.props?.prefixSlot" #prefix>
        <wd-icon :name="item.props.prefixSlot" />
      </template>

      <template v-if="item.props?.suffixSlot" #suffix>
        <view class="suffix-content">{{ item.props.suffixSlot }}</view>
      </template>
    </wd-notice-bar>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  /** 表单项配置 */
  item: FormItem
  /** 当前值 */
  modelValue?: any
  /** 标签文本 */
  label?: string
  /** 占位符 */
  placeholder?: string
  /** 是否必填 */
  required?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
  required: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
  change: [value: any]
  close: []
  next: [index: number]
  click: [data: { text: string; index: number }]
}>()

// 计算实际使用的属性值
const actualText = computed(() => {
  // 支持多种数据来源
  return (
    props.modelValue ||
    props.item?.props?.text ||
    props.item?.props?.description || // elAlert 使用 description 作为文本
    props.item?.info ||
    props.item?.title ||
    '这是一条通知信息'
  )
})

const actualType = computed(() => {
  // elAlert 类型映射
  if (props.item?.type === 'elAlert') {
    // elAlert 的 type 属性映射到 NoticeBar 的 type
    const elAlertType = props.item?.props?.type
    switch (elAlertType) {
      case 'success':
        return 'info'
      case 'warning':
        return 'warning'
      case 'error':
        return 'danger'
      case 'info':
        return 'info'
      default:
        return 'warning'
    }
  }
  // eAlert 类型映射
  if (props.item?.type === 'eAlert') {
    return props.item?.props?.alertType || 'warning'
  }
  return props.item?.props?.type || 'warning'
})

const actualPrefix = computed(() => {
  // 根据类型设置默认图标
  if (props.item?.props?.prefix) {
    return props.item.props.prefix
  }

  // elAlert 类型的图标映射
  if (props.item?.type === 'elAlert') {
    const elAlertType = props.item?.props?.type
    switch (elAlertType) {
      case 'success':
        return 'check-outline'
      case 'warning':
        return 'warn-bold'
      case 'error':
        return 'close-outline'
      case 'info':
        return 'info-outline'
      default:
        return 'warn-bold'
    }
  }

  // 根据计算后的类型设置默认图标
  switch (actualType.value) {
    case 'info':
      return 'check-outline'
    case 'warning':
      return 'warn-bold'
    case 'danger':
      return 'wifi-error'
    default:
      return 'warn-bold'
  }
})

const actualScrollable = computed(() => {
  return props.item?.props?.scrollable !== undefined ? props.item.props.scrollable : false
})

const actualClosable = computed(() => {
  return props.item?.props?.closable !== undefined ? props.item.props.closable : false
})

const actualWrapable = computed(() => {
  return props.item?.props?.wrapable !== undefined ? props.item.props.wrapable : true
})

const actualColor = computed(() => {
  return props.item?.props?.color || ''
})

const actualBackgroundColor = computed(() => {
  return props.item?.props?.backgroundColor || ''
})

const actualDelay = computed(() => {
  return props.item?.props?.delay || 1
})

const actualSpeed = computed(() => {
  return props.item?.props?.speed || 50
})

const actualDirection = computed(() => {
  return props.item?.props?.direction || 'horizontal'
})

// elAlert 样式计算属性
const isElAlert = computed(() => props.item?.type === 'elAlert')
const isElAlertDark = computed(() => isElAlert.value && props.item?.props?.effect === 'dark')
const isElAlertSuccess = computed(() => isElAlert.value && props.item?.props?.type === 'success')
const isElAlertWarning = computed(() => isElAlert.value && props.item?.props?.type === 'warning')
const isElAlertError = computed(() => isElAlert.value && props.item?.props?.type === 'error')
const isElAlertInfo = computed(() => isElAlert.value && props.item?.props?.type === 'info')

// 事件处理
const handleClose = () => {
  emit('close')
  console.log('📢 NoticeBar 关闭')
}

const handleNext = (index: number) => {
  emit('next', index)
  console.log('📢 NoticeBar 下一条:', index)
}

const handleClick = (data: { text: string; index: number }) => {
  emit('click', data)
  console.log('📢 NoticeBar 点击:', data)
}
</script>

<style lang="scss" scoped>
.yt-notice-bar {
  width: 100%;
  margin-bottom: 16rpx;
}

.suffix-content {
  font-size: 28rpx;
  color: #4d80f0;
}

// 自定义样式
:deep(.wd-notice-bar) {
  border-radius: 8rpx;
}

// 不同类型的自定义样式
:deep(.wd-notice-bar--info) {
  background-color: #f0f9ff;
  border: 1rpx solid #bfdbfe;
}

:deep(.wd-notice-bar--warning) {
  background-color: #fffbeb;
  border: 1rpx solid #fde68a;
}

:deep(.wd-notice-bar--danger) {
  background-color: #fef2f2;
  border: 1rpx solid #fca5a5;
}

// elAlert 特有样式
:deep(.custom-notice-bar.el-alert-success) {
  color: var(--status-completed-color);
  background-color: #f0f9ff;
  border: 1rpx solid var(--status-completed-color);
}

:deep(.custom-notice-bar.el-alert-warning) {
  color: var(--status-active-color);
  background-color: #fdf6ec;
  border: 1rpx solid var(--status-active-color);
}

:deep(.custom-notice-bar.el-alert-error) {
  color: var(--status-pending-color);
  background-color: #fef0f0;
  border: 1rpx solid var(--status-pending-color);
}

:deep(.custom-notice-bar.el-alert-info) {
  color: #909399;
  background-color: #f4f4f5;
  border: 1rpx solid #909399;
}

// dark 效果样式
:deep(.custom-notice-bar.el-alert-dark.el-alert-success) {
  color: #fff;
  background-color: var(--status-completed-color);
  border: 1rpx solid var(--status-completed-color);
}

:deep(.custom-notice-bar.el-alert-dark.el-alert-warning) {
  color: #fff;
  background-color: var(--status-active-color);
  border: 1rpx solid var(--status-active-color);
}

:deep(.custom-notice-bar.el-alert-dark.el-alert-error) {
  color: #fff;
  background-color: var(--status-pending-color);
  border: 1rpx solid var(--status-pending-color);
}
</style>
