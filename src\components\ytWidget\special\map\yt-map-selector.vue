<template>
  <wd-cell
    :disabled="item.props?.disabled || false"
    :required="actualRequired"
    custom-class="map-selector-cell"
  >
    <template #title>
      <view class="title-wrapper">
        <text class="title">{{ actualLabel }}</text>
        <wd-tag v-if="isRequired" size="small" type="danger">必填</wd-tag>
      </view>
    </template>

    <!-- 地图选择器主体 -->
    <view class="map-selector-container">
      <!-- 有数据时的显示 -->
      <view v-if="hasData" class="map-result-display">
        <view class="result-header">
          <wd-icon name="location" size="18px" color="#10b981" />
          <text class="result-title">{{ displayText }}</text>
          <wd-button
            v-if="item.props?.clearable !== false"
            class="clear-btn"
            size="small"
            type="text"
            @click.stop="clearSelection"
          >
            <wd-icon name="delete" size="14px" color="#ef4444" />
          </wd-button>
        </view>

        <view class="result-details">
          <text class="detail-text">{{ getResultBreakdown() }}</text>
        </view>

        <wd-button class="edit-btn" type="primary" size="small" plain @click="showMapSelector">
          <wd-icon name="edit" size="14px" />
          {{ getEditButtonText() }}
        </wd-button>
      </view>

      <!-- 无数据时的显示 -->
      <view v-else class="map-empty-display">
        <view class="empty-icon">
          <wd-icon name="location" size="32px" color="#d1d5db" />
        </view>
        <text class="empty-text">{{ actualPlaceholder }}</text>
        <wd-button
          :disabled="item.props?.disabled || false"
          class="draw-btn"
          type="primary"
          @click="showMapSelector"
        >
          <wd-icon name="add-location" size="16px" />
          {{ getDrawButtonText() }}
        </wd-button>
      </view>
    </view>
  </wd-cell>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'YtMapSelector',
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    modelValue: {
      type: [Object, String],
      default: () => ({}),
    },
    label: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    required: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    // 计算实际使用的属性值
    const actualLabel = computed(() => {
      return props.label || props.item?.title || '地图绘制'
    })

    const actualPlaceholder = computed(() => {
      // 从配置中获取允许的绘制模式
      const allowedModes = props.item?.props?.allowedModes || ['Point', 'Polyline', 'Polygon']

      // 根据允许的模式生成提示文本
      const modeTexts = {
        Point: '点',
        Polyline: '线',
        Polygon: '面',
      }

      const supportedModes = allowedModes.map((mode) => modeTexts[mode] || mode).join('、')

      return (
        props.placeholder ||
        props.item?.props?.placeholder ||
        props.item?.info ||
        `点击绘制${supportedModes}`
      )
    })

    const actualRequired = computed(() => {
      return props.required || !!props.item?.$required
    })

    const isRequired = computed(() => {
      return !!props.item?.$required
    })

    // 检查是否有数据
    const hasData = computed(() => {
      if (!props.modelValue) return false

      // 处理字符串格式（如POINT坐标）
      if (typeof props.modelValue === 'string') {
        return props.modelValue.trim().length > 0
      }

      // 处理对象格式
      if (typeof props.modelValue === 'object') {
        const total =
          (props.modelValue.points?.length || 0) +
          (props.modelValue.polylines?.length || 0) +
          (props.modelValue.polygons?.length || 0)
        return total > 0
      }

      return false
    })

    // 显示文本计算
    const displayText = computed(() => {
      if (!hasData.value) return ''

      // 处理字符串格式（WKT数据）
      if (typeof props.modelValue === 'string') {
        const wktType = getWKTType(props.modelValue)
        switch (wktType) {
          case 'POINT':
            return '位置标记点'
          case 'LINESTRING':
            return '绘制路径'
          case 'POLYGON':
            return '绘制区域'
          case 'MULTIPOINT':
            return '位置标记点'
          case 'MULTILINESTRING':
            return '绘制路径'
          case 'MULTIPOLYGON':
            return '绘制区域'
          case 'GEOMETRYCOLLECTION':
            return '复合几何图形'
          default:
            return '地理数据'
        }
      }

      // 处理对象格式
      const total =
        (props.modelValue.points?.length || 0) +
        (props.modelValue.polylines?.length || 0) +
        (props.modelValue.polygons?.length || 0)

      if (total === 0) return ''

      const parts = []
      if (props.modelValue.points?.length) {
        parts.push(`${props.modelValue.points.length}个点`)
      }
      if (props.modelValue.polylines?.length) {
        parts.push(`${props.modelValue.polylines.length}条线`)
      }
      if (props.modelValue.polygons?.length) {
        parts.push(`${props.modelValue.polygons.length}个面`)
      }

      return `已绘制图形`
    })

    // 获取结果详情
    const getResultBreakdown = () => {
      if (!hasData.value) return ''

      // 获取允许的绘制模式
      const allowedModes = props.item?.props?.allowedModes || ['Point', 'Polyline', 'Polygon']

      // 处理字符串格式（WKT数据）
      if (typeof props.modelValue === 'string') {
        const wktType = getWKTType(props.modelValue)

        // 判断是单图形还是多图形模式
        const isMultiMode = wktType?.startsWith('MULTI') || wktType === 'GEOMETRYCOLLECTION'
        const modeText = isMultiMode ? '多图形模式' : '单图形模式'

        return modeText
      }

      // 处理对象格式
      const parts = []

      // 只显示配置允许的模式对应的数据
      if (allowedModes.includes('Point') && props.modelValue.points?.length) {
        parts.push(`${props.modelValue.points.length}个标记点`)
      }
      if (allowedModes.includes('Polyline') && props.modelValue.polylines?.length) {
        parts.push(`${props.modelValue.polylines.length}条路径`)
      }
      if (allowedModes.includes('Polygon') && props.modelValue.polygons?.length) {
        parts.push(`${props.modelValue.polygons.length}个区域`)
      }

      // 如果只允许点模式且有点数据，显示具体坐标
      if (
        allowedModes.length === 1 &&
        allowedModes[0] === 'Point' &&
        props.modelValue.points?.length === 1
      ) {
        const point = props.modelValue.points[0]
        if (point.lng && point.lat) {
          return `经度: ${point.lng.toFixed(6)}°, 纬度: ${point.lat.toFixed(6)}°`
        }
      }

      return parts.length > 0 ? parts.join(' • ') : '包含绘制数据'
    }

    // 显示地图选择器
    const showMapSelector = () => {
      // 从item.props获取配置参数
      const mapProps = props.item?.props || {}

      // 准备传递给地图绘制页面的参数
      const params = {
        title: actualLabel.value,
        fieldName: 'mapSelector',
        // 使用配置的中心点坐标，如果没有则使用默认值
        latitude: mapProps.centerLatitude || 43.823755,
        longitude: mapProps.centerLongitude || 125.277062,
        // 使用配置的缩放级别，如果没有则使用默认值
        scale: mapProps.zoom || 16,
        // 传递允许的绘制模式
        allowedModes: mapProps.allowedModes
          ? JSON.stringify(mapProps.allowedModes)
          : JSON.stringify(['Point', 'Polyline', 'Polygon']),
        // 传递坐标系类型，告诉mapDraw页面输入数据的坐标系
        coordType: 'WGS84', // WKT数据通常使用WGS84坐标系
      }

      // 如果有现有数据，传递给页面
      if (hasData.value) {
        console.log('🗺️ 传递现有数据到mapDraw页面:', {
          数据类型: typeof props.modelValue,
          数据内容: props.modelValue,
          WKT类型: typeof props.modelValue === 'string' ? getWKTType(props.modelValue) : '非字符串',
        })

        if (typeof props.modelValue === 'string') {
          // 处理WKT字符串数据
          const wktType = getWKTType(props.modelValue)

          if (wktType) {
            // 将WKT字符串转换为mapDraw可以理解的格式
            const convertedData = convertWKTToMapData(props.modelValue)
            if (convertedData) {
              params.existingData = encodeURIComponent(JSON.stringify(convertedData))
              console.log('🗺️ WKT数据转换结果:', convertedData)
            } else {
              // 如果转换失败，直接传递原始WKT字符串
              params.coordinates = encodeURIComponent(props.modelValue)
              console.log('🗺️ WKT转换失败，传递原始字符串')
            }
          } else {
            // 不是标准WKT格式，直接传递
            params.coordinates = encodeURIComponent(props.modelValue)
          }
        } else {
          // 处理对象格式数据
          params.existingData = encodeURIComponent(JSON.stringify(props.modelValue))
          console.log('🗺️ 传递对象数据:', props.modelValue)
        }
      }

      // 构建URL参数
      const query = Object.entries(params)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&')

      console.log('🗺️ 地图绘制参数:', {
        中心点: `${params.latitude}, ${params.longitude}`,
        缩放级别: params.scale,
        允许模式: mapProps.allowedModes || ['Point', 'Polyline', 'Polygon'],
        坐标系: params.coordType,
        URL: `/pages/mapDraw/mapDraw?${query}`,
      })

      // 监听页面返回结果
      uni.$on('mapDrawResult', (data) => {
        if (data.fieldName === 'mapSelector') {
          console.log('🗺️ 接收到地图绘制结果:', data.result)
          emit('update:modelValue', data.result)
          emit('change', data.result)
          // 移除监听器
          uni.$off('mapDrawResult')
        }
      })

      uni.navigateTo({
        url: `/pages/mapDraw/mapDraw?${query}`,
      })
    }

    // 将WKT字符串转换为mapDraw页面可以理解的数据格式
    const convertWKTToMapData = (wktString) => {
      if (!wktString || typeof wktString !== 'string') return null

      const wktType = getWKTType(wktString)
      console.log('🔄 开始WKT转换:', { wktString, wktType })

      try {
        switch (wktType) {
          case 'POINT': {
            const pointMatch = wktString.match(/POINT\s*\(\s*([\d.-]+)\s+([\d.-]+)\s*\)/i)
            if (pointMatch) {
              const lng = parseFloat(pointMatch[1])
              const lat = parseFloat(pointMatch[2])
              return {
                points: [
                  {
                    id: Date.now(),
                    latitude: lat,
                    longitude: lng,
                    iconPath: '/static/fixedPoint.png',
                    width: 32,
                    height: 32,
                  },
                ],
                polylines: [],
                polygons: [],
              }
            }
            break
          }

          case 'LINESTRING': {
            const coordMatches = wktString.match(/LINESTRING\s*\(\s*(.*?)\s*\)/i)
            if (coordMatches) {
              const coordPairs = coordMatches[1].split(',').map((pair) => pair.trim())
              const points = coordPairs
                .map((pair, index) => {
                  const [lng, lat] = pair.split(/\s+/).map(parseFloat)
                  return {
                    id: Date.now() + index,
                    latitude: lat,
                    longitude: lng,
                  }
                })
                .filter((point) => !isNaN(point.latitude) && !isNaN(point.longitude))

              if (points.length >= 2) {
                return {
                  points: [],
                  polylines: [
                    {
                      points,
                      width: 4,
                      color: '#2196F3',
                      borderColor: '#1976D2',
                      borderWidth: 2,
                    },
                  ],
                  polygons: [],
                }
              }
            }
            break
          }

          case 'POLYGON': {
            const coordMatches = wktString.match(/POLYGON\s*\(\s*\((.*?)\)\s*\)/i)
            if (coordMatches) {
              const coordPairs = coordMatches[1].split(',').map((pair) => pair.trim())
              const points = coordPairs
                .map((pair, index) => {
                  const [lng, lat] = pair.split(/\s+/).map(parseFloat)
                  return {
                    id: Date.now() + index,
                    latitude: lat,
                    longitude: lng,
                  }
                })
                .filter((point) => !isNaN(point.latitude) && !isNaN(point.longitude))

              if (points.length >= 3) {
                return {
                  points: [],
                  polylines: [],
                  polygons: [
                    {
                      points,
                      strokeWidth: 3,
                      strokeColor: '#67c23a', // 已完成状态颜色
                      fillColor: '#67c23a4d', // 已完成状态颜色的透明版本
                    },
                  ],
                }
              }
            }
            break
          }

          // TODO: 添加对MULTIPOINT, MULTILINESTRING, MULTIPOLYGON的支持
          default:
            console.warn('🚫 暂不支持的WKT类型:', wktType)
            return null
        }
      } catch (error) {
        console.error('❌ WKT转换出错:', error)
        return null
      }

      return null
    }

    // 清除选择
    const clearSelection = () => {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除已绘制的地图数据吗？',
        confirmText: '清除',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            emit('update:modelValue', null)
            emit('change', null)
            uni.showToast({
              title: '已清除',
              icon: 'success',
            })
          }
        },
      })
    }

    // 获取编辑按钮文本
    const getEditButtonText = () => {
      const allowedModes = props.item?.props?.allowedModes || ['Point', 'Polyline', 'Polygon']

      if (allowedModes.length === 1) {
        switch (allowedModes[0]) {
          case 'Point':
            return '重新标记'
          case 'Polyline':
            return '重新画线'
          case 'Polygon':
            return '重新画区域'
          default:
            return '重新绘制'
        }
      }

      return '重新绘制'
    }

    // 获取绘制按钮文本
    const getDrawButtonText = () => {
      const allowedModes = props.item?.props?.allowedModes || ['Point', 'Polyline', 'Polygon']

      if (allowedModes.length === 1) {
        switch (allowedModes[0]) {
          case 'Point':
            return '标记位置'
          case 'Polyline':
            return '绘制路径'
          case 'Polygon':
            return '绘制区域'
          default:
            return '开始绘制'
        }
      }

      // 多种模式时显示通用文字
      const modeTexts = {
        Point: '点',
        Polyline: '线',
        Polygon: '面',
      }

      const supportedModes = allowedModes.map((mode) => modeTexts[mode] || mode).join('/')
      return `绘制${supportedModes}`
    }

    // 解析WKT数据类型
    const getWKTType = (wktString) => {
      if (!wktString || typeof wktString !== 'string') return null

      const upperWKT = wktString.trim().toUpperCase()

      if (upperWKT.startsWith('POINT')) return 'POINT'
      if (upperWKT.startsWith('MULTIPOINT')) return 'MULTIPOINT'
      if (upperWKT.startsWith('LINESTRING')) return 'LINESTRING'
      if (upperWKT.startsWith('MULTILINESTRING')) return 'MULTILINESTRING'
      if (upperWKT.startsWith('POLYGON')) return 'POLYGON'
      if (upperWKT.startsWith('MULTIPOLYGON')) return 'MULTIPOLYGON'
      if (upperWKT.startsWith('GEOMETRYCOLLECTION')) return 'GEOMETRYCOLLECTION'

      return null
    }

    // 解析WKT数据详情
    const parseWKTData = (wktString) => {
      if (!wktString || typeof wktString !== 'string') return '无效的地理数据'

      const wktType = getWKTType(wktString)

      try {
        switch (wktType) {
          case 'POINT': {
            const pointMatch = wktString.match(/POINT\s*\(\s*([\d.-]+)\s+([\d.-]+)\s*\)/i)
            if (pointMatch) {
              const lng = parseFloat(pointMatch[1])
              const lat = parseFloat(pointMatch[2])
              return `经度: ${lng.toFixed(6)}°, 纬度: ${lat.toFixed(6)}°`
            }
            return '点坐标数据'
          }

          case 'LINESTRING': {
            const coordMatches = wktString.match(/LINESTRING\s*\(\s*(.*?)\s*\)/i)
            if (coordMatches) {
              const coords = coordMatches[1].split(',').map((pair) => pair.trim())
              return `路径包含 ${coords.length} 个坐标点`
            }
            return '线条路径数据'
          }

          case 'MULTILINESTRING': {
            const lineMatches = wktString.match(/MULTILINESTRING\s*\(\s*(.*?)\s*\)/i)
            if (lineMatches) {
              // 计算包含的线条数量
              const lineCount = (lineMatches[1].match(/\(/g) || []).length
              return `包含 ${lineCount} 条路径线`
            }
            return '多线条路径数据'
          }

          case 'POLYGON': {
            const coordMatches = wktString.match(/POLYGON\s*\(\s*\((.*?)\)\s*\)/i)
            if (coordMatches) {
              const coords = coordMatches[1].split(',').map((pair) => pair.trim())
              return `区域包含 ${coords.length} 个边界点`
            }
            return '多边形区域数据'
          }

          case 'MULTIPOLYGON': {
            const polygonMatches = wktString.match(/MULTIPOLYGON\s*\(\s*(.*?)\s*\)/i)
            if (polygonMatches) {
              // 计算包含的多边形数量
              const polygonCount = (polygonMatches[1].match(/\(\(/g) || []).length
              return `包含 ${polygonCount} 个区域`
            }
            return '多区域数据'
          }

          case 'GEOMETRYCOLLECTION': {
            const collectionMatch = wktString.match(/GEOMETRYCOLLECTION\s*\(\s*(.*?)\s*\)/i)
            if (collectionMatch) {
              const content = collectionMatch[1]
              const pointCount = (content.match(/POINT/gi) || []).length
              const lineCount = (content.match(/LINESTRING/gi) || []).length
              const polygonCount = (content.match(/POLYGON/gi) || []).length

              const parts = []
              if (pointCount > 0) parts.push(`${pointCount}个点`)
              if (lineCount > 0) parts.push(`${lineCount}条线`)
              if (polygonCount > 0) parts.push(`${polygonCount}个面`)

              return parts.length > 0 ? `复合图形: ${parts.join('、')}` : '复合几何图形'
            }
            return '复合几何图形数据'
          }

          default:
            return '包含地理坐标信息'
        }
      } catch (error) {
        console.warn('解析WKT数据时出错:', error)
        return '地理数据解析异常'
      }
    }

    return {
      actualLabel,
      actualPlaceholder,
      actualRequired,
      isRequired,
      hasData,
      displayText,
      getResultBreakdown,
      showMapSelector,
      clearSelection,
      getEditButtonText,
      getDrawButtonText,
      getWKTType,
      parseWKTData,
      convertWKTToMapData,
    }
  },
}
</script>

<style lang="scss" scoped>
.map-selector-wrapper {
  // 预留容器样式
}

.title-wrapper {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.title {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}

:deep(.map-selector-cell) {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
}

.map-selector-container {
  width: 100%;
  padding: 16rpx 0;
}
/* 有数据时的显示样式 */
.map-result-display {
  position: relative;
  padding: 20rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f7fa 100%);
  border: 2rpx solid #10b981;
  border-radius: 12rpx;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.result-title {
  flex: 1;
  margin-left: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #065f46;
}

.clear-btn {
  padding: 8rpx !important;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 8rpx;

  &:hover {
    background: rgba(239, 68, 68, 0.2);
  }
}

.result-details {
  margin-bottom: 16rpx;
}

.detail-text {
  font-size: 24rpx;
  line-height: 1.4;
  color: #047857;
}

.edit-btn {
  width: 100%;
  color: #10b981 !important;
  background: rgba(16, 185, 129, 0.05) !important;
  border-color: #10b981 !important;
  border-radius: 8rpx;

  &:active {
    background: rgba(16, 185, 129, 0.1) !important;
  }
}
/* 无数据时的显示样式 */
.map-empty-display {
  padding: 40rpx 20rpx;
  text-align: center;
  background: #fafafa;
  border: 2rpx dashed #d1d5db;
  border-radius: 12rpx;
  transition: all 0.3s ease;

  &:active {
    background: #f5f5f5;
    transform: scale(0.98);
  }
}

.empty-icon {
  margin-bottom: 16rpx;
}

.empty-text {
  display: block;
  margin-bottom: 24rpx;
  font-size: 26rpx;
  line-height: 1.4;
  color: #9ca3af;
}

.draw-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border: none !important;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;

  &:active {
    box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.4);
    transform: translateY(2rpx);
  }

  &:disabled {
    background: #e5e7eb !important;
    box-shadow: none;
    transform: none;
  }
}
/* 按钮图标样式优化 */
:deep(.wd-button .wd-icon) {
  margin-right: 6rpx;
}
/* 标签样式 */
:deep(.wd-tag) {
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  border-radius: 4rpx;
}
/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .result-header {
    flex-wrap: wrap;
    gap: 8rpx;
  }

  .result-title {
    font-size: 28rpx;
  }

  .detail-text {
    font-size: 22rpx;
  }
}
/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .map-result-display {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    border-color: #10b981;
  }

  .result-title,
  .detail-text {
    color: #d1fae5;
  }

  .map-empty-display {
    background: #374151;
    border-color: #4b5563;
  }

  .empty-text {
    color: #d1d5db;
  }

  .title {
    color: #f9fafb;
  }
}
</style>
