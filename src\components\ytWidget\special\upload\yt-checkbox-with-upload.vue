<template>
  <view class="checkbox-with-upload">
    <!-- 标题显示部分 -->
    <view v-if="item.title" class="title-section">
      <text class="title-text">{{ item.title }}</text>
    </view>

    <!-- 复选框部分 -->
    <wd-cell center clickable vertical @click.stop="handleCheckboxClick">
      <template #title>
        <view class="checkbox-content">
          <wd-checkbox
            ref="checkBox"
            v-model="internalCheckboxValue"
            custom-style="margin-right: 8px; flex-shrink: 0;"
            shape="square"
            @change.stop="handleCheckboxClick"
          />
          <text class="checkbox-label">{{ checkboxLabel }}</text>
        </view>
      </template>
    </wd-cell>

    <!-- 上传图片部分 - 只有勾选时才显示 -->
    <view v-if="internalCheckboxValue" class="upload-section">
      <wd-cell vertical>
        <template #title>
          <text class="upload-title">上传相关图片</text>
        </template>
        <wd-upload
          :accept="'image'"
          :action="uploadAction"
          :auto-upload="true"
          :file-list="internalUploadFiles"
          :limit="uploadLimit"
          :multiple="true"
          class="dynamic-upload"
          @change="handleUploadChange"
          @fail="handleUploadFail"
          @remove="handleUploadRemove"
          @success="handleUploadSuccess"
        />
      </wd-cell>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { getEvnBaseUrl } from '@/utils'
import type { FormItem } from '@/types/form'

// {{CHENGQI:
// Action: Modified; Timestamp: 2025-01-25 16:25:00 +08:00; Reason: 保持原有{checked,images}对象结构，但简化images数组只存储URL字符串而非完整文件对象; Principle_Applied: 数据结构一致性、简化数据存储;
// }}

interface Props {
  item: FormItem
  modelValue?: {
    checked: boolean
    images: string[] // 只存储URL字符串数组
  }
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: { checked: boolean; images: string[] }]
}>()

// 内部状态 - 使用响应式数据管理
const internalCheckboxValue = ref<boolean>(false)
const internalUploadFiles = ref<any[]>([]) // 用于wot-design-uni组件的完整文件对象
const internalImageUrls = ref<string[]>([]) // 只存储URL字符串，用于emit
const isInternalChange = ref(false) // 防止循环更新的标志

// 计算属性
const checkboxLabel = computed(() => {
  return props.item?.props?.checkboxLabel || '请选择'
})

const uploadLimit = computed(() => {
  return props.item?.props?.uploadLimit || 3
})

const uploadAction = computed(() => {
  const baseUrl = getEvnBaseUrl()
  return props.item?.props?.uploadAction || `${baseUrl}/infra/file/upload`
})

// 从外部props同步到内部状态
const syncFromProps = () => {
  if (props.modelValue && typeof props.modelValue === 'object') {
    const newChecked = Boolean(props.modelValue.checked)
    const newImages = Array.isArray(props.modelValue.images) ? [...props.modelValue.images] : []

    // 只有当值真正不同时才更新
    if (internalCheckboxValue.value !== newChecked) {
      internalCheckboxValue.value = newChecked
    }

    // 比较URL数组内容是否相同
    if (JSON.stringify(internalImageUrls.value) !== JSON.stringify(newImages)) {
      internalImageUrls.value = newImages

      // 同步更新upload组件需要的文件对象数组
      internalUploadFiles.value = newImages.map((url, index) => ({
        uid: `${Date.now()}_${index}`,
        name: `image_${index}.jpg`,
        status: 'success',
        url,
      }))
    }
  } else {
    // 重置为默认状态
    if (internalCheckboxValue.value !== false) {
      internalCheckboxValue.value = false
    }
    if (internalImageUrls.value.length > 0) {
      internalImageUrls.value = []
      internalUploadFiles.value = []
    }
  }
}

// 监听外部值变化 - 只在非内部变化时同步
watch(
  () => props.modelValue,
  () => {
    if (!isInternalChange.value) {
      syncFromProps()
    }
  },
  { deep: true, immediate: true },
)

// 安全的emit方法
const safeEmit = async () => {
  const newValue = {
    checked: internalCheckboxValue.value,
    images: [...internalImageUrls.value], // 只emit URL字符串数组
  }

  console.log('📤 复选框上传组件 emit:', newValue)

  // 设置内部变化标志
  isInternalChange.value = true

  // emit更新
  emits('update:modelValue', newValue)

  // 使用nextTick确保更新完成后重置标志
  await nextTick()
  isInternalChange.value = false
}

// 复选框点击处理
const handleCheckboxClick = () => {
  // 如果当前是选中状态，且有上传的图片，则弹出确认提示
  if (internalCheckboxValue.value && internalImageUrls.value.length > 0) {
    uni.showModal({
      title: '确认取消',
      content: '取消选择将清空已上传的图片，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          // 用户确认取消
          internalCheckboxValue.value = false
          internalImageUrls.value = []
          internalUploadFiles.value = []
          safeEmit()
        }
        // 如果用户点击取消，不做任何操作，保持原状态
      },
    })
  } else {
    // 如果是从未选中到选中，或者没有图片，直接切换状态
    internalCheckboxValue.value = !internalCheckboxValue.value
    safeEmit()
  }
}

const handleCheckboxChange = (value: boolean) => {
  // 如果是通过点击复选框本身触发的change事件
  // 需要与handleCheckboxClick保持一致的逻辑
  if (!value && internalImageUrls.value.length > 0) {
    // 如果是取消选中且有图片，弹出确认提示
    uni.showModal({
      title: '确认取消',
      content: '取消选择将清空已上传的图片，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          // 用户确认取消
          internalCheckboxValue.value = false
          internalImageUrls.value = []
          internalUploadFiles.value = []
          safeEmit()
        } else {
          // 用户取消操作，恢复选中状态
          internalCheckboxValue.value = true
        }
      },
    })
  } else {
    // 其他情况直接更新状态
    internalCheckboxValue.value = value
    if (!internalCheckboxValue.value) {
      internalImageUrls.value = []
      internalUploadFiles.value = []
    }
    safeEmit()
  }
}

// 上传相关处理
const handleUploadChange = (event: any) => {
  console.log('📁 上传文件变化事件:', event)
  console.log('📁 事件结构 - fileList:', event?.fileList)

  // 根据wot-design-uni文档，change事件参数结构: { fileList }
  if (event && event.fileList && Array.isArray(event.fileList)) {
    console.log('📁 处理fileList数据:', event.fileList)

    // 直接使用传入的fileList，但确保数据完整性
    internalUploadFiles.value = event.fileList.map((file) => ({
      ...file,
      uid: file.uid || `${Date.now()}_${Math.random()}`,
      name: file.name || '未知文件',
      status: file.status || 'uploading',
      url: file.url || '',
    }))

    // 更新URL字符串数组（用于emit）- 只处理有效的网络URL
    internalImageUrls.value = event.fileList
      .filter((file) => {
        const hasValidUrl =
          file.url && (file.url.startsWith('http://') || file.url.startsWith('https://'))
        const isSuccess = file.status === 'success'
        return hasValidUrl && isSuccess
      })
      .map((file) => file.url)

    console.log('📁 Change事件 - 过滤后的有效URL:', internalImageUrls.value)
    console.log(
      '📁 Change事件 - 文件状态:',
      event.fileList.map((f) => ({ name: f.name, status: f.status, url: f.url })),
    )

    // 自动选中复选框（如果有有效图片）
    if (internalImageUrls.value.length > 0) {
      internalCheckboxValue.value = true
    }

    // 如果没有有效URL但有选中状态，保持选中（等待success事件）
    // 只有当完全没有文件时才取消选中
    if (event.fileList.length === 0) {
      internalCheckboxValue.value = false
    }
  } else if (Array.isArray(event)) {
    // 降级处理：如果直接传递的是数组
    console.log('📁 降级处理：直接传递数组')
    internalUploadFiles.value = event.map((file) => ({
      ...file,
      uid: file.uid || `${Date.now()}_${Math.random()}`,
      name: file.name || '未知文件',
      status: file.status || 'uploading',
      url: file.url || '',
    }))

    // 只处理有效的网络URL
    internalImageUrls.value = event
      .filter((file) => {
        const hasValidUrl =
          file.url && (file.url.startsWith('http://') || file.url.startsWith('https://'))
        const isSuccess = file.status === 'success'
        return hasValidUrl && isSuccess
      })
      .map((file) => file.url)

    console.log('📁 Change事件(数组) - 过滤后的有效URL:', internalImageUrls.value)

    if (internalImageUrls.value.length > 0) {
      internalCheckboxValue.value = true
    } else if (event.length === 0) {
      internalCheckboxValue.value = false
    }
  } else {
    // 清空状态
    console.log('📁 清空文件列表')
    internalUploadFiles.value = []
    internalImageUrls.value = []
    internalCheckboxValue.value = false
  }

  // 只有在有有效URL时才emit，避免emit临时路径
  if (internalImageUrls.value.length > 0) {
    console.log('📁 Change事件触发emit，有效URL数量:', internalImageUrls.value.length)
    safeEmit()
  }
}

const handleUploadSuccess = (event: any) => {
  console.log('✅ 上传成功事件:', event)
  console.log('✅ 事件结构 - file:', event?.file)
  console.log('✅ 事件结构 - fileList:', event?.fileList)

  // 根据wot-design-uni文档，success事件参数为 { file, fileList, formData }
  // file对象包含response字段
  let imageUrl = ''

  if (event?.file?.response) {
    const response = event.file.response
    console.log('📤 从file.response获取数据:', response)
    console.log('📤 response数据类型:', typeof response)

    if (typeof response === 'string') {
      try {
        const responseData = JSON.parse(response)
        console.log('📤 解析后的响应对象:', responseData)
        // 直接获取data字段的字符串值
        imageUrl = responseData?.data || ''
        console.log('📤 解析字符串响应，提取URL:', imageUrl)
      } catch (e) {
        console.warn('响应解析失败:', e)
        // 如果解析失败，可能响应本身就是URL字符串
        if (response.startsWith('http')) {
          imageUrl = response
          console.log('📤 响应字符串本身就是URL:', imageUrl)
        }
      }
    } else if (typeof response === 'object') {
      console.log('📤 响应对象结构:', response)
      // 直接获取data字段的字符串值
      imageUrl = response?.data || ''
      console.log('📤 解析对象响应，提取URL:', imageUrl)

      // 如果data字段为空，尝试其他可能的字段
      if (!imageUrl) {
        imageUrl = response?.url || response?.path || ''
        console.log('📤 尝试其他字段，URL:', imageUrl)
      }
    }
  }

  console.log('📤 最终提取的图片URL:', imageUrl)

  // 验证URL是否为有效的网络地址
  const isValidUrl = imageUrl && (imageUrl.startsWith('http://') || imageUrl.startsWith('https://'))
  console.log('📤 URL有效性检查:', isValidUrl)

  // 如果有有效的图片URL，更新文件列表和URL数组
  if (imageUrl && isValidUrl) {
    // 直接使用event.fileList来更新，这是wot-design-uni推荐的方式
    if (event?.fileList && Array.isArray(event.fileList)) {
      console.log('📤 使用event.fileList更新文件列表')

      // 更新文件列表，确保URL正确
      const updatedFileList = event.fileList.map((file: any) => {
        // 如果这是刚上传成功的文件，更新其URL
        if (file.uid === event.file?.uid) {
          return {
            ...file,
            url: imageUrl,
            status: 'success',
          }
        }
        return file
      })

      internalUploadFiles.value = updatedFileList
      console.log('📤 更新后的文件列表:', internalUploadFiles.value)
    } else {
      // 降级处理：手动更新文件列表
      console.log('📤 降级处理：手动更新文件列表')
      const currentFiles = internalUploadFiles.value.slice()

      // 查找对应的文件项并更新URL
      const fileIndex = currentFiles.findIndex(
        (file) => file.uid === event.file?.uid || file.name === event.file?.name,
      )

      if (fileIndex !== -1) {
        currentFiles[fileIndex] = {
          ...currentFiles[fileIndex],
          url: imageUrl,
          status: 'success',
        }
        console.log('📤 找到文件并更新:', fileIndex)
      } else {
        // 如果找不到，更新最后一个文件
        const lastIndex = currentFiles.length - 1
        if (lastIndex >= 0) {
          currentFiles[lastIndex] = {
            ...currentFiles[lastIndex],
            url: imageUrl,
            status: 'success',
          }
          console.log('📤 更新最后一个文件:', lastIndex)
        }
      }

      internalUploadFiles.value = currentFiles
    }

    // 更新URL数组 - 只存储成功上传的图片URL字符串
    internalImageUrls.value = internalUploadFiles.value
      .filter(
        (file) =>
          file.url &&
          file.status === 'success' &&
          (file.url.startsWith('http://') || file.url.startsWith('https://')),
      )
      .map((file) => file.url)

    console.log('📤 更新后的URL数组:', internalImageUrls.value)

    // 自动选中复选框
    if (internalImageUrls.value.length > 0) {
      internalCheckboxValue.value = true
    }

    safeEmit()
  } else {
    console.warn('⚠️ 未能从响应中提取到有效的图片URL')
    console.warn('⚠️ 原始event:', event)
    console.warn('⚠️ file.response:', event?.file?.response)
    console.warn('⚠️ 提取的URL:', imageUrl)
    console.warn('⚠️ URL有效性:', isValidUrl)
  }

  uni.showToast({
    title: '图片上传成功',
    icon: 'success',
    duration: 1500,
  })
}

const handleUploadFail = (event: any) => {
  console.error('❌ 上传失败:', event)
  uni.showToast({
    title: '图片上传失败',
    icon: 'none',
    duration: 2000,
  })
}

const handleUploadRemove = (event: any) => {
  console.log('🗑️ 移除图片:', event)

  // 从文件数组中移除对应项
  if (event?.file) {
    internalUploadFiles.value = internalUploadFiles.value.filter(
      (file) => file.uid !== event.file.uid,
    )
  }

  // 更新URL数组
  internalImageUrls.value = internalUploadFiles.value
    .filter((file) => file.url && file.status === 'success')
    .map((file) => file.url)

  // 如果没有图片了，取消选中
  if (internalImageUrls.value.length === 0) {
    internalCheckboxValue.value = false
  }

  safeEmit()
}

// 组件挂载时初始化
onMounted(() => {
  syncFromProps()
})
</script>

<style lang="scss" scoped>
.checkbox-with-upload {
  .title-section {
    padding: 8px 12px;
    margin-bottom: 12px;
    background: #f8fafc;
    border-left: 3px solid #3b82f6;
    // border-radius: 4px;

    .title-text {
      font-size: 15px;
      font-weight: 500;
      line-height: 1.4;
      color: #1e293b;
    }
  }

  .checkbox-content {
    display: flex;
    align-items: flex-start;
    width: 100%;

    .checkbox-label {
      flex: 1;
      font-size: 14px;
      line-height: 1.5;
      color: #333;
      word-break: break-all;
    }
  }

  .upload-section {
    margin-top: 8px;

    .upload-title {
      margin-bottom: 8px;
      font-size: 14px;
      color: #666;
    }
  }

  .dynamic-upload {
    ::v-deep(.wd-upload__file-list) {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 8px;
    }

    ::v-deep(.wd-upload__file) {
      margin: 0;
    }
  }
}
</style>
