<template>
  <wd-cell
    :disabled="item.props?.disabled || false"
    :required="actualRequired"
    custom-class="upload-cell"
    vertical
  >
    <template #title>
      <view>{{ actualLabel }}</view>
    </template>
    <view class="upload-container">
      <wd-upload
        :accept="actualAccept as any"
        :action="actualAction"
        :auto-upload="item.props?.autoUpload !== false"
        :before-preview="handleBeforePreview"
        :before-upload="handleBeforeUpload"
        :camera="item.props?.camera || 'back'"
        :compressed="item.props?.compressed !== false"
        :disabled="item.props?.disabled || false"
        :extension="item.props?.extension"
        :file-list="fileList"
        :image-mode="(item.props?.imageMode || 'aspectFill') as any"
        :limit="actualLimit"
        :max-duration="item.props?.maxDuration || 60"
        :multiple="actualMultiple"
        :reupload="item.props?.reupload || false"
        :success-status="item.props?.successStatus || 200"
        :upload-method="item.props?.uploadMethod"
        class="upload-widget"
        @change="handleChange"
        @chooseerror="handleChooseError"
        @click="handleClick"
        @fail="handleFail"
        @progress="handleProgress"
        @remove="handleRemove"
        @success="handleSuccess"
      >
        <template v-if="$slots.default" #default>
          <slot></slot>
        </template>
        <template v-else #default>
          <view class="upload-button">
            <wd-icon color="#c8c9cc" name="camera" size="24px" />
            <text class="upload-text">{{ actualPlaceholder }}</text>
          </view>
        </template>

        <template v-if="$slots['preview-cover']" #preview-cover="{ file, index }">
          <slot :file="file" :index="index" name="preview-cover"></slot>
        </template>
      </wd-upload>
    </view>
  </wd-cell>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, toRaw, watch, nextTick } from 'vue'
import { getEvnBaseUrl } from '@/utils'
import type { FormItem } from '@/types/form' // Props 定义

// {{CHENGQI:
// Action: Modified; Timestamp: 2025-06-19 10:06:17 +08:00; Reason: 彻底重构响应式设计，移除深度watch，改用computed+手动控制，从根本上解决循环更新问题; Principle_Applied: 单一职责原则、避免循环依赖;
// }}

// Props 定义
interface Props {
  item?: FormItem
  modelValue?: any[]
  label?: string
  placeholder?: string
  required?: boolean
  action?: string
  accept?: string
  multiple?: boolean
  limit?: number
  maxSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  item: () => ({}) as FormItem,
  modelValue: () => [],
  label: '',
  placeholder: '',
  required: false,
  action: '',
  accept: undefined,
  multiple: undefined,
  limit: undefined,
  maxSize: undefined,
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: any[]]
  change: [files: any[]]
  success: [event: any]
  fail: [event: any]
  progress: [event: any]
  chooseerror: [event: any]
  remove: [event: any]
}>()

// 内部文件列表
const fileList = ref<any[]>([])
const isInternalChange = ref(false) // 防止循环更新标志

// 从外部props同步到内部状态
const syncFromProps = () => {
  const newFiles = Array.isArray(props.modelValue) ? [...props.modelValue] : []

  // 比较数组内容是否相同，避免不必要的更新
  if (JSON.stringify(fileList.value) !== JSON.stringify(newFiles)) {
    fileList.value = newFiles
    console.log('📥 yt-upload 从props同步文件列表:', toRaw(newFiles))
  }
}

// 监听外部值变化 - 只在非内部变化时同步
watch(
  () => props.modelValue,
  () => {
    if (!isInternalChange.value) {
      syncFromProps()
    }
  },
  { deep: true, immediate: true },
)

// 获取默认上传地址
const getDefaultUploadUrl = () => {
  const baseUrl = getEvnBaseUrl()
  const uploadUrl = `${baseUrl}/infra/file/upload`
  return uploadUrl
}

// 计算实际使用的属性值
const actualLabel = computed(() => {
  return props.label || props.item?.title || '文件上传'
})

const actualPlaceholder = computed(() => {
  return props.placeholder || props.item?.props?.placeholder || props.item?.info || '选择文件'
})

const actualRequired = computed(() => {
  return props.required || !!props.item?.$required
})

const actualAction = computed(() => {
  const action = props.action || props.item?.props?.action || getDefaultUploadUrl()
  return action
})

const actualAccept = computed(() => {
  const accept = props.accept || props.item?.props?.accept || 'image'
  return accept
})

const actualMultiple = computed(() => {
  return props.multiple ?? props.item?.props?.multiple ?? false
})

const actualLimit = computed(() => {
  return props.limit ?? props.item?.props?.limit ?? 3
})

const actualMaxSize = computed(() => {
  const maxSize = props.maxSize ?? props.item?.props?.maxSize
  // 如果没有配置maxSize，设置为一个很大的值(1000MB)避免限制
  return maxSize ?? 1000
})

// 处理点击事件
const handleClick = () => {
  // 基本的点击处理，无需特殊逻辑
}

// 安全的emit方法，防止循环更新
const safeEmit = async (files: any[]) => {
  console.log('📤 yt-upload emit files:', files)

  isInternalChange.value = true

  emit('update:modelValue', files)
  emit('change', files)

  await nextTick()
  isInternalChange.value = false
}

// 处理文件列表变化
const handleChange = ({ fileList: files }: { fileList: any[] }) => {
  console.log('📁 yt-upload 文件列表变化:', files)
  fileList.value = files
  safeEmit(files)
}

// 处理上传成功
const handleSuccess = (event: any) => {
  console.log('✅ yt-upload 上传成功:', event)

  // 解析响应数据获取图片URL
  let imageUrl = ''
  if (event?.response) {
    if (typeof event.response === 'string') {
      try {
        const responseData = JSON.parse(event.response)
        imageUrl = responseData?.data?.url || responseData?.url || ''
      } catch (e) {
        console.warn('响应数据解析失败:', e)
      }
    } else if (typeof event.response === 'object') {
      imageUrl = event.response?.data?.url || event.response?.url || ''
    }
  }

  // 更新文件列表中对应项的URL
  if (imageUrl && event?.index !== undefined) {
    const currentFiles = [...fileList.value]
    if (currentFiles[event.index]) {
      currentFiles[event.index] = {
        ...currentFiles[event.index],
        url: imageUrl,
        status: 'success',
      }
      fileList.value = currentFiles
      safeEmit(currentFiles)
    }
  }

  emit('success', event)

  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 2000,
  })
}

// 处理上传失败
const handleFail = (event: any) => {
  console.error('❌ yt-upload 上传失败:', event)
  emit('fail', event)

  uni.showToast({
    title: '上传失败',
    icon: 'none',
    duration: 2000,
  })
}

// 处理上传进度
const handleProgress = (event: any) => {
  emit('progress', event)
}

// 处理选择文件错误
const handleChooseError = (event: any) => {
  console.error('❌ yt-upload 选择文件失败:', event)
  emit('chooseerror', event)

  uni.showToast({
    title: '选择文件失败',
    icon: 'none',
    duration: 2000,
  })
}

// 处理删除文件
const handleRemove = (event: any) => {
  console.log('🗑️ yt-upload 删除文件:', event)
  emit('remove', event)
}

// 处理上传前钩子
const handleBeforeUpload = (params: any) => {
  // 如果配置了自定义的beforeUpload函数
  if (props.item?.props?.beforeUpload && typeof props.item.props.beforeUpload === 'function') {
    return props.item.props.beforeUpload(params)
  }

  // 默认允许上传
  if (typeof params?.resolve === 'function') {
    params.resolve(true)
    return
  }

  return true
}

// 处理预览前钩子
const handleBeforePreview = (params: any) => {
  // 如果配置了自定义的beforePreview函数
  if (props.item?.props?.beforePreview && typeof props.item.props.beforePreview === 'function') {
    return props.item.props.beforePreview(params)
  }

  // 默认允许预览
  if (typeof params?.resolve === 'function') {
    params.resolve(true)
    return
  }

  return true
}

// 组件挂载
onMounted(() => {
  syncFromProps()
})
</script>

<style lang="scss" scoped>
.upload-cell {
  ::v-deep(.wd-cell__value) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
}

.upload-container {
  width: 100%;
}

.upload-widget {
  width: 100%;
}

.upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: #fafafa;
  border: 1px dashed #dcdee0;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:active {
    background-color: #f0f0f0;
  }
}

.upload-text {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}
</style>
