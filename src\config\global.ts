/**
 * 全局配置文件
 */

// 租户配置
export const TENANT_CONFIG = {
  // 默认租户ID
  DEFAULT_TENANT_ID: import.meta.env.VITE_APP_DEFAULT_TENANT_ID || '1',
  // 是否启用租户功能
  ENABLED: import.meta.env.VITE_APP_TENANT_ENABLE === 'true',
  // 租户名称
  DEFAULT_TENANT_NAME: import.meta.env.VITE_APP_DEFAULT_LOGIN_TENANT || 'master',
}

// API 配置
export const API_CONFIG = {
  // 基础地址
  BASE_URL: import.meta.env.VITE_SERVER_BASEURL,
  // 超时时间（毫秒）
  TIMEOUT: 10000,
  // 重试次数
  RETRY_COUNT: 3,
}

// 应用配置
export const APP_CONFIG = {
  // 应用名称
  APP_NAME: '城市健康检查',
  // 版本号
  VERSION: '1.0.0',
  // 开发环境
  IS_DEV: import.meta.env.DEV,
}

// 表单配置
export const FORM_CONFIG = {
  // 默认分页大小
  PAGE_SIZE: 20,
  // 最大文件上传大小（MB）
  MAX_UPLOAD_SIZE: 10,
}
