/// <reference types="vite/client" />
/// <reference types="vite-svg-loader" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  /** 网站标题，应用名称 */
  readonly VITE_APP_TITLE: string
  /** 服务端口号 */
  readonly VITE_SERVER_PORT: string
  /** 后台接口地址 */
  readonly VITE_SERVER_BASEURL: string
  /** H5是否需要代理 */
  readonly VITE_APP_PROXY: 'true' | 'false'
  /** H5是否需要代理，需要的话有个前缀 */
  readonly VITE_APP_PROXY_PREFIX: string // 一般是/api
  /** 上传图片地址 */
  readonly VITE_UPLOAD_BASEURL: string
  /** 是否清除console */
  readonly VITE_DELETE_CONSOLE: 'true' | 'false'
  /** 是否显示sourcemap */
  readonly VITE_SHOW_SOURCEMAP: 'true' | 'false'
  /** 是否启用验证码 */
  readonly VITE_APP_CAPTCHA_ENABLE: 'true' | 'false'
  /** 是否启用租户 */
  readonly VITE_APP_TENANT_ENABLE: 'true' | 'false'
  /** 默认登录租户 */
  readonly VITE_APP_DEFAULT_LOGIN_TENANT: string
  /** 默认登录用户名 */
  readonly VITE_APP_DEFAULT_LOGIN_USERNAME: string
  /** 默认登录密码 */
  readonly VITE_APP_DEFAULT_LOGIN_PASSWORD: string
  /** 默认租户ID */
  readonly VITE_APP_DEFAULT_TENANT_ID: string
  /** 请求标签 */
  readonly VITE_APP_REQUESR_TAG: string
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
