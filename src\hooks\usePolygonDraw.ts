import { reactive, ref } from 'vue'

export default function usePolygonDraw() {
  const isDrawing = ref(false)
  const drawingType = ref(0) // 0: 未绘制, 1: 点, 2: 线段, 3: 多边形
  const completedPolygons = ref<any[]>([]) // 多边形
  const completedPolylines = ref<any[]>([]) // 线段
  const completedPoints = ref<any[]>([]) // 点标记
  const currentPolygon = reactive({
    points: [] as any[],
    strokeWidth: 3,
    strokeColor: '#f56c6c', // 使用未开始状态颜色
    fillColor: '#F56C6C33', // 使用未开始状态颜色的透明版本
  })

  /**
   * @description 添加点到当前绘制中
   * @param coordinate 坐标点 {latitude, longitude}
   */
  const addPoint = (coordinate: { latitude: number; longitude: number }) => {
    if (!isDrawing.value) return

    console.log('添加点:', coordinate, '绘制类型:', drawingType.value)

    if (drawingType.value === 1) {
      // 点类型：直接添加到完成的点列表
      const base64Icon = '/static/map/icon/marker.png'
      completedPoints.value.push({
        id: `user_${Date.now()}_${completedPoints.value.length}`,
        latitude: coordinate.latitude,
        longitude: coordinate.longitude,
        iconPath: base64Icon,
        width: 32,
        height: 32,
      })
      console.log('添加点标记完成，当前点数量:', completedPoints.value.length)
      console.log(
        '🔍 [DEBUG] 添加的点数据:',
        completedPoints.value[completedPoints.value.length - 1],
      )
    } else if (drawingType.value === 2 || drawingType.value === 3) {
      // 线段或多边形：添加到当前多边形的点列表
      currentPolygon.points.push(coordinate)
      console.log('添加到多边形点列表，当前点数量:', currentPolygon.points.length)

      // 设置不同的样式
      if (drawingType.value === 2) {
        // 线段样式
        currentPolygon.strokeColor = '#2196F3'
        currentPolygon.strokeWidth = 4
        currentPolygon.fillColor = '#00000000' // 线段无填充
      } else if (drawingType.value === 3) {
        // 多边形样式 - 使用已完成状态颜色
        currentPolygon.strokeColor = '#67c23a' // 已完成状态颜色
        currentPolygon.strokeWidth = 3
        currentPolygon.fillColor = '#67C23A33' // 已完成状态颜色的透明版本
      }
    }
  }

  /**
   * @description 删除上一个点
   */
  const removeLastPoint = () => {
    if (!isDrawing.value) return false

    if (drawingType.value === 1) {
      // 点类型：删除最后一个完成的点（只删除用户绘制的）
      const userPoints = completedPoints.value.filter((p) => p.id.toString().startsWith('user_'))
      if (userPoints.length > 0) {
        const lastUserPoint = userPoints[userPoints.length - 1]
        const index = completedPoints.value.findIndex((p) => p.id === lastUserPoint.id)
        if (index !== -1) {
          completedPoints.value.splice(index, 1)
          console.log('删除最后一个用户点标记，剩余点数量:', completedPoints.value.length)
          return true
        }
      }
    } else if (drawingType.value === 2 || drawingType.value === 3) {
      // 线段或多边形：删除当前多边形的最后一个点
      if (currentPolygon.points.length > 0) {
        currentPolygon.points.pop()
        console.log('删除多边形最后一个点，剩余点数量:', currentPolygon.points.length)
        return true
      }
    }
    return false
  }

  const validatePolygon = () => {
    if (currentPolygon.points.length < 3) {
      uni.showToast({ title: '多边形至少需要三个顶点', icon: 'none' })
      return false
    }
    return true
  }

  const validateLine = () => {
    if (currentPolygon.points.length < 2) {
      uni.showToast({ title: '线段至少需要两个点', icon: 'none' })
      return false
    }
    return true
  }

  const finishDrawing = () => {
    if (!isDrawing.value) return

    console.log('完成绘制，当前类型:', drawingType.value, '当前点数:', currentPolygon.points.length)

    if (drawingType.value === 3) {
      // 多边形需要验证
      if (!validatePolygon()) return

      const polygon = {
        points: [...currentPolygon.points],
        strokeWidth: currentPolygon.strokeWidth,
        strokeColor: currentPolygon.strokeColor,
        fillColor: currentPolygon.fillColor,
      }

      completedPolygons.value.push(polygon)
      console.log('多边形绘制完成:', polygon)
      uni.showToast({ title: '多边形绘制完成', icon: 'success' })
    } else if (drawingType.value === 2) {
      // 线段 - 添加到 completedPolylines
      if (!validateLine()) return

      const line = {
        points: [...currentPolygon.points],
        width: currentPolygon.strokeWidth,
        color: currentPolygon.strokeColor,
      }

      completedPolylines.value.push(line)
      console.log('线段绘制完成:', line)
      uni.showToast({ title: '线段绘制完成', icon: 'success' })
    }

    resetDrawing()
  }

  const startNewPolygon = (val: number) => {
    // 如果当前有未完成的绘制，先自动完成它
    if (isDrawing.value && currentPolygon.points.length > 0) {
      if (drawingType.value === 2 && currentPolygon.points.length >= 2) {
        // 自动完成当前线段
        const line = {
          points: [...currentPolygon.points],
          width: currentPolygon.strokeWidth,
          color: currentPolygon.strokeColor,
        }
        completedPolylines.value.push(line)
        console.log('自动完成上一条线段:', line)
        uni.showToast({ title: '已保存上一条线段', icon: 'success' })
      } else if (drawingType.value === 3 && currentPolygon.points.length >= 3) {
        // 自动完成当前多边形
        const polygon = {
          points: [...currentPolygon.points],
          strokeWidth: currentPolygon.strokeWidth,
          strokeColor: currentPolygon.strokeColor,
          fillColor: currentPolygon.fillColor,
        }
        completedPolygons.value.push(polygon)
        console.log('自动完成上一个多边形:', polygon)
        uni.showToast({ title: '已保存上一个多边形', icon: 'success' })
      }
    }

    if (val === 1) {
      console.log('开始绘制点', val)
      isDrawing.value = true
      drawingType.value = 1
      resetCurrentPolygon()
    } else if (val === 2) {
      console.log('开始绘制线段', val)
      isDrawing.value = true
      drawingType.value = 2
      resetCurrentPolygon()
      // 设置线段样式
      currentPolygon.strokeColor = '#2196F3'
      currentPolygon.strokeWidth = 4
      currentPolygon.fillColor = '#2196f300'
    } else if (val === 3) {
      console.log('开始绘制多边形', val)
      isDrawing.value = true
      drawingType.value = 3
      resetCurrentPolygon()
      // 设置多边形样式 - 使用已完成状态颜色
      currentPolygon.strokeColor = '#67c23a' // 已完成状态颜色
      currentPolygon.strokeWidth = 3
      currentPolygon.fillColor = '#67c23a66' // 已完成状态颜色的透明版本
    } else {
      console.log('取消绘制', val)
      resetDrawing()
    }
  }

  const resetCurrentPolygon = () => {
    currentPolygon.points = []
  }

  const resetDrawing = () => {
    isDrawing.value = false
    drawingType.value = 0
    currentPolygon.points = []
  }

  const clearPolygons = () => {
    // 只清空用户绘制的内容，保留初始数据
    const userPoints = completedPoints.value.filter((p) => p.id.toString().startsWith('user_'))
    userPoints.forEach((point) => {
      const index = completedPoints.value.findIndex((p) => p.id === point.id)
      if (index !== -1) {
        completedPoints.value.splice(index, 1)
      }
    })

    completedPolygons.value = []
    completedPolylines.value = []
    resetDrawing()
    console.log('用户绘制内容已清空，保留初始数据')
  }

  return {
    isDrawing,
    drawingType,
    completedPolygons,
    completedPolylines,
    completedPoints,
    currentPolygon,
    addPoint,
    removeLastPoint,
    startNewPolygon,
    finishDrawing,
    clearPolygons,
  }
}
