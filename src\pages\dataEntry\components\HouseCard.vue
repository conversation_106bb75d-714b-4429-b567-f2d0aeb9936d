<template>
  <view class="house-card" @click.stop.prevent="handleCardClick($event)">
    <view class="card-row">
      <view class="card-header">
        <view class="header-left">
          <view class="house-badge">
            <wd-icon custom-style="color: #2E80ED;" name="home" size="14px" @click.stop />
            <text class="house-id">{{ getDisplayTitle() }}</text>
          </view>
          <view v-if="getSubTitle()" class="type-label">
            <wd-icon custom-style="color: #64748b;" name="tag" size="12px" @click.stop />
            <text>{{ getSubTitle() }}</text>
          </view>
        </view>
        <view :class="getWorkspaceStatusClass()" class="status-badge">
          <wd-icon
            :custom-style="`color: ${getStatusIconColor()};`"
            :name="getStatusIcon()"
            size="10px"
            @click.stop
          />
          <text>{{ getWorkspaceStatusText() }}</text>
        </view>
      </view>
    </view>
    <view class="action-bar">
      <wd-circle
        v-if="showProgress && item.status == 0"
        :model-value="item.completionRate"
        :size="28"
        :strokeWidth="6"
        :text="`${item.completionRate}%`"
        color="var(--status-pending-color)"
        customStyle=" font-size: 6px; z-index:1"
      ></wd-circle>
      <wd-circle
        v-if="showProgress && item.status == 1"
        :model-value="item.completionRate"
        :size="28"
        :strokeWidth="6"
        :text="`${item.completionRate}%`"
        color="var(--status-active-color)"
        customStyle=" font-size: 6px; z-index:1"
      ></wd-circle>
      <wd-circle
        v-if="showProgress && item.status == 2"
        :model-value="item.completionRate"
        :size="28"
        :strokeWidth="6"
        :text="`${item.completionRate}%`"
        color="var(--status-completed-color)"
        customStyle=" font-size: 6px; z-index:1"
      ></wd-circle>
      <view class="compact-primary-btn-map" size="small" @click.stop="handleViewOnMap">
        <wd-icon name="location" size="14px" />
      </view>
      <view class="compact-primary-btn" @click.stop="handleStartFillReport">填报</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  item: any
  taskType?: string
  showProgress?: boolean
}

interface Emits {
  (e: 'start-fill-report', house: any): void
  (e: 'view-on-map', house: any): void
  (e: 'card-click', event: Event, house: any): void
}

const props = withDefaults(defineProps<Props>(), {
  taskType: 'ZF',
  showProgress: true,
})

const emit = defineEmits<Emits>()

// 🚀 统一的状态配置管理（支持数字状态）
const STATUS_CONFIG = {
  // 数字状态（实际业务使用）
  0: {
    text: '未填报',
    icon: 'circle',
    color: 'var(--status-pending-color)',
    cssClass: 'status-default',
  },
  1: {
    text: '填报中',
    icon: 'clock',
    color: 'var(--status-active-color)',
    cssClass: 'status-processing',
  },
  2: {
    text: '已填报',
    icon: 'check-circle',
    color: 'var(--status-completed-color)',
    cssClass: 'status-success',
  },
  // 字符串状态（保持兼容性）
  completed: {
    text: '已完成',
    icon: 'check-circle',
    color: 'var(--status-completed-color)',
    cssClass: 'status-success',
  },
  in_progress: {
    text: '进行中',
    icon: 'clock',
    color: 'var(--status-active-color)',
    cssClass: 'status-processing',
  },
  pending: {
    text: '待处理',
    icon: 'clock',
    color: 'var(--status-pending-color)',
    cssClass: 'status-pending',
  },
  error: {
    text: '错误',
    icon: 'x-circle',
    color: 'var(--status-pending-color)',
    cssClass: 'status-error',
  },
  default: {
    text: '未填报',
    icon: 'question-circle',
    color: 'var(--status-pending-color)',
    cssClass: 'status-default',
  },
} as const

// 🚀 优化后的状态函数
const getWorkspaceStatusText = () => {
  return STATUS_CONFIG[props.item.status]?.text || STATUS_CONFIG.default.text
}

const getWorkspaceStatusClass = () => {
  const config = STATUS_CONFIG[props.item.status] || STATUS_CONFIG.default
  return config.cssClass
}

const getStatusIcon = () => {
  return STATUS_CONFIG[props.item.status]?.icon || STATUS_CONFIG.default.icon
}

const getStatusIconColor = () => {
  return STATUS_CONFIG[props.item.status]?.color || STATUS_CONFIG.default.color
}

// 动态文本配置
const getDataTypeText = () => {
  return props.taskType === 'ZF' ? '' : '区域'
}

// 获取显示标题
const getDisplayTitle = () => {
  if (props.taskType === 'ZF') {
    // ZF任务显示：房屋 + ID（支持更长ID显示）
    const fwjzdm = props.item.fwjzdm ? props.item.fwjzdm.toString() : ''
    const displayId = fwjzdm
    return `${getDataTypeText()} ${displayId}`
  } else {
    // 非ZF任务显示：name字段，如果没有name则显示区域 + ID
    const name = props.item.name || props.item.regionName
    if (name) {
      // 名称太长时也添加省略号
      return name.length > 15 ? `${name.substring(0, 15)}...` : name
    } else {
      const id = props.item.id ? props.item.id.toString() : ''
      const displayId = id.length > 12 ? `${id.substring(0, 12)}...` : id
      return `${getDataTypeText()} ${displayId}`
    }
  }
}

// 获取副标题信息
const getSubTitle = () => {
  if (props.taskType === 'ZF') {
    // ZF任务显示类型
    return props.item.type ? props.item.type : ''
  } else {
    // 非ZF任务显示区域级别或其他属性
    return props.item.regionLevel ? `级别: ${props.item.regionLevel}` : ''
  }
}

// 事件处理
const handleCardClick = (event) => {
  // 阻止所有事件传播和默认行为
  if (event) {
    event.preventDefault?.()
    event.stopPropagation?.()
    event.stopImmediatePropagation?.()
  }

  console.log('📋 卡片点击事件触发:', props.item?.id, event?.target?.tagName, event?.type)
  emit('card-click', event, props.item)
  return false
}

const handleStartFillReport = () => {
  emit('start-fill-report', props.item)
}

const handleViewOnMap = () => {
  emit('view-on-map', props.item)
}
</script>

<style lang="scss" scoped>
// 紧凑型房屋卡片样式
.house-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin: 10px 10px 0 10px;
  overflow: hidden;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px #0000004d;

  .card-header {
    display: flex;
    align-items: center;
    width: 100%;

    .header-left {
      gap: 8px;

      .house-badge {
        display: flex;
        gap: 6px;
        align-items: center;
        color: #474747;

        .house-id {
          max-width: 160px;
          margin-right: 5px;
          overflow: hidden;
          font-size: 12px;
          font-weight: 700;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .type-label {
        display: flex;
        gap: 4px;
        align-items: center;
        padding: 2px 6px;
        font-size: 10px;
        font-weight: 500;
        color: #64748b;
        background: #f1f5f9;
        border: 1px solid #e2e8f0;
        border-radius: 4px;
      }
    }
  }

  .action-bar {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 100px;

    .compact-primary-btn-map {
      padding: 4px 12px;
      margin: 0 5px;
      background: #eeeeee;
      border: 1px solid #999999d4;
      border-radius: 8px;
    }

    // 🚀 自定义紧凑型按钮样式
    .compact-primary-btn {
      align-items: center;
      justify-content: center;
      padding: 8px 10px;
      font-size: 12px;
      font-weight: 600;
      color: white;
      text-align: center;
      background: #2563eb;
      border-radius: 8px;
    }
  }
}

// 紧凑型状态标签样式
.status-badge {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid transparent;
  border-radius: 8px;

  &.status-success {
    color: var(--status-completed-color);
    background: var(--status-completed-bg);
    border-color: var(--status-completed-color);
  }

  &.status-processing {
    color: var(--status-active-color);
    background: var(--status-active-bg);
    border-color: var(--status-active-color);
  }

  &.status-pending {
    color: var(--status-pending-color);
    background: var(--status-pending-bg);
    border-color: var(--status-pending-color);
  }

  &.status-error {
    color: var(--status-pending-color);
    background: var(--status-pending-bg);
    border-color: var(--status-pending-color);
  }

  &.status-default {
    color: var(--status-pending-color);
    background: var(--status-pending-bg);
    border-color: var(--status-pending-color);
  }
}
</style>
