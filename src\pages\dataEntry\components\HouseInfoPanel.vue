<template>
  <view class="house-info-panel">
    <view v-if="!house" class="empty-state">
      <view class="empty-content">
        <wd-icon custom-style="color: #94a3b8;" name="location" size="20px" />
        <text class="empty-text">点击"选择地点"查看房屋信息</text>
      </view>
    </view>

    <!-- 使用 HouseCard 组件替换原有的房屋信息显示 -->
    <HouseCard
      v-if="house"
      :item="house"
      :task-type="'ZF'"
      :show-progress="false"
      @start-fill-report="handleStartFillReport"
      @view-on-map="handleViewOnMap"
      @card-click="handleCardClick"
    />
  </view>
</template>

<script lang="ts" setup>
import { formatTechDateTime } from '../utils/helpers'
import HouseCard from './HouseCard.vue'

interface House {
  id: string | number
  status?: string | null
  height?: number
  createTime?: number
  updateTime?: number
  address?: string
  geom?: string
  type?: string
}

interface Props {
  house: House | null
}

interface Emits {
  (e: 'submit', house: House): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 获取状态显示文本
const getStatusText = (status?: string | null): string => {
  const statusMap: Record<string, string> = {
    completed: '✓ 完成',
    in_progress: '⏳ 进行中',
    pending: '⏸ 待处理',
    error: '❌ 错误',
  }

  return statusMap[status || ''] || '• 未填报'
}

// 获取状态样式类
const getStatusClass = (status?: string | null): string => {
  const classMap: Record<string, string> = {
    completed: 'status-completed',
    in_progress: 'status-processing',
    pending: 'status-pending',
    error: 'status-error',
  }

  return `status-badge ${classMap[status || ''] || 'status-default'}`
}

// 处理 HouseCard 组件的填报事件
const handleStartFillReport = (house: House) => {
  emit('submit', house)
}

// 处理 HouseCard 组件的地图查看事件
const handleViewOnMap = (house: House) => {
  console.log('🗺️ 在地图上查看房屋:', house.id)
  // 这里可以添加查看地图的逻辑
}

// 处理 HouseCard 组件的卡片点击事件
const handleCardClick = (event: Event, house: House) => {
  console.log('🏠 房屋信息面板 - 卡片点击:', house.id)
}

// 处理提交事件 - 保留原有逻辑
const handleSubmit = () => {
  if (props.house) {
    emit('submit', props.house)
  }
}
</script>

<style lang="scss" scoped>
.house-info-panel {
  width: 100%;
}

.empty-state {
  .empty-content {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    padding: 16px 20px;
    background: #f8fafc;
    border-radius: 8px;

    .empty-text {
      font-size: 13px;
      color: #6b7280;
    }
  }
}

.house-content {
  .compact-info {
    padding: 12px;
    margin-bottom: 12px;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;

    .house-id-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .id-badge {
        display: flex;
        gap: 4px;
        align-items: center;
        padding: 4px 8px;
        background: #eff6ff;
        border-radius: 6px;

        .id-text {
          width: 200px;
          overflow: hidden;
          font-size: 13px;
          font-weight: 600;
          color: #1e40af;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .info-row {
      display: flex;
      gap: 16px;
      margin-bottom: 6px;

      .info-item {
        display: flex;
        gap: 4px;
        align-items: center;

        .info-text {
          font-size: 12px;
          font-weight: 500;
          color: #374151;
        }
      }
    }

    .address-row {
      display: flex;
      gap: 4px;
      align-items: flex-start;

      .address-text {
        flex: 1;
        font-size: 12px;
        line-height: 1.3;
        color: #6b7280;
        word-break: break-all;
      }
    }
  }
}

.status-badge {
  padding: 3px 8px;
  border-radius: 12px;

  .status-text {
    font-size: 11px;
    font-weight: 500;
  }

  &.status-completed {
    background: #dcfce7;
    .status-text {
      color: #166534;
    }
  }

  &.status-processing {
    background: #dbeafe;
    .status-text {
      color: #1e40af;
    }
  }

  &.status-pending {
    background: #fef3c7;
    .status-text {
      color: #92400e;
    }
  }

  &.status-error {
    background: #fee2e2;
    .status-text {
      color: #991b1b;
    }
  }

  &.status-default {
    background: #f3f4f6;
    .status-text {
      color: #6b7280;
    }
  }
}

.action-section {
  .compact-btn {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 36px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px #3b82f633;
      transform: translateY(-1px);
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .house-content .compact-info {
    padding: 10px;

    .info-row {
      gap: 12px;
    }
  }
}
</style>
