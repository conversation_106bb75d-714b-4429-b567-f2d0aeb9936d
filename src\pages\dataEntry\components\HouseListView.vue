<template>
  <view class="house-list-view">
    <!-- 加载状态 -->
    <!--    <view v-if="isLoading" class="loading-overlay">-->
    <!--      <view class="loading-content">-->
    <!--        <wd-loading size="24px" />-->
    <!--        <text class="loading-text">加载列表数据...</text>-->
    <!--      </view>-->
    <!--    </view>-->

    <!-- 列表内容 -->
    <view class="list-content">
      <z-paging
        ref="paging"
        v-model="displayDataList"
        :auto-height="true"
        :auto-show-back-to-top="true"
        :default-page-size="20"
        :empty-view-text="getEmptyText()"
        :loading-more-enabled="true"
        :local-paging="true"
        :local-paging-loading-time="1200"
        :refresher-enabled="true"
        :show-default-go-to-top="true"
        @onRefresh="handleRefresh"
        @query="queryList"
      >
        <!-- 搜索栏 -->
        <template #top>
          <view class="search-container">
            <view class="search-bar-wrapper">
              <view class="search-input-wrapper">
                <wd-search
                  v-model="search.searchKeyword.value"
                  :hide-cancel="!search.searchKeyword.value"
                  :placeholder="getSearchPlaceholder()"
                  cancel-txt="搜索"
                  @blur="search.handleSearchBlur"
                  @cancel="handleSearchConfirm"
                  @change="handleSearchChange"
                  @clear="handleSearchClear"
                  @focus="search.handleSearchFocus"
                  @search="handleSearchConfirm"
                />
              </view>

              <!-- 地图/列表切换按钮 -->
              <view class="view-toggle-wrapper">
                <wd-button
                  custom-class="toggle-btn"
                  size="small"
                  type="primary"
                  @click="switchToMap"
                >
                  <image src="/static/map/icon/map-btn.png" style="width: 16px; height: 16px" />
                </wd-button>
              </view>
            </view>

            <!-- 搜索结果提示 -->
            <view v-if="search.showSearchResults.value" class="search-result-tip">
              <wd-icon
                custom-style="color: var(--status-active-color); margin-right: 4px;"
                name="search"
                size="14px"
              />
              <text class="result-text">
                {{ getSearchResultText() }}
              </text>
              <wd-button class="clear-search-btn" plain size="small" @click="handleSearchClear">
                清除
              </wd-button>
            </view>
          </view>
        </template>

        <!-- 房屋信息卡片 -->
        <HouseCard
          v-for="(item, index) in displayDataList"
          :key="item.id || index"
          :item="item"
          :task-type="taskType"
          @start-fill-report="handleStartFillReport"
          @view-on-map="handleViewOnMap"
          @card-click="handleCardClick"
        />
      </z-paging>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, onUnmounted, ref, watchEffect } from 'vue'
import { useSearch } from '../composables/useSearch'
import HouseCard from './HouseCard.vue'

interface Props {
  taskId: string | null
  taskType?: string
  houseData: any[]
  isLoading: boolean
}

interface Emits {
  (e: 'start-fill-report', house: any): void
  (e: 'view-on-map', house: any): void
  (e: 'switch-to-map'): void
  (e: 'data-loaded'): void
  (e: 'refresh-data'): void
}

const props = withDefaults(defineProps<Props>(), {
  taskType: 'ZF',
})
const emit = defineEmits<Emits>()

// 响应式数据
const paging = ref(null)
const displayDataList = ref([])
const originalDataList = ref([])
const isComponentMounted = ref(false)

// 使用搜索功能
const search = useSearch()

onMounted(() => {
  console.log('📋 列表组件已挂载')
  isComponentMounted.value = true
})

onUnmounted(() => {
  console.log('📋 列表组件已卸载')
  isComponentMounted.value = false
  search.cleanup()
})

// 使用 watchEffect 替代复杂的 watch，更简单直接
watchEffect(() => {
  if (!isComponentMounted.value) return

  console.log('📊 数据更新:', props.houseData?.length)
  originalDataList.value = props.houseData || []

  // 直接重新加载分页
  nextTick(() => {
    if (paging.value && typeof paging.value.reload === 'function') {
      console.log('🔄 重新加载z-paging数据')
      paging.value.reload()
    }
  })
})

// 列表查询 - z-paging的query事件处理
const queryList = (pageNo = 1, pageSize = 10) => {
  console.log('📋 z-paging查询:', { pageNo, pageSize })

  // 获取数据源
  const sourceData = search.searchKeyword.value ? getFilteredData() : originalDataList.value

  // 本地分页处理
  const startIndex = (pageNo - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pageData = sourceData.slice(startIndex, endIndex)

  console.log('📄 分页数据:', {
    total: sourceData.length,
    pageData: pageData.length,
    currentPage: pageNo,
    startIndex,
    endIndex,
  })

  if (paging.value && typeof paging.value.complete === 'function') {
    // 简化异步处理，移除 setTimeout
    const hasMore = endIndex < sourceData.length

    if (pageNo === 1) {
      paging.value.complete(pageData)
    } else {
      if (pageData.length > 0) {
        paging.value.addData(pageData, hasMore)
      } else {
        paging.value.complete([], false)
      }
    }
  }
}

// 过滤数据
const getFilteredData = () => {
  if (!search.searchKeyword.value.trim()) {
    return originalDataList.value
  }

  const keyword = search.searchKeyword.value.trim().toLowerCase()
  return originalDataList.value.filter((item) => {
    return (
      (item.fwjzdm && item.fwjzdm.toString().toLowerCase().includes(keyword)) ||
      (item.id && item.id.toString().toLowerCase().includes(keyword)) ||
      (item.address && item.address.toLowerCase().includes(keyword)) ||
      (item.type && item.type.toLowerCase().includes(keyword)) ||
      (item.name && item.name.toLowerCase().includes(keyword)) ||
      (item.regionName && item.regionName.toLowerCase().includes(keyword))
    )
  })
}

// 重新加载分页数据的简化方法
const reloadPaging = () => {
  if (!isComponentMounted.value) return

  nextTick(() => {
    if (paging.value && typeof paging.value.reload === 'function') {
      console.log('🔄 重新加载分页')
      paging.value.reload()
    }
  })
}

// 搜索事件处理 - 简化版本
const handleSearchChange = ({ value }) => {
  search.setSearchKeyword(value)

  if (value?.trim()) {
    const filteredData = getFilteredData()
    search.searchResultCount.value = filteredData.length

    if (filteredData.length === 0) {
      uni.showToast({
        title: `未找到匹配的${getDataTypeText()}`,
        icon: 'none',
        duration: 1500,
      })
    }
  } else {
    search.searchResultCount.value = null
  }

  reloadPaging()
}

const handleSearchConfirm = ({ value }) => {
  const keyword = value || search.searchKeyword.value
  if (keyword?.trim()) {
    search.setSearchKeyword(keyword)
    const filteredData = getFilteredData()
    search.searchResultCount.value = filteredData.length

    if (filteredData.length === 0) {
      uni.showToast({
        title: `未找到匹配的${getDataTypeText()}`,
        icon: 'none',
        duration: 1500,
      })
    } else {
      console.log('🔍 搜索完成:', {
        关键词: keyword,
        结果数量: filteredData.length,
      })
    }
  }

  reloadPaging()
}

const handleSearchClear = () => {
  search.searchKeyword.value = ''
  search.searchResultCount.value = null
  console.log('🗑️ 搜索已清除')
  reloadPaging()
}

// 刷新处理 - 简化版本
const handleRefresh = () => {
  console.log('🔄 触发数据刷新')
  emit('refresh-data')

  // if (paging.value && typeof paging.value.complete === 'function') {
  //   paging.value.complete()
  // }
}

// 操作事件
const handleStartFillReport = (item) => {
  emit('start-fill-report', item)
}

const handleViewOnMap = (item) => {
  emit('view-on-map', item)
}

const switchToMap = () => {
  emit('switch-to-map')
}

const handleCardClick = (event, item) => {
  // 阻止所有事件传播和默认行为
  if (event) {
    event.preventDefault?.()
    event.stopPropagation?.()
    event.stopImmediatePropagation?.()
  }

  console.log('📋 卡片点击事件触发:', item?.id, event?.target?.tagName, event?.type)
  return false
}

// 工作台风格格式化函数 - 保留备用
const formatWorkspaceDate = (timestamp) => {
  if (!timestamp) return '--'
  const date = new Date(timestamp)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const formatWorkspaceTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 动态文本配置
const getDataTypeText = () => {
  return props.taskType === 'ZF' ? '房屋' : '区域'
}

const getSearchPlaceholder = () => {
  if (props.taskType === 'ZF') {
    return `请输入${getDataTypeText()}ID进行搜索`
  } else {
    return `请输入${getDataTypeText()}名称或ID进行搜索`
  }
}

const getEmptyText = () => {
  return search.searchKeyword.value
    ? `未找到匹配的${getDataTypeText()}`
    : `暂无${getDataTypeText()}数据`
}

const getSearchResultText = () => {
  return search.searchResultCount.value > 0
    ? `找到 ${search.searchResultCount.value} 条结果`
    : `未找到匹配的${getDataTypeText()}`
}

// 新增数据字段文本格式化函数 - 保留备用
const getResidentialTypeText = (types: string[]) => {
  if (!types || types.length === 0) return '--'

  const typeMap = {
    '1': '一般住宅小区',
    '2': '商品房小区',
    '3': '历史文化街区',
    '4': '保障性住房小区',
    '5': '城中村',
    '6': '其他',
  }

  const typeTexts = types.map((type) => typeMap[type] || type)
  return typeTexts.join('、')
}

const getRenovationStatusText = (status: string) => {
  if (!status) return '--'

  const statusMap = {
    '0': '未改造',
    '1': '进行中',
    '2': '已完成',
    '3': '暂停',
  }

  return statusMap[status] || status
}

const getPropertyManagementText = (management: string) => {
  if (!management) return '--'

  const managementMap = {
    '0': '无',
    '1': '有',
  }

  return managementMap[management] || management
}

const formatDate = (timestamp) => {
  if (!timestamp) return '--'
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}
</script>

<style lang="scss" scoped>
.house-list-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8fafc;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #ffffffcc;
}

.loading-content {
  text-align: center;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #333333;
}

.list-content {
  flex: 1;
  overflow: hidden;
}

// 搜索容器样式
.search-container {
  position: relative;
  z-index: 10;
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px 0 #0000000d;

  .search-bar-wrapper {
    display: flex;
    gap: 12px;
    align-items: center;

    .search-input-wrapper {
      flex: 1;
    }

    .view-toggle-wrapper {
      flex-shrink: 0;

      .toggle-btn {
        display: flex;
        gap: 4px;
        align-items: center;
        height: 36px;
        padding: 0 12px;
        font-size: 13px;
        font-weight: 500;
        border-radius: 18px;
        transition: all 0.2s ease;

        &:hover {
          box-shadow: 0 4px 12px #00000026;
          transform: translateY(-1px);
        }
      }
    }
  }

  .search-result-tip {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    margin-top: 12px;
    background: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    box-shadow: 0 1px 3px #0000000d;

    .result-text {
      flex: 1;
      font-size: 13px;
      font-weight: 500;
      color: var(--status-active-color);
    }

    .clear-search-btn {
      height: 28px;
      padding: 0 12px;
      margin-left: 8px;
      font-size: 12px;
    }
  }
}

// 紧凑型房屋卡片样式 - 已移至 HouseCard.vue 组件

// 紧凑型状态标签样式 - 已移至 HouseCard.vue 组件

.task-progress {
  width: 100%;
  margin: 8px 0;
  border-radius: 5px;
  .progress-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  text {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
  }
}
</style>
