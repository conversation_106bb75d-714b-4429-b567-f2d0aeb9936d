import { ref, computed } from 'vue'

export function useSearch() {
  // ==================== 搜索状态 ====================
  const searchKeyword = ref('')
  const searchResultCount = ref<number | null>(null)
  const isSearching = ref(false)

  // ==================== 计算属性 ====================
  const hasSearchKeyword = computed(() => searchKeyword.value.trim().length > 0)
  const showSearchResults = computed(
    () => hasSearchKeyword.value && searchResultCount.value !== null,
  )

  // ==================== 搜索方法 ====================
  /**
   * 过滤数据 - 优化搜索算法
   */
  const filterData = (dataList: any[], keyword: string) => {
    if (!keyword.trim()) {
      return dataList
    }

    const searchTerm = keyword.trim().toLowerCase()

    return dataList.filter((item) => {
      // 多字段模糊搜索
      const searchFields = [item.id?.toString(), item.address, item.type].filter(Boolean)

      return searchFields.some((field) => field.toLowerCase().includes(searchTerm))
    })
  }

  /**
   * 设置搜索关键词
   */
  const setSearchKeyword = (keyword: string) => {
    searchKeyword.value = keyword
  }

  /**
   * 搜索事件处理器 - 简化版本
   */
  const handleSearchFocus = () => {
    console.log('🔍 搜索框聚焦')
  }

  const handleSearchBlur = () => {
    console.log('🔍 搜索框失焦')
  }

  /**
   * 获取搜索统计信息
   */
  const getSearchStats = () => {
    return {
      hasKeyword: hasSearchKeyword.value,
      keyword: searchKeyword.value,
      resultCount: searchResultCount.value,
      isSearching: isSearching.value,
    }
  }

  /**
   * 清理资源 - 简化版本
   */
  const cleanup = () => {
    searchKeyword.value = ''
    searchResultCount.value = null
    isSearching.value = false
  }

  return {
    // 状态
    searchKeyword,
    searchResultCount,
    isSearching,
    hasSearchKeyword,
    showSearchResults,

    // 方法
    filterData,
    setSearchKeyword,
    handleSearchFocus,
    handleSearchBlur,
    getSearchStats,
    cleanup,
  }
}
