import { ref, computed } from 'vue'
import { getHousesByTask, getHouseByLocation, getRegionsByTask } from '@/service/task/taskAPI'
import { gcj02ToWgs84 } from '@/utils/coordTransformNew'

interface House {
  id: string | number
  status?: string
  height?: number
  createTime?: number
  updateTime?: number
  address?: string
  geom?: string
  type?: string
}

export function useTaskData() {
  // ==================== 数据状态 ====================
  const originalDataList = ref<any[]>([])
  const isLoading = ref(false)
  const currentTaskId = ref<string | null>(null)
  const currentTaskType = ref<string | null>(null)

  // 缓存机制
  const dataCache = new Map<string, { data: any[]; timestamp: number }>()
  const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  // ==================== 计算属性 ====================
  const hasData = computed(() => originalDataList.value.length > 0)
  const dataCount = computed(() => originalDataList.value.length)

  // ==================== API请求优化 ====================
  /**
   * 获取任务房屋或区域数据 - 带缓存和类型判断
   */
  const fetchTaskData = async (taskId: string, taskType?: string): Promise<any[]> => {
    if (!taskId) {
      throw new Error('Task ID is required')
    }

    // 检查缓存
    const cacheKey = `${taskType === 'ZF' ? 'houses' : 'regions'}_${taskId}`
    const cached = dataCache.get(cacheKey)
    const now = Date.now()

    if (cached && now - cached.timestamp < CACHE_DURATION) {
      console.log('🎯 使用缓存数据:', { taskId, taskType, cacheAge: now - cached.timestamp })
      return cached.data
    }

    try {
      isLoading.value = true
      console.log('🌐 发起API请求:', { taskId, taskType })

      let response: any[]

      // 根据taskType选择不同的接口
      if (taskType === 'ZF') {
        response = await getHousesByTask(taskId)
        console.log('✅ 房屋数据获取成功:', { count: response?.length || 0 })
      } else {
        response = await getRegionsByTask(taskId)
        console.log('✅ 区域数据获取成功:', { count: response?.length || 0 })
      }

      const data = response || []

      // 更新缓存
      dataCache.set(cacheKey, {
        data,
        timestamp: now,
      })

      return data
    } catch (error) {
      console.error(`❌ 获取${taskType === 'ZF' ? '房屋' : '区域'}数据失败:`, error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取任务房屋数据 - 带缓存（保持向后兼容）
   */
  const fetchHousesByTask = async (taskId: string): Promise<any[]> => {
    return fetchTaskData(taskId, 'ZF')
  }

  /**
   * 根据位置查询房屋 - 优化参数验证
   */
  const fetchHouseByLocation = async (params: {
    taskId: number
    latitude: number
    longitude: number
    citystandardId?: number
  }) => {
    const { taskId, latitude, longitude, citystandardId = 1 } = params

    // 参数验证
    if (!taskId || isNaN(taskId) || taskId <= 0) {
      throw new Error('Invalid task ID')
    }

    if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
      throw new Error('Invalid coordinates')
    }

    try {
      // 坐标转换
      const wgs84Point = gcj02ToWgs84({ latitude, longitude })
      if (!wgs84Point) {
        throw new Error('Coordinate conversion failed')
      }

      const queryParams = {
        taskId,
        citystandardId,
        longitude: wgs84Point.longitude,
        latitude: wgs84Point.latitude,
      }

      console.log('🌐 位置查询请求:', queryParams)

      const response = await getHouseByLocation(queryParams)

      // 处理响应数据
      let houses: any[] = []
      if (response) {
        if (Array.isArray(response)) {
          houses = response.filter((item) => item?.id)
        } else if (typeof response === 'object' && (response as any).id) {
          houses = [response as any]
        } else if ((response as any).data) {
          const data = (response as any).data
          if (Array.isArray(data)) {
            houses = data.filter((item) => item?.id)
          } else if (typeof data === 'object' && data.id) {
            houses = [data]
          }
        }
      }

      console.log('✅ 位置查询成功:', { count: houses.length })
      return houses
    } catch (error) {
      console.error('❌ 位置查询失败:', error)
      throw error
    }
  }

  /**
   * 刷新任务数据
   */
  const refreshTaskData = async (taskId: string, taskType?: string) => {
    if (!taskId) return

    try {
      // 清除相关缓存
      const housesCacheKey = `houses_${taskId}`
      const regionsCacheKey = `regions_${taskId}`
      dataCache.delete(housesCacheKey)
      dataCache.delete(regionsCacheKey)

      // 重新获取数据
      const data = await fetchTaskData(taskId, taskType)
      originalDataList.value = data
      currentTaskId.value = taskId
      currentTaskType.value = taskType || null

      return data
    } catch (error) {
      console.error('❌ 刷新数据失败:', error)
      uni.showToast({
        title: '数据刷新失败',
        icon: 'error',
      })
      throw error
    }
  }

  /**
   * 初始化任务数据
   */
  const initTaskData = async (taskId: string, taskType?: string) => {
    if (currentTaskId.value === taskId && currentTaskType.value === taskType && hasData.value) {
      console.log('🎯 数据已存在，跳过初始化')
      return originalDataList.value
    }

    try {
      const data = await fetchTaskData(taskId, taskType)
      originalDataList.value = data
      currentTaskId.value = taskId
      currentTaskType.value = taskType || null
      return data
    } catch (error) {
      console.error('❌ 初始化数据失败:', error)
      uni.showToast({
        title: '数据加载失败',
        icon: 'error',
      })
      throw error
    }
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    dataCache.clear()
    console.log('🗑️ 缓存已清除')
  }

  /**
   * 获取缓存状态
   */
  const getCacheInfo = () => {
    const cacheEntries = Array.from(dataCache.entries()).map(([key, value]) => ({
      key,
      timestamp: value.timestamp,
      dataCount: value.data.length,
      age: Date.now() - value.timestamp,
    }))

    return {
      entries: cacheEntries,
      totalSize: dataCache.size,
    }
  }

  return {
    // 状态
    originalDataList,
    isLoading,
    hasData,
    dataCount,
    currentTaskId,
    currentTaskType,

    // 方法
    fetchTaskData,
    fetchHousesByTask,
    fetchHouseByLocation,
    refreshTaskData,
    initTaskData,
    clearCache,
    getCacheInfo,
  }
}
