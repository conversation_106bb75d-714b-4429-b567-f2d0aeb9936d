/**
 * 数据录入页面工具函数
 */

/**
 * 科技风格日期时间格式化
 * @param timestamp 时间戳
 * @returns 简化的日期时间字符串
 */
export const formatTechDateTime = (timestamp?: number): string => {
  if (!timestamp) return '--'

  try {
    const date = new Date(timestamp)
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${month}/${day} ${hours}:${minutes}`
  } catch (error) {
    console.error('❌ 科技日期格式化失败:', error)
    return '--'
  }
}

/**
 * 错误处理装饰器
 * @param func 要包装的异步函数
 * @param errorMessage 错误提示信息
 * @returns 包装后的函数
 */
export const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(
  func: T,
  errorMessage: string = '操作失败',
) => {
  return async (...args: Parameters<T>): Promise<ReturnType<T> | null> => {
    try {
      return await func(...args)
    } catch (error) {
      console.error(`❌ ${errorMessage}:`, error)

      let displayMessage = errorMessage
      if (error instanceof Error) {
        if (error.message.includes('网络') || error.message.includes('Network')) {
          displayMessage = '网络连接失败，请检查网络'
        } else if (error.message.includes('404')) {
          displayMessage = '请求的资源不存在'
        } else if (error.message.includes('401')) {
          displayMessage = '权限验证失败，请重新登录'
        } else if (error.message.includes('500')) {
          displayMessage = '服务器错误，请稍后重试'
        } else if (error.message.includes('timeout')) {
          displayMessage = '请求超时，请稍后重试'
        }
      }

      uni.showToast({
        title: displayMessage,
        icon: 'error',
        duration: 2000,
      })

      return null
    }
  }
}
