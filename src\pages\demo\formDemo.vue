<template>
  <!-- Dynamic Form Demo -->
  <!-- =============================================================================================================== -->
  <view class="wrap-form">
    <!-- 测试校验按钮 -->
    <view class="test-section">
      <wd-button type="primary" @click="handleTestValidate">测试表单校验</wd-button>
      <text class="test-tip">测试 tableForm 校验：空数组通过，有记录时校验必填字段</text>
    </view>

    <yt-dynamic-form
      ref="formRef"
      v-model="model"
      :config="fromList"
      submit-button-text="提交表单"
      @submit="handleFormSubmit"
      @validate="handleFormValidate"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import YtDynamicForm from '@/components/ytWidget/core/yt-dynamic-form.vue'
import type { FormItem } from '@/types/form'

const formRef = ref()
const model = ref({
  name: '',
  email: '',
  phone: '',
  password: '',
  F9nimbiz6vurakc: '', // 多行输入框
  F3nhmbj0jvy7aoc: '', // 选择器（单选）
  Fcwwmbj1a0wqayc: '', // 选择器2（单选）
  F29hmbj1a26zb1c: [], // 选择器2多（多选 - 必须是数组）
  Fbhvmbk5qh6jb8c: Date.now(), // 日期选择（13位时间戳）
  Fil0mbk6e8uvbbc: '12:00', // 开始时间（HH:mm 格式字符串）
  '6f0283a0-dc47-412e-b942-00a69b06cf7d': [], // 行政区划（级联选择器）
  mapSelector: null, // 地图选择器
  Fvg8mbkkbrynbqc: 1, // 计数器
  uploadFiles: [], // 上传文件
  imageUrl: '', // 单独显示的图片地址
  aaaa: {
    // 复选框和上传组件的数据
    checked: false,
    images: [],
  },
  testTableForm: [], // 测试表格表单，初始为空数组
})

const fromList = ref<FormItem[]>([
  {
    type: 'input',
    field: 'name',
    title: '姓名',
    info: '请输入姓名',
    $required: '请输入姓名',
    props: {
      disabled: false,
      readonly: false,
      maxlength: 20,
      clearable: true,
    },
    _fc_id: 'id_Ffpwmbhpdb72acc',
    name: 'ref_Fvr9mbhpdb72adc',
    display: true,
    hidden: false,
    _fc_drag_tag: 'input',
  },
  {
    type: 'input',
    field: 'email',
    title: '邮箱',
    info: '请输入邮箱地址',
    $required: '请输入正确的邮箱地址',
    props: {
      disabled: false,
      readonly: false,
      maxlength: 50,
      clearable: true,
      suffixIcon: 'scan1',
    },
    display: true,
    hidden: false,
  },
  {
    type: 'input',
    field: 'phone',
    title: '手机号',
    info: '请输入手机号码',
    $required: false,
    props: {
      disabled: false,
      readonly: false,
      maxlength: 11,
      minlength: 11,
      clearable: true,
    },
    display: true,
    hidden: false,
  },
  {
    type: 'DynamicCheckboxWithUpload',
    field: 'aaaa',
    title: '',
    info: '',
    required: false,
    _fc_id: 'id_F987mbklnv3rabc',
    name: 'ref_Fm3imbklnv3racc',
    display: true,
    hidden: false,
    _fc_drag_tag: 'DynamicCheckboxWithUpload',
    props: {
      checkboxLabel:
        '根据第一次全国自然灾害综合风险普查房屋建筑和市政设施调查数据库中的初步筛查数据，存在结构安全隐患的住房建筑',
    },
  },
  {
    type: 'inputNumber',
    field: 'Fvg8mbkkbrynbqc',
    title: '计数器',
    info: '',
    $required: false,
    props: {
      min: 1,
      max: 100,
      precision: 1,
      step: 5,
    },
    _fc_id: 'id_F33smbkkbrynbrc',
    name: 'ref_Fhrymbkkbrynbsc',
    display: true,
    hidden: false,
    _fc_drag_tag: 'inputNumber',
  },
  {
    type: 'input',
    field: 'password',
    title: '密码',
    info: '请输入密码',
    $required: '密码不能为空',
    props: {
      disabled: false,
      readonly: false,
      showPassword: true,
      maxlength: 20,
    },
    display: true,
    hidden: false,
  },
  {
    type: 'input',
    field: 'F9nimbiz6vurakc',
    title: '多行输入框',
    info: '',
    $required: false,
    props: {
      type: 'textarea',
      placeholder: '想要说点什么......',
    },
    _fc_id: 'id_Fdvqmbiz6vuralc',
    name: 'ref_Fdjdmbiz6vuramc',
    display: true,
    hidden: false,
    _fc_drag_tag: 'textarea',
  },
  {
    type: 'select',
    field: 'F3nhmbj0jvy7aoc',
    title: '选择器',
    info: '请选择一个选项',
    $required: '请选择一个选项',
    props: {
      _optionType: 2,
      clearable: true,
      disabled: false,
      multiple: false,
      placeholder: '请选择...',
    },
    _fc_id: 'id_Fs6cmbj0jvy7apc',
    name: 'ref_Fheimbj0jvy7aqc',
    display: true,
    hidden: false,
    _fc_drag_tag: 'select',
    options: [
      {
        label: '选项01',
        value: '1',
      },
      {
        label: '选项02',
        value: '2',
      },
      {
        label: '选项03',
        value: '3',
      },
    ],
  },
  {
    type: 'select',
    field: 'Fcwwmbj1a0wqayc',
    title: '选择器',
    info: '',
    effect: {
      fetch: '',
    },
    $required: false,
    options: [
      {
        label: '选项01',
        value: '1',
      },
      {
        label: '选项02',
        value: '2',
      },
      {
        label: '选项03',
        value: '3',
      },
    ],
    _fc_id: 'id_Fitombj1a0wqazc',
    name: 'ref_F0qmmbj1a0wqb0c',
    display: true,
    hidden: false,
    _fc_drag_tag: 'select',
  },
  {
    type: 'select',
    field: 'F29hmbj1a26zb1c',
    title: '选择器2多',
    info: '',
    effect: {
      fetch: '',
    },
    $required: false,
    props: {
      multiple: true,
    },
    options: [
      {
        label: '选项01',
        value: '1',
      },
      {
        label: '选项02',
        value: '2',
      },
      {
        label: '选项03',
        value: '3',
      },
      {
        label: '233',
        value: '4',
      },
    ],
    _fc_id: 'id_F0ilmbj1a26zb2c',
    name: 'ref_Fcvfmbj1a26zb3c',
    display: true,
    hidden: false,
    _fc_drag_tag: 'select',
  },
  {
    type: 'calendar',
    field: 'Fbhvmbk5qh6jb8c',
    title: '日期选择',
    info: '请选择日期',
    $required: '请选择日期',
    props: {
      calendarType: 'date',
      showConfirm: true,
      clearable: true,
    },
    _fc_id: 'id_Fdf2mbk5qh6jb9c',
    name: 'ref_Fb5nmbk5qh6jbac',
    display: true,
    hidden: false,
    _fc_drag_tag: 'calendar',
  },
  {
    type: 'timePicker',
    field: 'Fil0mbk6e8uvbbc',
    title: '开始时间',
    info: '请选择开始时间',
    $required: true,
    props: {
      pickerType: 'time',
      showConfirm: true,
      clearable: true,
    },
    _fc_id: 'id_Ftu3mbk6e8uvbcc',
    name: 'ref_F9o8mbk6e8uvbdc',
    display: true,
    hidden: false,
    _fc_drag_tag: 'timePicker',
  },
  {
    type: 'RegionCascader',
    field: '6f0283a0-dc47-412e-b942-00a69b06cf7d',
    title: '行政区划',
    info: '请选择行政区划',
    $required: '请选择行政区划',
    props: {
      placeholder: '请选择省市区',
      clearable: true,
    },
    _fc_id: 'id_Fpubmbk75jxcbkc',
    name: 'ref_Fm6mmbk75jxcblc',
    display: true,
    hidden: false,
    _fc_drag_tag: 'RegionCascader',
  },
  {
    type: 'MapSelector',
    field: 'mapSelector',
    title: '地图选择',
    info: '请选择地图位置',
    $required: '请选择地图位置',
    props: {
      placeholder: '请选择地图位置',
      clearable: true,
    },
    _fc_id: 'id_Fpubmbk75jxcbkc',
    name: 'ref_Fm6mmbk75jxcblc',
    display: true,
    hidden: false,
    _fc_drag_tag: 'MapSelector',
  },
  {
    type: 'upload',
    field: 'uploadFiles',
    title: '文件上传',
    info: '请选择文件',
    $required: false,
    props: {
      accept: 'image',
      multiple: false,
      placeholder: '选择图片',
    },
    _fc_id: 'id_UploadDemo123',
    name: 'ref_UploadDemo456',
    display: true,
    hidden: false,
    _fc_drag_tag: 'upload',
  },
  {
    type: 'tableForm',
    field: 'testTableForm',
    title: '测试表格表单',
    info: '这是一个测试表格表单，用于验证校验功能',
    $required: false, // tableForm 本身不是必填，允许为空
    props: {
      columns: [
        {
          label: '基本信息',
          rule: [
            {
              type: 'input',
              field: 'name',
              title: '姓名',
              $required: '姓名不能为空', // 必填字段
              props: {
                placeholder: '请输入姓名',
                clearable: true,
              },
            },
            {
              type: 'inputNumber',
              field: 'age',
              title: '年龄',
              $required: '年龄不能为空', // 必填数字字段
              props: {
                min: 1,
                max: 120,
                placeholder: '请输入年龄',
              },
            },
            {
              type: 'select',
              field: 'gender',
              title: '性别',
              $required: '请选择性别', // 必填选择字段
              options: [
                { label: '男', value: 'male' },
                { label: '女', value: 'female' },
              ],
              props: {
                placeholder: '请选择性别',
                clearable: true,
              },
            },
            {
              type: 'input',
              field: 'remark',
              title: '备注',
              $required: false, // 可选字段
              props: {
                placeholder: '请输入备注',
                clearable: true,
              },
            },
          ],
        },
      ],
    },
    display: true,
    hidden: false,
  },
])

// 表单提交处理
const handleFormSubmit = (data: Record<string, any>) => {
  console.log('表单提交成功，数据：', data)

  uni.showToast({
    title: '提交成功',
    icon: 'success',
    duration: 2000,
  })

  // 这里可以调用API提交数据
  // submitFormData(data)
}

// 上传成功处理

// 表单验证结果处理
const handleFormValidate = (result: { valid: boolean; errors: string[] }) => {
  console.log('表单验证结果:', result)

  if (!result.valid) {
    console.warn('表单验证失败:', result.errors)
  }
}

// 测试表单校验
const handleTestValidate = async () => {
  console.log('🧪 开始测试表单校验...')

  if (!formRef.value) {
    console.error('❌ 表单引用未找到')
    uni.showToast({ title: '表单引用未找到', icon: 'error' })
    return
  }

  try {
    const isValid = await formRef.value.validateForm()
    console.log(`🧪 表单校验结果: ${isValid ? '✅ 通过' : '❌ 失败'}`)

    uni.showToast({
      title: isValid ? '校验通过' : '校验失败',
      icon: isValid ? 'success' : 'error',
      duration: 2000,
    })
  } catch (error) {
    console.error('💥 表单校验异常:', error)
    uni.showToast({ title: '校验异常', icon: 'error' })
  }
}
</script>

<style lang="scss" scoped>
.wrap-form {
  padding: 20px;
}

.test-section {
  margin-bottom: 20px;
}

.test-tip {
  margin-left: 10px;
  color: #909399;
}
</style>
