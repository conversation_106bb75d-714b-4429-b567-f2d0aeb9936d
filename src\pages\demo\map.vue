<template>
  <view class="map-container">
    <map
      id="myMap"
      :latitude="latitude"
      :longitude="longitude"
      :markers="covers"
      :polygons="allPolygons"
      :polyline="allPolylines"
      :scale="scale"
      :show-location="false"
      :enable-satellite="true"
      class="map-container"
      @regionchange="handleRegionChange"
    >
      <!--中心点-->
      <CenterMarker
        v-if="isShowCenterMarker"
        v-model:marker="centerMarker"
        :is-moving="isMapMoving"
      />
      <DrawView
        v-if="isDrawing && drawingType > 0"
        v-model:marker="centerMarker"
        :is-moving="isMapMoving"
        @confirm="handleDrawConfirm"
        @cancel="handleDrawCancel"
        @delete="handleDrawDelete"
      />
      <view class="control-wrapper">
        <ControlPanel
          v-if="isShowEdit"
          v-model:points-count="pointsCount"
          :drawing="isDrawing"
          :has-polygons="completedPolygons.length > 0"
          @clear="clearPolygons"
          @finish="finishDrawing"
          @start="startNewPolygon"
        />
      </view>
    </map>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, watchEffect } from 'vue'
import CenterMarker from '@/components/lomoMap/CenterMarker.vue'
import ControlPanel from '@/components/lomoMap/ControlPanel.vue'
import usePolygonDraw from '@/hooks/usePolygonDraw'
import DrawView from '@/components/lomoMap/DrawView.vue' // 新增依赖

// 新增依赖
const {
  isDrawing,
  drawingType,
  completedPolygons,
  completedPolylines,
  completedPoints,
  currentPolygon,
  addPoint,
  removeLastPoint,
  startNewPolygon,
  finishDrawing,
  clearPolygons,
} = usePolygonDraw()

// 添加调试监控
watchEffect(() => {
  console.log('🔍 [DEBUG] completedPoints变化:', completedPoints.value)
  console.log('🔍 [DEBUG] completedPoints数量:', completedPoints.value.length)
  console.log('🔍 [DEBUG] completedPolygons变化:', completedPolygons.value)
  console.log('🔍 [DEBUG] completedPolygons数量:', completedPolygons.value.length)
  console.log('🔍 [DEBUG] completedPolylines变化:', completedPolylines.value)
  console.log('🔍 [DEBUG] completedPolylines数量:', completedPolylines.value.length)
  console.log('🔍 [DEBUG] covers计算结果:', covers.value)
  console.log('🔍 [DEBUG] allPolygons计算结果:', allPolygons.value)
  console.log('🔍 [DEBUG] allPolylines计算结果:', allPolylines.value)
})

/**
 * @description 绘制确认事件处理 - 根据绘制类型添加坐标
 * @param marker 当前标记点信息
 */
const handleDrawConfirm = (marker) => {
  console.log('确认添加点位:', marker, '当前绘制类型:', drawingType.value)

  // 调用hook的addPoint方法，根据绘制类型自动处理
  addPoint({
    latitude: marker.latitude,
    longitude: marker.longitude,
  })

  // 添加调试信息
  console.log('🔍 [DEBUG] 添加点后，completedPoints:', completedPoints.value)

  // 根据绘制类型给用户不同的反馈
  if (drawingType.value === 1) {
    uni.showToast({
      title: '点标记已添加',
      icon: 'success',
    })
  } else if (drawingType.value === 2) {
    uni.showToast({
      title: `线段点已添加 (${currentPolygon.points.length})`,
      icon: 'success',
    })
  } else if (drawingType.value === 3) {
    uni.showToast({
      title: `多边形点已添加 (${currentPolygon.points.length})`,
      icon: 'success',
    })
  }
}

/**
 * @description 绘制取消事件处理 - 删除上一个点
 */
const handleDrawCancel = () => {
  console.log('取消操作 - 删除上一个点，当前绘制类型:', drawingType.value)

  const success = removeLastPoint()

  if (success) {
    if (drawingType.value === 1) {
      uni.showToast({
        title: '已删除上一个点标记',
        icon: 'success',
      })
    } else if (drawingType.value === 2 || drawingType.value === 3) {
      uni.showToast({
        title: '已删除上一个顶点',
        icon: 'success',
      })
    }
  } else {
    uni.showToast({
      title: '没有可删除的点',
      icon: 'none',
    })
  }
}

/**
 * @description 绘制删除事件处理 - 清空当前绘制
 * @param marker 要删除的标记点信息
 */
const handleDrawDelete = (marker) => {
  console.log('清空当前绘制:', marker)
  uni.showModal({
    title: '确认清空',
    content: '确定要清空当前绘制内容吗？',
    success: (res) => {
      if (res.confirm) {
        console.log('用户确认清空绘制')
        clearPolygons()
        uni.showToast({
          title: '绘制内容已清空',
          icon: 'success',
        })
      } else {
        console.log('用户取消清空')
      }
    },
  })
}

const isShowCenterMarker = ref(false)
const isShowEdit = ref(true)
// 地图状态
const isMapMoving = ref(false)
// 43.823755,125.277062
const latitude = ref(43.823755)
const longitude = ref(125.277062)
const centerMarker = ref({ latitude: 43.823755, longitude: 125.277062 })

// 合并所有多边形，包含初始多边形和绘制的多边形
const allPolygons = computed(() => {
  const result = [...initPolygons, ...completedPolygons.value]

  // 如果正在绘制多边形，添加当前绘制进度
  if (isDrawing.value && drawingType.value === 3 && currentPolygon.points.length >= 3) {
    const currentDrawing = {
      points: [...currentPolygon.points],
      strokeWidth: currentPolygon.strokeWidth,
      strokeColor: currentPolygon.strokeColor,
      fillColor: currentPolygon.fillColor,
    }
    result.push(currentDrawing)
    console.log('🔍 [DEBUG] 添加当前多边形绘制进度到显示:', currentDrawing)
  }

  console.log('🔍 [DEBUG] allPolygons计算中，initPolygons:', initPolygons)
  console.log('🔍 [DEBUG] allPolygons计算中，completedPolygons:', completedPolygons.value)
  return result
})

// 合并所有线段，使用polyline属性
const allPolylines = computed(() => {
  const result = [...initPolylines, ...completedPolylines.value]

  // 如果正在绘制线段，且至少有2个点时才显示线段
  if (isDrawing.value && drawingType.value === 2 && currentPolygon.points.length >= 2) {
    const currentDrawing = {
      points: [...currentPolygon.points],
      width: currentPolygon.strokeWidth,
      color: currentPolygon.strokeColor,
    }
    result.push(currentDrawing)
    console.log('🔍 [DEBUG] 添加当前线段绘制进度到显示:', currentDrawing)
  }

  // 如果正在绘制线段且有至少1个点，添加预览线（从最后一个点到当前中心点）
  if (isDrawing.value && drawingType.value === 2 && currentPolygon.points.length >= 1) {
    const previewLine = {
      points: [
        currentPolygon.points[currentPolygon.points.length - 1], // 最后一个确认的点
        { latitude: centerMarker.value.latitude, longitude: centerMarker.value.longitude }, // 当前中心点
      ],
      width: 2,
      color: '#90CAF9', // 浅蓝色预览线
    }
    result.push(previewLine)
    console.log('🔍 [DEBUG] 添加预览线:', previewLine)
  }

  // 如果正在绘制多边形且有至少1个点，添加预览线
  if (isDrawing.value && drawingType.value === 3 && currentPolygon.points.length >= 1) {
    const previewLine = {
      points: [
        currentPolygon.points[currentPolygon.points.length - 1], // 最后一个确认的点
        { latitude: centerMarker.value.latitude, longitude: centerMarker.value.longitude }, // 当前中心点
      ],
      width: 2,
      color: '#A5D6A7', // 浅绿色预览线
    }
    result.push(previewLine)
    console.log('🔍 [DEBUG] 添加多边形预览线:', previewLine)
  }

  console.log('🔍 [DEBUG] allPolylines计算结果:', result)
  return result
})

// 合并covers数组，包含完成的点标记
const covers = computed(() => {
  console.log('🔍 [DEBUG] covers计算中，completedPoints.value:', completedPoints.value)

  // 合并初始点和绘制的点
  const result = [...initPoints, ...completedPoints.value]

  // 如果正在绘制线段或多边形，显示当前绘制的点位置
  if (
    isDrawing.value &&
    (drawingType.value === 2 || drawingType.value === 3) &&
    currentPolygon.points.length > 0
  ) {
    currentPolygon.points.forEach((point, index) => {
      result.push({
        id: `temp_${index}`,
        latitude: point.latitude,
        longitude: point.longitude,
        iconPath: '/static/map/icon/marker.png',
        width: 24,
        height: 24,
      })
    })
    console.log('🔍 [DEBUG] 添加当前绘制点标记:', currentPolygon.points.length)
  }

  console.log('🔍 [DEBUG] covers计算结果:', result)
  return result
})

const pointsCount = ref(0)
const scale = ref(16) // 地图缩放级别

// 初始化的多边形数据
const initPolygons = [
  {
    points: [
      { latitude: 43.826715, longitude: 125.277162 },
      { latitude: 43.827483, longitude: 125.291839 },
      { latitude: 43.821633, longitude: 125.291789 },
      { latitude: 43.821451, longitude: 125.280083 },
    ],
    fillColor: '#ff000070',
    strokeColor: '#ff00c3',
    strokeWidth: 2,
  },
]

// 初始化的线段数据
const initPolylines = [
  {
    points: [
      { latitude: 43.825, longitude: 125.275 },
      { latitude: 43.8255, longitude: 125.276 },
      { latitude: 43.826, longitude: 125.277 },
    ],
    width: 3,
    color: '#00BFFF',
  },
]

// 初始化的点标记数据
const initPoints = [
  {
    id: 'init_1',
    latitude: 43.824,
    longitude: 125.278,
    iconPath: '/static/map/icon/marker.png',
    width: 32,
    height: 32,
  },
  {
    id: 'init_2',
    latitude: 43.8245,
    longitude: 125.279,
    iconPath: '/static/map/icon/marker.png',
    width: 32,
    height: 32,
  },
]

/**
 * @description 地图移动事件
 * @param e
 */
const handleRegionChange = (e) => {
  isMapMoving.value = e.type === 'begin'
}
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  height: 100vh;
}

.control-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  left: 20px;
  display: flex;
  gap: 15px;
  justify-content: space-between;
  pointer-events: none;

  //> * {
  //  pointer-events: auto;
  //}
}
.map-container {
  width: 100%;
  height: 100vh;
}
</style>
