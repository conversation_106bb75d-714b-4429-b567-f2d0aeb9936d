<template>
  <view class="container">
    <!-- 地图容器 -->
    <map
      id="myMap"
      :latitude="center.latitude"
      :longitude="center.longitude"
      :markers="markers"
      :show-location="true"
      :enable-satellite="true"
      class="map"
      @regionchange="handleMapMove"
    />

    <!-- 坐标显示区域 -->
    <view class="coord-box">
      当前中心点坐标：
      <text>{{ currentCenter.latitude }}, {{ currentCenter.longitude }}</text>
    </view>
  </view>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'

// 地图基础配置
const mapKey = '你的腾讯地图密钥' // 前往腾讯位置服务官网申请

// 初始中心点（示例坐标：北京天安门）
const initCenter = {
  latitude: 39.908823,
  longitude: 116.39747,
}

// 响应式数据
const center = reactive({ ...initCenter })
const currentCenter = reactive({ ...initCenter })
const markers = ref([])

// 初始化地图标记
const initMarkers = () => {
  markers.value = [
    {
      id: 0,
      latitude: initCenter.latitude,
      longitude: initCenter.longitude,
      iconPath: '/static/marker.png', // 建议使用绝对路径
      width: 32,
      height: 32,
    },
  ]
}

// 地图移动处理
const handleMapMove = (e) => {
  if (e.type === 'end') {
    const mapContext = uni.createMapContext('myMap')
    mapContext.getCenterLocation({
      success: (res) => {
        console.log('🚀🚀🚀~~~当前 57 行,方法名：success，变量：res=====', res)
        currentCenter.latitude = res.latitude.toFixed(6)
        currentCenter.longitude = res.longitude.toFixed(6)
        // 如果需要实时更新中心点标记，取消下面注释
        // markers.value[0].latitude = res.latitude
        // markers.value[0].longitude = res.longitude
      },
    })
  }
}

// 生命周期钩子
onMounted(() => {
  initMarkers()
  // 初始化地图服务（根据平台需要）
  if (uni.getSystemInfoSync().platform === 'android') {
    uni.requireNativePlugin('tencent-map')
  }
})
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.map {
  flex: 1;
  width: 100%;
}

.coord-box {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background: #ffffffe6;
  border-top: 1px solid #eee;
}
</style>
