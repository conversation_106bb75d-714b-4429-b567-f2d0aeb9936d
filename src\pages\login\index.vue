<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '登录页面',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="login-container">
    <!-- 背景图片 -->
    <!--    <image class="background-image" mode="aspectFill" src="/static/loginbg.png" />-->
    <!-- 蓝色渐变遮罩 -->
    <view class="gradient-overlay"></view>

    <!-- 主要内容区域 -->
    <view class="content-wrapper">
      <!-- 标题区域 -->
      <view class="title-section">
        <image
          class="tilte-img"
          mode="aspectFill"
          src="https://oss.urban.udo.top/urban/login-bg.png"
        />
        <text class="title-year">延吉市</text>
        <text class="title-main">城市体检数据采集</text>
      </view>

      <!-- 登录表单区域 -->
      <view class="form-section">
        <!-- 手机号输入框 -->
        <view class="input-container">
          <view class="input-label">账号</view>
          <wd-input
            v-model="loginForm.username"
            :no-border="true"
            custom-class="login-input"
            placeholder="请输入账号"
            type="text"
          />
        </view>

        <!-- 密码输入框 -->
        <view class="input-container">
          <view class="input-label">密码</view>

          <wd-input
            v-model="loginForm.password"
            :no-border="true"
            custom-class="login-input"
            placeholder="请输入登录密码"
            show-password
          />
        </view>
        <!-- 隐私与用户条款 -->
        <view class="privacy-section">
          <view class="privacy-wrapper">
            <wd-checkbox
              v-model="agree"
              custom-class="privacy-checkbox"
              shape="square"
            ></wd-checkbox>
            <text class="privacy-text">已阅读并同意</text>
            <text class="privacy-link" @click="handlePrivacyPolicy">《用户协议》</text>
            <text class="privacy-text">和</text>
            <text class="privacy-link" @click="handlePrivacyPolicy">《隐私政策》</text>
          </view>
        </view>
        <view class="button-section">
          <!-- 登录按钮 -->
          <wd-button block custom-class="login-button" type="primary" @click="handleLogin">
            登录
          </wd-button>
        </view>
      </view>
    </view>

    <!-- Toast组件 -->
    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useToast } from 'wot-design-uni'
import { getTenantIdByName, login } from '@/service/login/LoginAPI'
import { useUserStore } from '@/store'
import * as authUtil from '@/utils/auth'

const toast = useToast()

const agree = ref(false) // 是否同意协议

const loginForm = reactive({
  tenantName: import.meta.env.VITE_APP_DEFAULT_LOGIN_TENANT || '',
  username: import.meta.env.VITE_APP_DEFAULT_LOGIN_USERNAME || '',
  password: import.meta.env.VITE_APP_DEFAULT_LOGIN_PASSWORD || '',
  captchaVerification: '',
  rememberMe: true,
})

// 获取租户 ID
const getTenantId = async () => {
  const tenantEnable = import.meta.env.VITE_APP_TENANT_ENABLE
  if (tenantEnable === 'true') {
    const res = (await getTenantIdByName(loginForm.tenantName)) as string
    authUtil.setTenantId(res)
  }
}
const { afterLogin } = useUserStore()

const handleLogin = async () => {
  console.log('手机号密码登录')
  if (!agree.value) {
    toast.warning('请阅读并同意《用户协议》和《隐私政策》')
    return
  }

  if (!loginForm.username) {
    toast.warning('请输入手机号')
    return
  }

  if (!loginForm.password) {
    toast.warning('请输入密码')
    return
  }

  try {
    // 1. 先根据租户名，获取 tenantId
    await getTenantId()

    // 2. 登录
    const res = await login(loginForm)
    if (!res) {
      return
    }
    console.log('🚀🚀🚀~~~当前 130 行,方法名：handleLogin，变量：res=====', res)
    // authUtil.setToken(res)
    await afterLogin(res)

    // 获取用户信息，保存到 store
    const userStore = useUserStore()
    await userStore.setUserInfoAction()

    // 跳转到首页
    // uni.switchTab({
    //   url: '/pages/work/index',
    // })

    toast.success('登录成功')
    console.log('登录成功')
  } catch (error) {
    console.error('登录失败:', error)
    // toast.error('登录失败，请检查手机号和密码')
  }
}
const handlePrivacyPolicy = () => {
  uni.navigateTo({ url: '/pages/my/privacy-policy' })
}

onLoad(() => {
  console.log('登录页面加载')
})
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  //height: 260px;
  height: 100vh;
}
.tilte-img {
  width: 100%;
  height: 160px;
  margin-bottom: 20px;
}
.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #2979ffe6 0%, #2979ffcc 50%, #2979ff99 100%);
}

.content-wrapper {
  position: absolute;
  //justify-content: center;
  //height: 100vh;
  bottom: 0;
  z-index: 3;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.title-section {
  width: 100%;
  padding: 0 40rpx;
  margin-top: 160rpx;
  margin-bottom: 180rpx;
  font-size: 44rpx;
  font-weight: bold;
  line-height: normal;
  color: #ffffff;
  letter-spacing: normal;
}

.title-year {
  display: block;
  margin-bottom: 12rpx;
  margin-left: 20px;
  line-height: 1.2;
  text-shadow: 0rpx 2rpx 4rpx #00000080;
}

.title-main {
  display: block;
  margin-left: 20px;
  line-height: 1.2;
  text-shadow: 0rpx 2rpx 4rpx #00000080;
}

.form-section {
  width: 100%;
  padding: 80rpx 0rpx;
  background: #fbfbfc;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  box-shadow: -2rpx -4rpx 30rpx 0px #0000004d;
  //height: 100%;
}

.input-container {
  width: 317px;
  margin: 0 auto;
  margin-bottom: 24rpx;
  .input-label {
    margin-bottom: 24rpx;
    font-size: 28rpx;
    font-weight: 500;
    line-height: normal;
    color: #535353;
    text-shadow: 0 2rpx 4rpx #0000004d;
    letter-spacing: normal;
  }
}

:deep(.login-input) {
  .wd-input__body {
    background: #eeeeee !important;
    border-radius: 12rpx !important;
  }
  .wd-input__inner {
    height: 80rpx !important;
    padding: 0 24rpx !important;
    font-size: 28rpx !important;
    line-height: 80rpx !important;
    color: #535353 !important;
    background: #eeeeee !important;
    border-radius: 12rpx !important;
  }

  .wd-input__inner::placeholder {
    font-size: 28rpx !important;
    color: #9f9f9f80 !important;
  }

  .wd-input__prefix {
    font-size: 28rpx !important;
    color: #666666 !important;
    background: #eeeeee !important;
  }
  .wd-icon {
    margin-right: 16rpx !important;
    background: #eeeeee !important;
  }
}
.button-section {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

:deep(.login-button) {
  width: 280px;
  height: 88rpx !important;
  margin: 0 auto;
  margin-top: 60rpx !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  background: #2e80ed !important;
  border: none !important;
  border-radius: 24rpx !important;
  box-shadow: 0 6rpx 20rpx #2979ff4d !important;
}

.privacy-section {
  display: flex;
  justify-content: center;
  margin-top: 44rpx;
}

.privacy-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 500;
  line-height: normal;
  letter-spacing: normal;
}

:deep(.privacy-checkbox) {
  margin-right: 16rpx !important;

  .wd-checkbox__shape.is-checked::after {
    border-color: #2e80ed !important;
  }
}

.privacy-text {
  font-size: 24rpx;
  line-height: 1.5;
  color: #535353;
}

.privacy-link {
  margin: 0 8rpx;
  font-size: 24rpx;
  line-height: 1.5;
  color: #535353;
  text-decoration: underline;
}
</style>
