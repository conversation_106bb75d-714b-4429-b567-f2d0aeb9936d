<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '修改密码',
  },
}
</route>

<template>
  <view class="p-4 flex flex-col gap-4 bg-white min-h-screen">
    <view class="text-xl font-bold mb-4">修改密码</view>
    <wd-input
      v-model="form.oldPassword"
      clearable
      label="原密码"
      placeholder="请输入原密码"
      required
      showPassword
      type="password"
    />
    <wd-input
      v-model="form.newPassword"
      clearable
      label="新密码"
      placeholder="请输入新密码"
      required
      showPassword
      type="password"
    />
    <wd-input
      v-model="form.confirmPassword"
      clearable
      label="确认新密码"
      placeholder="请再次输入新密码"
      required
      showPassword
      type="password"
    />
    <button class="w-90% bg-blue-500 text-white rounded-16px mt-6 font-bold" @click="handleSubmit">
      提交
    </button>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useMessage } from 'wot-design-uni'
import { changePassword } from '@/service/userAPI'

const message = useMessage()

const form = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const handleSubmit = async () => {
  if (!form.value.oldPassword || !form.value.newPassword || !form.value.confirmPassword) {
    return
  }
  if (form.value.newPassword !== form.value.confirmPassword) {
    return
  }
  try {
    await changePassword({
      oldPassword: form.value.oldPassword,
      newPassword: form.value.newPassword,
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  } catch (e: any) {}
}
</script>

<style lang="scss" scoped>
.min-h-screen {
  min-height: 100vh;
}
</style>
