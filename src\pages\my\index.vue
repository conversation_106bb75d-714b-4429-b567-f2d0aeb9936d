<route lang="json5" type="page">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '我的',
  },
}
</route>

<template>
  <view class="p-4 flex flex-col gap-4">
    <!-- 个人信息-登陆后 -->
    <view
      v-if="getAccessToken()"
      class="flex justify-between w-full items-center gap-4 h-150rpx bg-white rounded-xl py-15rpx px-10rpx box-border"
    >
      <view class="rounded-full w-120rpx h-120rpx">
        <!-- 有头像时显示头像 -->
        <image
          v-if="user?.avatar"
          :src="user.avatar"
          class="h-120rpx w-120rpx rounded-full"
          mode="scaleToFill"
        />
        <!-- 没有头像但有昵称时显示昵称首字母 -->
        <view
          v-else-if="user?.nickname"
          class="h-120rpx w-120rpx rounded-full bg-blue-500 flex items-center justify-center"
        >
          <text class="text-white text-3xl font-600">{{ user.nickname.substring(0, 1) }}</text>
        </view>
        <!-- 都没有时显示默认头像 -->
        <image
          v-else
          src="/static/images/user.png"
          class="h-120rpx w-120rpx rounded-full"
          mode="scaleToFill"
        />
      </view>
      <view class="grow-1 flex flex-col justify-between h-120rpx py-2 box-border">
        <view>
          <text class="text-2xl font-600">{{ user?.nickname }}</text>
        </view>
        <!--        <view class="text-xs flex items-center">-->
        <!--          <text>芋道集团</text>-->
        <!--          <view class="i-ic-twotone-shield text-emerald"></view>-->
        <!--        </view>-->
      </view>
      <view class="h-full">
        <!-- todo 设置按钮 -->
        <view class="i-ic-outline-settings mr-15rpx"></view>
      </view>
    </view>
    <!-- 个人信息-登录前 -->
    <view v-else class="flex justify-between w-full items-start gap-4">
      <view class="text-2xl font-600" @click="handleLogin">点击登录</view>
    </view>

    <!-- // todo 日程安排 -->
    <!-- 应用设置 -->
    <view class="flex flex-col gap-2">
      <view>
        <text class="text-lg font-600">应用设置</text>
      </view>
      <view class="rounded-xl bg-white overflow-hidden">
        <wd-cell v-if="getAccessToken()" is-link title="修改密码" @click="handleChangePassword">
          <template #icon>
            <view class="self-center flex item-center justify-center mr-2">
              <view class="i-ic-outline-lock"></view>
            </view>
          </template>
        </wd-cell>
        <wd-cell is-link title="隐私协议" @click="handlePrivacyPolicy">
          <template #icon>
            <view class="self-center flex item-center justify-center mr-2">
              <view class="i-ic-outline-policy"></view>
            </view>
          </template>
        </wd-cell>
        <!--        <wd-cell is-link title="关于我们">-->
        <!--          <template #icon>-->
        <!--            <view class="self-center flex item-center justify-center mr-2">-->
        <!--              <view class="i-ic-outline-info"></view>-->
        <!--            </view>-->
        <!--          </template>-->
        <!--        </wd-cell>-->
        <!--        <wd-cell is-link title="投诉与建议">-->
        <!--          <template #icon>-->
        <!--            <view class="self-center flex item-center justify-center mr-2">-->
        <!--              <view class="i-ic-outline-feedback"></view>-->
        <!--            </view>-->
        <!--          </template>-->
        <!--        </wd-cell>-->
        <!-- 退出登录，红色的警告字体 -->
        <wd-cell v-if="getAccessToken()" clickable @click="handleLogout">
          <template #icon>
            <view class="self-center flex item-center justify-center mr-2">
              <view class="i-ic-outline-logout text-red-500"></view>
            </view>
          </template>
          <template #title>
            <view>
              <text class="text-red-500">退出登录</text>
            </view>
          </template>
        </wd-cell>
      </view>
    </view>

    <!-- 技术信息 -->
    <view class="flex flex-col gap-2">
      <view>
        <text class="text-lg font-600">应用信息</text>
      </view>
      <view class="rounded-xl bg-white overflow-hidden">
        <view class="px-4 py-3">
          <!-- 应用版本 -->
          <view class="flex justify-between items-center">
            <view class="flex items-center gap-2">
              <view class="i-ic-outline-info text-blue-500"></view>
              <text class="text-sm text-gray-700">当前版本</text>
            </view>
            <text class="text-sm text-gray-500">{{ appVersion }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { useMessage } from 'wot-design-uni'
import { getAccessToken } from '@/utils/auth'

const message = useMessage()
//

const userStore = useUserStore()

const handleLogin = () => {
  uni.redirectTo({ url: '/pages/login/index' })
}
const handleLogout = async () => {
  message
    .confirm({
      msg: '确定退出登录？',
      title: '退出登录',
    })
    .then(async () => {
      await userStore.LogOut()
      uni.redirectTo({ url: '/pages/login/index' })
    })
}

const handleChangePassword = () => {
  uni.navigateTo({ url: '/pages/my/change-password' })
}

const handlePrivacyPolicy = () => {
  uni.navigateTo({ url: '/pages/my/privacy-policy' })
}

const user = ref<any>({})

// {{CHENGQI:
// Action: Modified; Timestamp: 2025-07-18 15:30:00 +08:00; Reason: 实现默认头像显示nickname第一个字，当用户没有头像时显示昵称首字母作为文字头像; Principle_Applied: 用户体验优化;
// }}

// {{CHENGQI:
// Action: Modified; Timestamp: 2025-07-07 10:26:46 +08:00; Reason: 简化为只显示应用版本号，根据用户反馈去掉复杂技术栈信息; Principle_Applied: KISS;
// }}
// 应用版本信息
const appVersion = ref('1.0.0')

onLoad((_option) => {
  const {
    miniProgram: { envVersion, version },
  } = uni.getAccountInfoSync()

  if (envVersion === 'release') {
    appVersion.value = version
  } else if (envVersion === 'trial') {
    appVersion.value = '体验版'
  } else {
    appVersion.value = '开发版'
  }
  console.info(' ~🚀小程序 当前环境版本：--->', envVersion)
  console.info(' ~🚀小程序 版本号：--->', version)
  user.value = userStore.userInfo.user
})
</script>

<style lang="scss" scoped>
//
</style>
