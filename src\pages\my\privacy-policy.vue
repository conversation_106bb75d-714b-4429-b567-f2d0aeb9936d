<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '隐私协议',
  },
}
</route>

<template>
  <view class="privacy-policy-container">
    <!-- 页面头部 -->
    <view class="header-section">
      <view class="title-text">延吉市城市体检系统隐私协议</view>
      <text class="update-time">最后更新时间：2025年7月7日</text>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" enable-back-to-top scroll-y>
      <view class="content-wrapper">
        <!-- 前言 -->
        <view class="content-card">
          <view class="section-title">前言</view>
          <text class="section-text">
            感谢您使用延吉市城市体检系统！我们深知个人信息对您的重要性，并会全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。
          </text>
        </view>

        <!-- 我们收集的信息 -->
        <view class="content-card">
          <view class="section-title">1. 我们收集的信息</view>
          <view class="subsection-group">
            <view class="info-table">
              <view class="table-header">
                <view class="col-type">信息类型</view>
                <view class="col-purpose">收集目的</view>
                <view class="col-auth">授权说明</view>
              </view>
              <view class="table-row">
                <view class="col-type">
                  <text class="info-title">设备信息</text>
                  <text class="info-detail">设备型号、操作系统版本、设备标识符</text>
                </view>
                <view class="col-purpose">
                  <text class="purpose-text">保障应用正常运行，提供技术支持</text>
                </view>
                <view class="col-auth">
                  <text class="auth-auto">自动收集</text>
                </view>
              </view>
              <view class="table-row">
                <view class="col-type">
                  <text class="info-title">位置信息</text>
                  <text class="info-detail">GPS坐标、地理位置数据</text>
                </view>
                <view class="col-purpose">
                  <text class="purpose-text">地图绘制、区域数据录入、现场定位</text>
                </view>
                <view class="col-auth">
                  <text class="auth-manual">需要授权</text>
                </view>
              </view>
              <view class="table-row">
                <view class="col-type">
                  <text class="info-title">微信OpenID</text>
                  <text class="info-detail">微信用户唯一标识</text>
                </view>
                <view class="col-purpose">
                  <text class="purpose-text">用户身份识别、登录验证</text>
                </view>
                <view class="col-auth">
                  <text class="auth-auto">自动获取</text>
                </view>
              </view>
              <view class="table-row">
                <view class="col-type">
                  <text class="info-title">工作数据</text>
                  <text class="info-detail">表单填报、检查记录、上传文件</text>
                </view>
                <view class="col-purpose">
                  <text class="purpose-text">城市健康检查业务功能实现</text>
                </view>
                <view class="col-auth">
                  <text class="auth-manual">用户主动提供</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 信息使用方式 -->
        <view class="content-card">
          <view class="section-title">2. 信息使用方式</view>
          <view class="subsection-group">
            <view class="usage-list">
              <view class="usage-item core-function">
                <view class="usage-icon">🏢</view>
                <view class="usage-content">
                  <text class="usage-title">核心业务功能</text>
                  <text class="usage-desc">提供城市健康检查、数据录入、地图绘制等核心功能</text>
                </view>
              </view>
              <view class="usage-item security">
                <view class="usage-icon">🔒</view>
                <view class="usage-content">
                  <text class="usage-title">安全保障</text>
                  <text class="usage-desc">账户安全验证、异常行为监测、数据备份恢复</text>
                </view>
              </view>
              <view class="usage-item optimization">
                <view class="usage-icon">⚡</view>
                <view class="usage-content">
                  <text class="usage-title">服务优化</text>
                  <text class="usage-desc">优化应用性能、改进用户体验（基于去标识化数据）</text>
                </view>
              </view>
              <view class="usage-item compliance">
                <view class="usage-icon">📋</view>
                <view class="usage-content">
                  <text class="usage-title">合规要求</text>
                  <text class="usage-desc">满足政府监管要求、数据统计分析、审计需要</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 数据安全措施 -->
        <view class="content-card">
          <view class="section-title">3. 数据安全措施</view>
          <view class="security-measures">
            <view class="measure-item verified">
              <view class="measure-status">✅</view>
              <view class="measure-content">
                <text class="measure-title">加密传输</text>
                <text class="measure-desc">采用TLS 1.2+协议，确保数据传输安全</text>
              </view>
            </view>
            <view class="measure-item verified">
              <view class="measure-status">✅</view>
              <view class="measure-content">
                <text class="measure-title">访问控制</text>
                <text class="measure-desc">严格限制员工数据访问权限，实施最小权限原则</text>
              </view>
            </view>
            <view class="measure-item verified">
              <view class="measure-status">✅</view>
              <view class="measure-content">
                <text class="measure-title">日志审计</text>
                <text class="measure-desc">定期审查数据访问记录，监控异常操作</text>
              </view>
            </view>
            <view class="measure-item verified">
              <view class="measure-status">✅</view>
              <view class="measure-content">
                <text class="measure-title">数据备份</text>
                <text class="measure-desc">建立完善的数据备份机制，防止数据丢失</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 您的权利 -->
        <view class="content-card">
          <view class="section-title">4. 您的权利</view>
          <view class="rights-grid">
            <view class="right-card access">
              <view class="right-icon">👁️</view>
              <view class="right-title">访问权</view>
              <text class="right-desc">查看我们收集的您的个人信息</text>
            </view>
            <view class="right-card correction">
              <view class="right-icon">✏️</view>
              <view class="right-title">更正权</view>
              <text class="right-desc">要求更正不准确的个人信息</text>
            </view>
            <view class="right-card deletion">
              <view class="right-icon">🗑️</view>
              <view class="right-title">删除权</view>
              <text class="right-desc">要求删除非必要的个人信息</text>
            </view>
            <view class="right-card portability">
              <view class="right-icon">📤</view>
              <view class="right-title">携带权</view>
              <text class="right-desc">获取您的个人信息副本</text>
            </view>
            <view class="right-card withdrawal">
              <view class="right-icon">🚫</view>
              <view class="right-title">撤回权</view>
              <text class="right-desc">撤回已给予的授权同意</text>
            </view>
            <view class="right-card complaint">
              <view class="right-icon">📞</view>
              <view class="right-title">投诉权</view>
              <text class="right-desc">向监管部门投诉举报</text>
            </view>
          </view>
        </view>

        <!-- 数据保留与删除 -->
        <view class="content-card">
          <view class="section-title">5. 数据保留与删除</view>
          <view class="retention-timeline">
            <view class="timeline-item">
              <view class="timeline-dot active"></view>
              <view class="timeline-content">
                <text class="timeline-title">使用期间</text>
                <text class="timeline-desc">在您使用我们服务期间，我们会保留您的个人信息</text>
              </view>
            </view>
            <view class="timeline-item">
              <view class="timeline-dot"></view>
              <view class="timeline-content">
                <text class="timeline-title">停用后30天</text>
                <text class="timeline-desc">账户停用后30天内，您可以重新激活账户</text>
              </view>
            </view>
            <view class="timeline-item">
              <view class="timeline-dot"></view>
              <view class="timeline-content">
                <text class="timeline-title">法定期限</text>
                <text class="timeline-desc">超出法律法规要求的保存期限后，我们将删除相关信息</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 政策变更 -->
        <view class="content-card">
          <view class="section-title">6. 政策变更</view>
          <text class="section-text">
            我们可能适时修订本政策的条款，该等修订构成本政策的一部分。如该等修订造成您在本政策下权利的实质减少，我们将在修订生效前通过在主页上显著位置提示或向您发送电子邮件或以其他方式通知您。
          </text>
        </view>

        <!-- 联系我们 -->
        <view class="content-card">
          <view class="section-title">7. 联系我们</view>
          <view class="contact-info">
            <view class="contact-main">
              <text class="contact-desc">
                如您对本政策有任何疑问、意见或建议，请通过以下方式联系我们：
              </text>
            </view>
            <view class="contact-details">
              <view class="contact-item">
                <view class="contact-icon">📧</view>
                <view class="contact-text">
                  <text class="contact-label">邮箱：</text>
                  <text class="contact-value"><EMAIL></text>
                </view>
              </view>
              <view class="contact-item">
                <view class="contact-icon">⏰</view>
                <view class="contact-text">
                  <text class="contact-label">工作时间：</text>
                  <text class="contact-value">周一至周五 8:30-17:30</text>
                </view>
              </view>
              <view class="contact-item">
                <view class="contact-icon">🏢</view>
                <view class="contact-text">
                  <text class="contact-label">单位：</text>
                  <text class="contact-value">xxxxxxxxxx</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部空白 -->
        <view class="bottom-spacer"></view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
// 延吉市城市体检系统隐私协议页面
// 无需特殊逻辑，纯展示页面
</script>

<style lang="scss" scoped>
.privacy-policy-container {
  min-height: 100vh;
  background-color: #f2f3f7;
}

.header-section {
  padding: 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
  box-shadow: 0 2rpx 8rpx #0000000d;
}

.title-text {
  margin-bottom: 8rpx;
  font-size: 36rpx;
  font-weight: 700;
  color: #333333;
  text-align: center;
}

.update-time {
  display: block;
  font-size: 24rpx;
  color: #999999;
  text-align: center;
}

.content-scroll {
  flex: 1;
  height: calc(100vh - 140rpx);
}

.content-wrapper {
  padding: 24rpx;
}

.content-card {
  padding: 32rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx #0000000a;
}

.section-title {
  padding-left: 16rpx;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  border-left: 4rpx solid #07c160;
}

.section-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666666;
}

// 信息收集表格
.info-table {
  margin-top: 16rpx;
}

.table-header {
  display: flex;
  padding: 16rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  background-color: #f7f7f7;
  border-radius: 8rpx 8rpx 0 0;
}

.table-row {
  display: flex;
  padding: 20rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
    border-radius: 0 0 8rpx 8rpx;
  }
}

.col-type {
  display: flex;
  flex: 2;
  flex-direction: column;
  gap: 8rpx;
}

.col-purpose {
  display: flex;
  flex: 2;
  align-items: center;
  padding: 0 16rpx;
}

.col-auth {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}

.info-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.info-detail {
  font-size: 24rpx;
  color: #666;
}

.purpose-text {
  font-size: 26rpx;
  line-height: 1.4;
  color: #666;
}

.auth-auto {
  padding: 6rpx 12rpx;
  font-size: 22rpx;
  color: #52c41a;
  background-color: #f6ffed;
  border-radius: 12rpx;
}

.auth-manual {
  padding: 6rpx 12rpx;
  font-size: 22rpx;
  color: #fa8c16;
  background-color: #fff7e6;
  border-radius: 12rpx;
}

// 使用方式列表
.usage-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.usage-item {
  display: flex;
  gap: 16rpx;
  align-items: flex-start;
  padding: 20rpx;
  border-radius: 12rpx;

  &.core-function {
    background-color: #f6ffed;
  }

  &.security {
    background-color: #f0f5ff;
  }

  &.optimization {
    background-color: #fff7e6;
  }

  &.compliance {
    background-color: #fafafa;
  }
}

.usage-icon {
  font-size: 32rpx;
  line-height: 1;
}

.usage-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 8rpx;
}

.usage-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.usage-desc {
  font-size: 24rpx;
  line-height: 1.4;
  color: #666;
}

// 安全措施
.security-measures {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.measure-item {
  display: flex;
  gap: 16rpx;
  align-items: flex-start;
  padding: 16rpx;
  background-color: #f6ffed;
  border-radius: 8rpx;
}

.measure-status {
  font-size: 24rpx;
  color: #52c41a;
}

.measure-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 6rpx;
}

.measure-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.measure-desc {
  font-size: 24rpx;
  color: #666;
}

// 权利网格
.rights-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.right-card {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  align-items: center;
  padding: 20rpx;
  text-align: center;
  background-color: #f0f5ff;
  border-radius: 12rpx;
}

.right-icon {
  font-size: 40rpx;
}

.right-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.right-desc {
  font-size: 22rpx;
  line-height: 1.3;
  color: #666;
}

// 保留时间线
.retention-timeline {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-left: 16rpx;
}

.timeline-item {
  display: flex;
  gap: 16rpx;
  align-items: flex-start;
}

.timeline-dot {
  width: 16rpx;
  height: 16rpx;
  margin-top: 8rpx;
  background-color: #d9d9d9;
  border-radius: 50%;

  &.active {
    background-color: #52c41a;
  }
}

.timeline-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 6rpx;
}

.timeline-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.timeline-desc {
  font-size: 24rpx;
  line-height: 1.4;
  color: #666;
}

// 联系信息
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-desc {
  font-size: 26rpx;
  line-height: 1.5;
  color: #666;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 24rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
}

.contact-item {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.contact-icon {
  font-size: 28rpx;
}

.contact-text {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.contact-label {
  font-size: 26rpx;
  color: #666;
}

.contact-value {
  font-size: 26rpx;
  font-weight: 500;
  color: #1890ff;
}

.bottom-spacer {
  height: 32rpx;
}
</style>
