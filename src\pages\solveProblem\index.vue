<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '填报详情',
  },
}
</route>

<template>
  <view class="warps">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-overlay">
      <view class="loading-content">
        <wd-loading size="24px" />
        <text class="loading-text">加载表单数据...</text>
      </view>
    </view>

    <!-- 切换过渡状态 -->
    <view v-if="isTransitioning" class="transitioning-overlay">
      <view class="transitioning-content">
        <wd-loading size="20px" />
        <text class="transitioning-text">切换中...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view v-if="!isLoading" :class="{ 'is-transitioning': isTransitioning }" class="main-content">
      <!-- 进度条 -->
      <view class="progress-section">
        <!--        <view class="progress-bar">-->
        <!--          <view :style="{ width: navigationInfo.progress + '%' }" class="progress-fill"></view>-->
        <!--        </view>-->
        <view class="progress-info">
          <text class="progress-title">{{ navigationTitle }}</text>
          <text class="progress-percentage">
            {{ navigationInfo.currentIndex }}/{{ navigationInfo.totalItems }}
          </text>
        </view>
      </view>
      <!-- 表单内容区域 -->
      <view class="content-wrapper">
        <view class="content">
          <yt-dynamic-form
            v-if="fromList.length > 0 && !isTransitioning"
            :key="`form-refresh-${forceRefresh}`"
            ref="formRef"
            v-model="model"
            :config="fromList"
            @submit="handleFormSubmit"
            @validate="handleFormValidate"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view v-if="!isLoading" class="tab-btn">
    <view class="btn-row">
      <!-- 上一项按钮 -->
      <wd-button
        :disabled="isTransitioning"
        class="nav-btn"
        custom-class="nav-btn-left"
        type="default"
        @click="handlePreviousQuestion"
      >
        <wd-icon name="arrow-left" size="16px" />
        上一项
      </wd-button>
      <!-- 如果没有上一项，显示空白区域 -->
      <!-- 下一项/完成按钮 -->
      <wd-button
        :class="hasNext ? 'nav-btn' : 'complete-btn'"
        :custom-class="hasNext ? 'nav-btn-right' : 'complete-btn-custom'"
        :disabled="isSaving || isTransitioning"
        :loading="isSaving"
        :type="hasNext ? 'primary' : 'primary'"
        @click="handleNextQuestion"
      >
        <template v-if="!isSaving">
          <template v-if="hasNext">
            下一项
            <wd-icon name="arrow-right" size="16px" />
          </template>
          <template v-else>
            <wd-icon name="check-circle" size="18px" />
            完成填报
          </template>
        </template>
        <template v-else>
          {{ hasNext ? '保存中...' : '提交中...' }}
        </template>
      </wd-button>
    </view>
  </view>
</template>
<script lang="ts" setup>
/**
 * 填报详情页面
 * @fileoverview
 * 用于显示和填写具体填报项目的表单页面，支持：
 * - 动态表单渲染（基于后端配置）
 * - 数据初始化和类型化默认值设置
 * - 表单数据变更检测和智能保存
 * - 项目间导航切换（上一项/下一项）
 * - 已填报数据的加载和编辑
 * - 完整的状态管理和错误处理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024
 *
 * @example
 * ```vue
 * <!-- 通过路由导航到此页面 -->
 * uni.navigateTo({
 *   url: '/pages/solveProblem/index?id=123&taskId=456&businessId=789'
 * })
 * ```
 */
import { computed, nextTick, ref } from 'vue'
import { type FormItem } from '@/components/ytWidget'
import YtDynamicForm from '@/components/ytWidget/core/yt-dynamic-form.vue'
import {
  getCityStandardItemDataByCondition,
  getCityStandardItemFormByItemId,
  saveCityStandardItemData,
  updateCityStandardItemData,
} from '@/service/task/taskAPI'
import { onLoad } from '@dcloudio/uni-app'
import { type SolveProblemItem, useSolveProblemStore } from '@/store/modules/solveProblem'

/** 动态表单组件引用 */
const formRef = ref<InstanceType<typeof YtDynamicForm> | null>(null)

/** 表单数据模型 */
const model = ref<Record<string, any>>({})

/** 导航标题 */
const navigationTitle = ref<string>('调查任务')

/** 表单配置列表 */
const fromList = ref<FormItem[]>([])

/** 强制刷新标志 */
const forceRefresh = ref<number>(0)

/** 原始表单数据 */
const originalFormData = ref<Record<string, any>>({})

// ==================== 页面参数 ====================

/** 当前填报项目ID */
const itemId = ref<string | null>(null)

/** 任务ID */
const taskId = ref<string | null>(null)

/** 业务ID */
const businessId = ref<string | null>(null)

/** 任务类型 */
const taskType = ref<string | null>(null)

/** 城市标准ID */
const citystandardId = ref<string | null>(null)

/** 表单配置ID */
const formId = ref<string | null>(null)

/** 表单版本 */
const formVersion = ref<string | null>(null)

/** 表单选项 */
const formOption = ref<any>(null)

/** 已填报数据ID */
const existingDataId = ref<number | null>(null)

// ==================== 状态管理 ====================

/** 页面加载状态 */
const isLoading = ref(false)

/** 保存状态 */
const isSaving = ref(false)

/** 切换状态 */
const isTransitioning = ref(false)

/** 填报状态管理store */
const solveProblemStore = useSolveProblemStore()

/**
 * 导航信息计算属性
 * @description 从store获取当前导航状态信息，包括进度、当前索引等
 * @returns {Object} 导航信息对象
 */
const navigationInfo = computed(() => solveProblemStore.getNavigationInfo())

/**
 * 是否有上一项计算属性
 * @description 判断当前是否有上一个填报项目可以切换
 * @returns {boolean} 有上一项返回true，否则返回false
 */
const hasPrevious = computed(() => navigationInfo.value.hasPrevious)

/**
 * 是否有下一项计算属性
 * @description 判断当前是否有下一个填报项目可以切换
 * @returns {boolean} 有下一项返回true，否则返回false
 */
const hasNext = computed(() => navigationInfo.value.hasNext)

/**
 * 当前项目信息计算属性
 * @description 从store获取当前正在填报的项目信息，仅用于UI显示
 * @returns {SolveProblemItem|null} 当前项目对象或null
 */
const currentItem = computed(() => solveProblemStore.getCurrentItem())

/**
 * 切换到指定的填报项目
 * @description 执行项目切换操作，包括状态重置、参数更新、数据加载等完整流程
 * @param {SolveProblemItem} item - 要切换到的项目对象，包含项目ID、名称、类型等信息
 * @returns {Promise<void>} 无返回值的异步函数
 * @example
 * ```typescript
 * const nextItem = { id: '123', itemName: '住房安全', taskType: 'ZF' }
 * await switchToItem(nextItem)
 * ```
 */
const switchToItem = async (item: SolveProblemItem) => {
  console.log('🔄 switchToItem被调用:', item)

  // 简化状态检查
  if (isTransitioning.value) {
    console.log('🔄 正在切换中，跳过本次切换')
    return
  }

  console.log('🔄 开始切换项目，设置transitioning状态')
  isTransitioning.value = true

  try {
    // 🔥 强制重置所有状态
    console.log('🔥 强制重置表单状态...')
    formRef.value = null
    model.value = {}
    fromList.value = []
    existingDataId.value = null
    formId.value = null
    formVersion.value = null // 🚀 重置表单版本
    formOption.value = null // 🚀 重置表单选项
    originalFormData.value = {} // 🚀 重置原始表单数据
    forceRefresh.value = Date.now() // 使用时间戳确保唯一性

    // 等待一个tick确保清空生效
    await nextTick()
    console.log('🔥 表单状态重置完成')

    // 更新页面参数
    console.log('🔄 更新页面参数...')
    itemId.value = item.id.toString()
    taskType.value = item.taskType || 'ZF'
    citystandardId.value = item.citystandardId?.toString() || ''
    navigationTitle.value = item.itemName

    // 更新store状态，确保UI显示正确
    solveProblemStore.setCurrentItem(item.id)

    console.log('🔄 页面参数已更新:', {
      itemId: itemId.value,
      taskType: taskType.value,
      citystandardId: citystandardId.value,
      navigationTitle: navigationTitle.value,
      forceRefresh: forceRefresh.value,
    })

    // 更新页面标题
    uni.setNavigationBarTitle({
      title: item.itemName,
    })

    console.log('🔄 开始加载新的表单数据...')
    // 加载新的表单数据（包括已填报数据）
    await getData()
    console.log('🔄 表单数据加载完成')

    // 等待DOM更新
    await nextTick()
    console.log('🔄 DOM更新完成')
  } finally {
    console.log('🔄 切换完成，重置状态')
    isTransitioning.value = false
  }
}

/**
 * 表单提交成功回调
 * @description 处理动态表单组件的提交成功事件
 * @param {any} values - 表单提交的数据值
 * @returns {void}
 */
const handleFormSubmit = (values: any) => {
  console.log('表单提交成功', values)
}

/**
 * 表单验证成功回调
 * @description 处理动态表单组件的验证成功事件
 * @param {any} values - 验证通过的表单数据
 * @returns {void}
 */
const handleFormValidate = (values: any) => {
  console.log('表单验证成功', values)
}

/**
 * 文件上传成功回调
 * @description 处理文件上传组件的上传成功事件
 * @param {any} res - 上传成功的响应数据
 * @returns {void}
 */
const handleUploadSuccess = (res: any) => {
  console.log('上传成功', res)
}

/**
 * 处理上一项按钮点击事件
 * @description 切换到上一个填报项目，包含状态检查、触觉反馈和项目切换逻辑
 * @returns {Promise<void>} 无返回值的异步函数
 * @throws {void} 如果没有上一项或正在切换中，会显示提示信息
 * @example
 * ```typescript
 * // 在模板中绑定点击事件
 * <button @click="handlePreviousQuestion">上一项</button>
 * ```
 */
const handlePreviousQuestion = async () => {
  console.log('⬅️ 点击上一项按钮')

  // 简化状态检查
  if (isTransitioning.value) {
    console.log('⬅️ 正在处理中，忽略点击')
    return
  }

  // 添加触觉反馈
  uni.vibrateShort()

  const previousItem = solveProblemStore.getPreviousItem()
  console.log('⬅️ 获取上一项结果:', previousItem)

  if (previousItem) {
    console.log(`⬅️ 切换到项目: ${previousItem.id} - ${previousItem.itemName}`)
    // 直接调用切换函数，不再依赖watch
    await switchToItem(previousItem)
  } else {
    console.log('⬅️ 没有上一项')
    uni.showToast({
      title: '已经是第一项',
      icon: 'none',
    })
  }
}

/**
 * 检测表单数据是否有变更
 * @description 通过深度比较当前表单数据与原始数据，判断用户是否修改了表单内容
 * @returns {boolean} 如果表单数据有变更返回true，否则返回false
 * @example
 * ```typescript
 * const hasChanged = hasFormDataChanged()
 * if (hasChanged) {
 *   console.log('用户修改了表单数据')
 * }
 * ```
 * @see originalFormData - 原始表单数据，用于对比
 * @see formRef - 表单组件引用，用于获取当前数据
 */
const hasFormDataChanged = (): boolean => {
  if (!formRef.value) {
    return false
  }

  const currentFormData = formRef.value.getFormData()

  /**
   * 深度比较两个对象是否相等
   * @description 递归比较对象的每个属性，支持嵌套对象和数组的比较
   * @param {any} obj1 - 第一个比较对象
   * @param {any} obj2 - 第二个比较对象
   * @returns {boolean} 如果两个对象完全相等返回true，否则返回false
   */
  const deepEqual = (obj1: any, obj2: any): boolean => {
    if (obj1 === obj2) return true

    if (obj1 == null || obj2 == null) return false

    if (typeof obj1 !== typeof obj2) return false

    if (typeof obj1 !== 'object') return obj1 === obj2

    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)

    if (keys1.length !== keys2.length) return false

    for (const key of keys1) {
      if (!keys2.includes(key)) return false
      if (!deepEqual(obj1[key], obj2[key])) return false
    }

    return true
  }

  const hasChanged = !deepEqual(currentFormData, originalFormData.value)
  console.log('🔍 表单变更检测:', {
    currentFormData,
    originalFormData: originalFormData.value,
    hasChanged,
  })

  return hasChanged
}

/**
 * 显示表单变更确认弹窗
 * @description 当检测到表单数据有变更时，显示确认弹窗询问用户是否保存修改
 * @returns {Promise<boolean>} 返回Promise，resolve用户的选择结果
 * @example
 * ```typescript
 * const shouldSave = await showChangeConfirmDialog()
 * if (shouldSave) {
 *   // 用户选择保存
 *   await saveFormData()
 * } else {
 *   // 用户选择跳过
 *   navigateToNext()
 * }
 * ```
 */
const showChangeConfirmDialog = (): Promise<boolean> => {
  return new Promise((resolve) => {
    uni.showModal({
      title: '表单有变更',
      content: '检测到表单数据有变更，是否保存修改？',
      confirmText: '保存',
      cancelText: '跳过',
      success: (res) => {
        resolve(res.confirm)
      },
    })
  })
}

/**
 * 处理下一项按钮点击事件
 * @description 保存当前表单数据并切换到下一个填报项目
 * - 在修改模式下会检测数据变更，用户可选择是否保存
 * - 在新增模式下会进行表单验证后保存数据
 * - 保存成功后自动切换到下一项或显示完成提示
 * @returns {Promise<void>} 无返回值的异步函数
 * @throws {void} 表单验证失败或保存失败时会显示错误提示
 * @example
 * ```typescript
 * // 在模板中绑定点击事件
 * <button @click="handleNextQuestion">下一项</button>
 * ```
 * @see hasFormDataChanged - 检测表单变更
 * @see showChangeConfirmDialog - 显示确认弹窗
 * @see switchToNextItem - 切换到下一项
 */
// 处理下一项 - 保存当前表单并切换到下一项
const handleNextQuestion = async () => {
  const skipValidation = false
  // 先检查表单是否存在
  if (!formRef.value) {
    console.error('❌ 表单引用不存在')
    return
  }

  // 🚀 主表单校验（含 tableForm）- 空表单保存时跳过
  if (!skipValidation && typeof formRef.value.validateForm === 'function') {
    const valid = await formRef.value.validateForm()
    if (!valid) {
      uni.vibrateShort()
      return
    }
  }

  console.info(' ~🚀文件:index，方法:handleNextQuestion 变量：model.value--->', model.value)

  // 🚀 修改模式下的特殊处理
  const isUpdateMode = existingDataId.value !== null

  if (isUpdateMode) {
    console.log('🔄 当前为修改模式，检测表单变更...')
    const hasChanged = hasFormDataChanged()

    if (!hasChanged) {
      console.log('📝 表单无变更，直接切换到下一项')
      await switchToNextItem()
      return
    }

    const shouldSave = await showChangeConfirmDialog()
    if (!shouldSave) {
      console.log('📝 用户选择跳过保存，直接切换')
      await switchToNextItem()
      return
    }
  }

  // 执行保存逻辑
  console.log('开始保存数据')
  uni.vibrateShort()
  isSaving.value = true

  try {
    // 获取表单数据并排除系统字段
    const formData = formRef.value.getFormData()
    const systemFields = [
      'id',
      'createTime',
      'updateTime',
      'taskId',
      'businessId',
      'formId',
      'itemId',
      'formVersion',
      'formOption',
      'citystandardId',
      'type',
      'status',
      'timestamp',
      'remark',
      'location',
      'coordinates',
      'category',
      'priority',
      'submitted',
      'valid',
      'version',
    ] // 进一步扩展系统字段排除列表
    const userFormData = Object.fromEntries(
      Object.entries(formData).filter(([key]) => !systemFields.includes(key)),
    )
    console.log('🔍 原始表单数据:', formData)
    console.log('🔍 排除系统字段后数据:', userFormData)
    console.log('🔍 系统字段:', systemFields)
    console.log('🔍 用户可见字段:', Object.keys(userFormData))

    // 验证必填字段
    if (!itemId.value || !taskId.value || !businessId.value || !formId.value) {
      uni.showToast({
        title: '缺少必要参数，无法保存',
        icon: 'error',
      })
      return
    }

    // 检查表单是否为空（移至必填字段验证之后）
    console.log('📋 开始空表单检查 --------------------')
    console.log('📋 当前表单数据:', formData)

    // 定义空值检查函数
    const checkValue = (value: any): boolean => {
      // 处理空白字符串
      if (typeof value === 'string' && value.trim() === '') return true

      const result =
        value === null ||
        value === undefined ||
        value === false ||
        value === 0 ||
        (Array.isArray(value) && value.every(checkValue)) ||
        (typeof value === 'object' &&
          !Array.isArray(value) &&
          (Object.keys(value).length === 0 || Object.values(value).every(checkValue)))
      // console.log('🔍 检查值:', value, '结果:', result);
      return result
    }

    // 检查每个用户字段的空值状态
    console.log('📋 字段级空值检测 (用户可见字段):')
    Object.entries(userFormData).forEach(([key, value]) => {
      const isEmpty = checkValue(value)
      console.log(`  🔑 ${key}: 值=${JSON.stringify(value)}, 为空=${isEmpty}`)
    })

    // 整体空表单检查 (仅用户可见字段)
    const isFormEmpty = Object.values(userFormData).every(checkValue)

    // 识别非空用户字段
    const nonEmptyFields = Object.entries(userFormData)
      .filter(([_, value]) => !checkValue(value))
      .map(([key]) => key)

    console.log('📊 空表单检查结果 (用户可见字段):', isFormEmpty)
    console.log('🔍 非空用户字段列表:', nonEmptyFields.length > 0 ? nonEmptyFields : '无')

    if (isFormEmpty) {
      console.log('📋 检测到空表单，显示提示对话框')
      const { confirm } = await uni.showModal({
        title: '表单未填写',
        content: '当前表单未填写任何内容，是否保存空数据？',
        confirmText: '保存',
        cancelText: '不保存',
      })
      console.log('📋 用户选择:', confirm ? '保存空数据' : '不保存')

      if (!confirm) {
        // 用户选择不保存，直接切换到下一项
        console.log('📋 用户选择不保存，切换到下一项')
        await switchToNextItem()
        return
      } else {
        console.log('📋 用户选择保存空数据，继续保存流程')
      }
    } else {
      console.log('📋 表单包含数据，跳过空表单提示')
    }

    // 表单验证（移至空表单检查之后）
    if (typeof formRef.value.validateForm === 'function') {
      const valid = await formRef.value.validateForm()
      if (!valid) {
        uni.vibrateShort()
        return
      }
    }

    // 根据是否存在已填报数据ID判断是新增还是更新
    const isUpdate = existingDataId.value !== null
    console.log('操作类型:', isUpdate ? '更新' : '新增', '已填报数据ID:', existingDataId.value)

    // 🚀 组装保存参数，包含表单配置信息
    const saveParams = {
      id: isUpdate ? existingDataId.value! : Number(itemId.value), // 更新时使用已填报数据ID，新增时使用itemId
      itemId: Number(itemId.value), // 指标ID
      taskId: Number(taskId.value), // 任务ID
      businessId: businessId.value!, // 业务ID
      formData, // 表单结果
      formRule: fromList.value,
      taskType: taskType.value || 'ZF', // 任务类型，默认为ZF
      formId: Number(formId.value), // 表单ID
      formVersion: formVersion.value, // 🚀 表单版本
      formOption: formOption.value, // 🚀 表单选项
      citystandardId: citystandardId.value ? Number(citystandardId.value) : undefined, // 城市指标体系ID
      remark: 'mp-weixin',
    }

    console.log('保存参数:', saveParams)

    // 调用对应的接口
    if (isUpdate) {
      console.log('调用更新接口')
      await updateCityStandardItemData(saveParams)
    } else {
      console.log('调用新增接口')
      await saveCityStandardItemData(saveParams)
    }

    // 🚀 保存成功后，更新原始数据为当前数据
    originalFormData.value = { ...formData }

    // 更新项目填报状态为已填报
    solveProblemStore.updateItemAnsweredStatus(itemId.value, true)

    // 根据是否为最后一项显示不同的成功提示
    const successMessage = isUpdate ? '更新成功' : '保存成功'
    const isLastItem = !hasNext.value

    if (isLastItem) {
      // 最后一项，添加特殊的完成反馈
      uni.vibrateLong()
      uni.showToast({
        title: `${successMessage}，已完成所有填报！`,
        icon: 'success',
        duration: 2000,
      })
    } else {
      uni.showToast({
        title: successMessage,
        icon: 'success',
      })
    }

    // 切换到下一项
    await switchToNextItem()
  } catch (error) {
    console.error('保存失败:', error)
    // 错误时也添加振动反馈
    uni.vibrateShort()
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'error',
    })
  } finally {
    isSaving.value = false
  }
}

/**
 * 切换到下一个填报项目
 * @description 获取下一个项目并执行切换操作，如果没有下一项则显示完成提示
 * @returns {Promise<void>} 无返回值的异步函数
 * @throws {void} 如果会话过期会显示错误提示并返回上一页
 * @example
 * ```typescript
 * // 在保存成功后调用
 * await switchToNextItem()
 * ```
 * @see solveProblemStore.getNextItem - 获取下一项数据
 * @see switchToItem - 执行项目切换
 */
// 切换到下一项
const switchToNextItem = async () => {
  console.log('🔄 开始切换到下一项...')

  // 简化状态检查
  if (isTransitioning.value) {
    console.log('🔄 正在处理中，忽略切换请求')
    return
  }

  // 检查store会话状态
  if (
    !solveProblemStore.currentSession ||
    !solveProblemStore.currentSession.itemsFlattened?.length
  ) {
    console.log('❌ Store会话未初始化，无法获取下一项')
    uni.showToast({
      title: '会话已过期，请重新进入',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.switchTab({
            url: '/pages/work/index',
          })
        },
      })
    }, 1500)
    return
  }

  const nextItem = solveProblemStore.getNextItem()
  console.log('🔄 获取到的下一项:', nextItem)

  if (nextItem) {
    console.log(`🔄 切换到项目: ${nextItem.id} - ${nextItem.itemName}`)
    // 直接调用切换函数，不再依赖watch
    await switchToItem(nextItem)
  } else {
    console.log('🔄 没有下一项，显示完成提示')
    // 没有下一项，是最后一项，显示完成提示
    uni.showModal({
      title: '填报完成',
      content: '所有项目已填报完成！',
      showCancel: false,
      success: () => {
        // 清除填报会话
        solveProblemStore.clearSession()

        // 返回上一页或指定页面
        uni.navigateBack({
          delta: 1,
          fail: () => {
            // 如果无法返回，则跳转到首页
            uni.switchTab({
              url: '/pages/work/index',
            })
          },
        })
      },
    })
  }
}

/**
 * 页面加载时的初始化函数
 * @description 处理页面加载时的参数解析、会话状态检查和数据初始化
 * - 解析页面参数（itemId、taskId、businessId等）
 * - 验证store会话状态的有效性
 * - 调用getData获取表单配置和数据
 * @param {any} option - 页面路由参数对象
 * @returns {void}
 * @example
 * ```typescript
 * // 路由参数示例
 * {
 *   id: '123',
 *   taskId: '456',
 *   businessId: '789',
 *   taskType: 'ZF',
 *   citystandardId: '101',
 *   title: '住房安全'
 * }
 * ```
 * @see solveProblemStore - 填报状态管理
 * @see getData - 获取表单数据
 */
onLoad((option: any) => {
  itemId.value = option.id
  taskId.value = option.taskId
  businessId.value = option.businessId
  taskType.value = option.taskType
  citystandardId.value = option.citystandardId
  navigationTitle.value = option.title || '调查任务'

  // 重置已填报数据ID
  existingDataId.value = null

  console.log('页面参数:', {
    itemId: itemId.value,
    taskId: taskId.value,
    businessId: businessId.value,
    taskType: taskType.value,
    citystandardId: citystandardId.value,
  })

  // 检查store会话是否已初始化
  console.log('🔍 检查store会话状态:', solveProblemStore.currentSession)
  if (
    !solveProblemStore.currentSession ||
    !solveProblemStore.currentSession.itemsFlattened?.length
  ) {
    console.log('❌ Store会话未初始化，返回上一页')
    uni.showToast({
      title: '请从任务列表进入',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.switchTab({
            url: '/pages/work/index',
          })
        },
      })
    }, 1500)
    return
  }

  // 直接加载当前页面数据，不再需要设置store状态
  getData()
})

/**
 * 获取表单配置数据
 * @description 根据当前项目ID获取表单配置，并加载已填报的数据进行合并
 * @returns {Promise<void>} 无返回值的异步函数
 * @throws {void} 获取数据失败时会显示错误提示
 * @example
 * ```typescript
 * // 在页面加载或切换项目时调用
 * await getData()
 * ```
 * @see getCityStandardItemFormByItemId - 获取表单配置API
 * @see setModel - 设置表单模型
 * @see loadExistingFormData - 加载已填报数据
 */
const getData = async () => {
  if (!itemId.value) {
    uni.showToast({
      title: '参数错误',
      icon: 'error',
    })
    return
  }

  console.log('📋 开始获取表单数据，itemId:', itemId.value)
  isLoading.value = true

  try {
    const res = await getCityStandardItemFormByItemId(Number(itemId.value))
    console.log('📋 获取表单配置成功:', res)

    if (res && res.formRule) {
      // 判断formRule是否已经是对象类型
      if (typeof res.formRule === 'string') {
        // 如果是字符串，需要解析
        fromList.value = JSON.parse(res.formRule)
      } else {
        // 如果已经是对象，直接使用
        fromList.value = res.formRule
      }

      // 🚀 获取并存储表单配置的完整信息
      formId.value = res.id
      formVersion.value = res.formVersion || null

      // 处理 formOption，如果是字符串则解析，否则直接使用
      if (res.formOption) {
        if (typeof res.formOption === 'string') {
          try {
            formOption.value = JSON.parse(res.formOption)
          } catch (error) {
            console.warn('📋 formOption 解析失败，使用原始值:', error)
            formOption.value = res.formOption
          }
        } else {
          formOption.value = res.formOption
        }
      } else {
        formOption.value = null
      }

      console.log('📋 表单配置已更新 fromList.value:', fromList.value)
      console.log('📋 表单配置数量:', fromList.value.length)
      console.log('📋 表单版本:', formVersion.value)
      console.log('📋 表单选项:', formOption.value)

      // 先设置初始表单模型
      setModel(fromList.value)
      console.log('📋 初始表单模型已设置 model.value:', model.value)

      // 尝试获取已填报的数据
      await loadExistingFormData()
    } else {
      uni.showToast({
        title: '数据格式错误',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('📋 获取数据失败:', error)
    uni.showToast({
      title: '数据加载失败',
      icon: 'error',
    })
  } finally {
    isLoading.value = false
    console.log('📋 getData完成，loading状态已重置')
  }
}

/**
 * 加载已填报的表单数据
 * @description 根据任务ID、业务ID和项目ID查询已填报的数据，并与初始化数据进行智能合并
 * - 特殊处理DynamicCheckboxWithUpload类型，确保images数组只存储URL字符串
 * - 更新项目填报状态和原始数据用于变更检测
 * @returns {Promise<void>} 无返回值的异步函数
 * @example
 * ```typescript
 * // 在setModel之后调用
 * await loadExistingFormData()
 * ```
 * @see getCityStandardItemDataByCondition - 获取已填报数据API
 * @see solveProblemStore.updateItemAnsweredStatus - 更新填报状态
 */
// 新增方法：加载已填报的表单数据
const loadExistingFormData = async () => {
  // 验证必要参数
  if (!taskId.value || !businessId.value || !itemId.value) {
    console.log('💾 缺少必要参数，跳过加载已填报数据')
    return
  }

  try {
    const params = {
      taskId: Number(taskId.value),
      businessId: businessId.value,
      itemId: Number(itemId.value),
    }

    console.log('💾 获取已填报数据参数:', params)

    const existingData = await getCityStandardItemDataByCondition(params)
    console.log('💾 获取已填报数据成功:', existingData)

    // 如果有已填报的数据，填充到表单中
    if (existingData && existingData.formData) {
      // 存储已填报数据的ID，用于判断是更新还是新增
      existingDataId.value = existingData.id || null

      console.log('💾 合并前的model.value:', model.value)
      console.log('💾 要合并的existingData.formData:', existingData.formData)

      // 🚀 智能合并：保持初始化的数据结构，但用已填报数据覆盖
      const mergedData = { ...model.value }

      Object.keys(existingData.formData).forEach((key) => {
        if (key in mergedData) {
          // 🚀 特殊处理 DynamicCheckboxWithUpload 类型
          if (
            typeof mergedData[key] === 'object' &&
            mergedData[key] &&
            'checked' in mergedData[key] &&
            'images' in mergedData[key]
          ) {
            // 对于复选框上传组件，确保数据结构完整，images只存储URL字符串
            const existingImages = existingData.formData[key]?.images || []
            mergedData[key] = {
              checked: existingData.formData[key]?.checked || false,
              images: Array.isArray(existingImages) ? existingImages : [],
            }
            console.log(`💾 🔄 ${key}: 特殊处理DynamicCheckboxWithUpload数据`)
          } else if (Array.isArray(mergedData[key])) {
            // 🚀 特殊处理 tableForm 类型 - 确保数组格式
            const existingArrayData = existingData.formData[key]
            if (Array.isArray(existingArrayData)) {
              mergedData[key] = existingArrayData
            } else if (existingArrayData && typeof existingArrayData === 'object') {
              // 如果后端返回的是对象，包装成数组
              mergedData[key] = [existingArrayData]
            } else {
              // 保持默认初始化的数组结构
              // mergedData[key] 已经是初始化时的数组，无需修改
            }
            console.log(`💾 🔄 ${key}: 特殊处理tableForm数组数据`)
          } else {
            // 对于其他类型，直接覆盖
            mergedData[key] = existingData.formData[key]
          }
        } else {
          // 如果初始化时没有这个字段，直接添加
          mergedData[key] = existingData.formData[key]
        }
      })

      model.value = mergedData

      // 🚀 同步更新原始表单数据
      originalFormData.value = { ...mergedData }

      console.log('💾 智能合并后的model.value:', model.value)
      console.log('💾 同步更新的originalFormData.value:', originalFormData.value)
      console.log('💾 存储的已填报数据ID:', existingDataId.value)

      // 标记该项目为已填报
      if (itemId.value) {
        solveProblemStore.updateItemAnsweredStatus(itemId.value, true)
        console.log('💾 已标记项目为已填报:', itemId.value)
      }
    } else {
      // 没有已填报数据，清除existingDataId和原始数据
      existingDataId.value = null
      originalFormData.value = { ...model.value } // 🚀 使用当前初始化的数据作为原始数据
      console.log('💾 没有已填报数据，使用初始化数据作为原始数据')
    }
  } catch (error) {
    console.log('💾 获取已填报数据失败或无已填报数据:', error)
    // 这里不显示错误提示，因为可能是正常的未填报状态
    existingDataId.value = null
    originalFormData.value = { ...model.value } // 🚀 使用当前初始化的数据作为原始数据
  }
}

/**
 * 设置表单数据模型
 * @description 根据表单配置项初始化表单数据，为不同类型的组件设置合适的默认值
 * - DynamicCheckboxWithUpload: {checked: false, images: []} (images存储URL字符串)
 * - upload: []
 * - select: 单选为''，多选为[]
 * - inputNumber: 使用配置的min值或0
 * - 其他类型: 根据具体类型设置相应默认值
 * @param {FormItem[]} keyList - 表单配置项数组
 * @returns {void}
 * @example
 * ```typescript
 * const formConfig = [
 *   { type: 'input', field: 'name', title: '姓名' },
 *   { type: 'DynamicCheckboxWithUpload', field: 'photos', title: '照片' }
 * ]
 * setModel(formConfig)
 * ```
 * @see FormItem - 表单项配置接口
 */
const setModel = (keyList: FormItem[]) => {
  console.log('🔧 setModel被调用，keyList:', keyList)
  const form: Record<string, any> = {}

  // 🚀 根据组件类型进行不同的初始化
  keyList.forEach((item) => {
    // 跳过没有field字段的项
    if (!item.field) return
    // 跳过elAlert等仅展示型组件
    if (item.type === 'elAlert') return

    let initialValue: any

    // 根据组件类型设置初始值
    switch (item.type) {
      case 'DynamicCheckboxWithUpload':
        // 复选框加上传组件初始化
        initialValue = {
          checked: false,
          images: [], // 存储图片URL字符串数组
        }
        console.log(`📝 ${item.field} (${item.type}): 初始化为复选框上传结构`)
        break
      case 'upload':
        // 上传组件初始化
        initialValue = []
        console.log(`📝 ${item.field} (${item.type}): 初始化为空数组`)
        break
      case 'select':
        // 下拉框初始化 - 根据是否多选决定初始值
        if (item.props?.multiple) {
          initialValue = [] // 多选初始化为数组
          console.log(`📝 ${item.field} (${item.type}-多选): 初始化为空数组`)
        } else {
          initialValue = '' // 单选初始化为空字符串
          console.log(`📝 ${item.field} (${item.type}-单选): 初始化为空字符串`)
        }
        break
      case 'checkbox':
        // 复选框组初始化 - 统一使用数组格式
        initialValue = []
        console.log(`📝 ${item.field} (${item.type}): 初始化为空数组（统一复选框组格式）`)
        break
      case 'inputNumber':
        // 数字输入框初始化 - 根据配置决定初始值
        if (item.props?.allowNull) {
          initialValue = '' // 允许空值时默认为空
        } else if (item.props?.min !== undefined) {
          initialValue = item.props.min // 有明确min值时使用min值
        } else {
          initialValue = '' // 其他情况默认为空，让组件自己处理
        }
        console.log(`📝 ${item.field} (${item.type}): 初始化为 ${initialValue}`)
        break
      case 'calendar':
      case 'timePicker':
        initialValue = ''
        break
      case 'datePicker':
        initialValue = ''
        break
      case 'datetime-picker':
        // 日期时间选择器初始化
        initialValue = ''
        console.log(`📝 ${item.field} (${item.type}): 初始化为空字符串`)
        break
      case 'RegionCascader':
        // 🚀 地区选择器初始化 - 使用层级对象格式
        initialValue = {}
        console.log(`📝 ${item.field} (${item.type}): 初始化为层级对象格式`)
        break
      case 'MapSelector':
        // 地图选择器初始化
        initialValue = {
          latitude: 0,
          longitude: 0,
          address: '',
        }
        console.log(`📝 ${item.field} (${item.type}): 初始化为地图结构`)
        break
      case 'tableForm':
        // 表格表单初始化 - 初始化为空数组
        initialValue = []
        console.log(`📝 ${item.field} (${item.type}): 初始化为表格表单空数组`)
        break
      case 'DictSelectWithOther':
        // 字典选择带其他选项初始化
        initialValue = {
          otherText: '',
          selectType: item.props?.selectType || 'checkbox',
          selected: [],
        }
        console.log(`📝 ${item.field} (${item.type}): 初始化为字典选择结构`)
        break
      case 'input':
      case 'textarea':
      default:
        // 输入框和默认类型初始化为空字符串
        initialValue = ''
        console.log(`📝 ${item.field} (${item.type}): 初始化为空字符串`)
        break
    }

    form[item.field] = initialValue
  })

  model.value = form
  console.log('🔧 setModel完成，包含类型化初始值的model.value:', model.value)

  // 🚀 如果不是修改模式，同步初始化原始表单数据
  if (existingDataId.value === null) {
    originalFormData.value = { ...form }
    console.log('🔧 同步初始化originalFormData.value:', originalFormData.value)
  }
}
</script>

<style lang="scss" scoped>
.warps {
  position: relative;
  //min-height: 100vh;
  padding: 0;
  background: #f8fafc;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #ffffffcc;
}

.loading-content {
  text-align: center;

  .loading-text {
    display: block;
    margin-top: 12px;
    font-size: 14px;
    color: #64748b;
  }
}

.main-content {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto; /* 占满整个视口高度 */
  padding-bottom: 80px;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
  &.is-transitioning {
    opacity: 0.6;
    transform: scale(0.98);
  }
}

// 切换过渡状态样式
.transitioning-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #ffffffb3;
  backdrop-filter: blur(2px);
}

.transitioning-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  padding: 16px 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px #0000001a;

  .transitioning-text {
    font-size: 12px;
    color: #64748b;
  }
}

// 内容包装器添加动画
.content-wrapper {
  width: 100%;
  height: calc(100vh - 190px);
  //flex-grow: 1; /* 该部分会填充剩余空间 */
  overflow-y: auto; /* 垂直方向可滚动 */
  animation: fadeInUp 0.4s ease-out;
}
.content {
  height: auto;
  padding: 10px 0px;
  margin: 0 16px;
  margin-bottom: 120rpx;
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 1px 3px #0000001f,
    0 1px 2px #0000003d;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 进度条样式
.progress-section {
  padding: 20px;
  margin: 16px;
  background: #ffffff;
  border: 1px solid #f1f5f9;
  border-radius: 16px;
  box-shadow:
    0 1px 3px #0000001f,
    0 1px 2px #0000003d;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 6px;
  margin-bottom: 8px;
  overflow: hidden;
  background: #e2e8f0;
  border-radius: 3px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 3px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

.progress-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.progress-percentage {
  padding: 4px 8px;
  font-size: 13px;
  font-weight: 600;
  color: #3b82f6;
  text-align: right;
  background: #3b82f61a;
  border: 1px solid #3b82f633;
  border-radius: 8px;
}

.tab-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 12px 0px 32px 0px;
  background: #ffffff;
  backdrop-filter: blur(10px);
  border-top: 1px solid #f1f5f9;
  box-shadow:
    0 -4px 6px -1px #0000001a,
    0 -2px 4px -1px #0000000f;

  // 按钮行
  .btn-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    //max-width: 320px;
  }

  // 空白区域，用于平衡布局
  .btn-spacer {
    width: 80px;
    height: 44px;
  }

  :deep(.wd-button) {
    flex-shrink: 0;
    font-weight: 600;
    border-radius: 12px;
    box-shadow:
      0 1px 3px #0000001f,
      0 1px 2px #0000003d;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.is-disabled {
      cursor: not-allowed;
      box-shadow: none;
      opacity: 0.4;
    }

    &.is-loading {
      opacity: 0.8;
    }

    // 按钮图标样式
    .wd-icon {
      margin: 0 4px;
    }

    // 导航按钮（普通大小）
    &.nav-btn {
      min-width: 100px;
      height: 44px;
    }

    // 完成按钮（保持完整圆弧）
    &.complete-btn {
      min-width: 100px;
      height: 44px;
      font-size: 15px;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border-radius: 12px; // 完成按钮保持完整圆弧
      box-shadow:
        0 4px 6px -1px #10b98166,
        0 2px 4px -1px #10b98133;

      &:not(.is-disabled):active {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: scale(0.98);
      }
    }
  }

  // 使用custom-class的按钮样式
  :deep(.nav-btn-left) {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
    min-width: 80px !important;
    height: 44px;
    color: #64748b !important;
    background: #f8fafc !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 0 12px 12px 0 !important; // 只保留右侧圆弧

    &:not(.is-disabled):active {
      background: #e2e8f0 !important;
      border-color: #cbd5e1 !important;
      transform: scale(0.98);
    }

    &:not(.is-disabled):hover {
      background: #f1f5f9 !important;
      border-color: #d1d5db !important;
    }
  }

  :deep(.nav-btn-right) {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
    min-width: 80px !important;
    height: 44px;
    color: #ffffff !important;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    border: none !important;
    border-radius: 12px 0 0 12px !important; // 只保留左侧圆弧

    &:not(.is-disabled):active {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
      transform: scale(0.98);
    }

    &:not(.is-disabled):hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
      box-shadow:
        0 4px 6px -1px #3b82f666,
        0 2px 4px -1px #3b82f633;
    }
  }

  :deep(.complete-btn-custom) {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
    min-width: 80px !important;
    height: 44px;
    color: #ffffff !important;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    border: none !important;
    border-radius: 12px 0 0 12px !important; // 只保留左侧圆弧

    &:not(.is-disabled):active {
      background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
      transform: scale(0.98);
    }
  }
}
</style>
