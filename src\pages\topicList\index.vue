<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '填报列表',
  },
}
</route>

<template>
  <view class="topic-list-page">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-overlay">
      <view class="loading-content">
        <wd-loading size="24px" />
        <text class="loading-text">加载填报项目...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view v-if="!isLoading" class="main-content">
      <!-- 刷新状态提示 -->
      <view v-if="isRefreshing" class="refresh-tip">
        <view class="refresh-content">
          <wd-loading size="16px" />
          <text class="refresh-text">正在刷新数据...</text>
        </view>
      </view>

      <!-- 直接显示所有分类内容 -->
      <view v-if="tabs.length > 0" class="categories-container">
        <view
          v-for="(item, index) in tabs"
          :key="item.dimensionLevel2 || index"
          class="category-section"
        >
          <!-- 分类标题 -->
          <view class="section-header">
            <view class="header-left">
              <wd-icon
                :name="getTabIcon(item.dimensionLevel2)"
                custom-style="color: #3b82f6;"
                size="18px"
              />
              <text class="section-title">{{ item.dimensionLevel2 }}</text>
            </view>
            <view class="header-right">
              <view class="stats-info">
                <text class="completion-rate">{{ getTabStats(item).percentage }}%</text>
                <text class="item-count">
                  {{ getTabStats(item).completed }}/{{ getTabStats(item).total }} 已填报
                </text>
              </view>
            </view>
          </view>

          <!-- 填报项目列表 -->
          <view class="item-list">
            <view
              v-for="(cell, cellIndex) in item.items"
              :key="cell.id || cellIndex"
              :class="{ 'is-answered': cell.isAnswered }"
              class="item-card"
              @click="handleItemClick(cell)"
            >
              <!-- 卡片头部 -->
              <view class="card-header">
                <view class="header-content">
                  <view class="item-icon">
                    <wd-icon custom-style="color: #3b82f6;" name="checklist" size="14px" />
                  </view>
                  <view class="item-info">
                    <text class="item-title">{{ cell.itemAlias }}</text>
                    <text class="item-id">{{ cell.itemCode }}</text>
                  </view>
                </view>
                <view class="card-right">
                  <view :class="getStatusClass(cell)" class="status-badge">
                    <wd-icon
                      :custom-style="`color: ${getStatusIconColor(cell)}`"
                      :name="getStatusIcon(cell)"
                      size="10px"
                    />
                    <text>{{ getStatusText(cell) }}</text>
                  </view>
                  <view class="arrow-icon">
                    <wd-icon custom-style="color: #94a3b8;" name="arrow-right" size="14px" />
                  </view>
                </view>
              </view>

              <!-- 卡片内容（如果有额外信息） -->
              <view v-if="cell.description || cell.remarks" class="card-content">
                <text v-if="cell.description" class="item-description">
                  {{ cell.description }}
                </text>
                <text v-if="cell.remarks" class="item-remarks">{{ cell.remarks }}</text>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-if="!item.items || item.items.length === 0" class="empty-state">
            <view class="empty-content">
              <wd-icon custom-style="color: #cbd5e1;" name="document" size="32px" />
              <text class="empty-text">该分类暂无填报项目</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 无数据状态 -->
      <view v-if="!isLoading && tabs.length === 0" class="no-data-state">
        <view class="no-data-content">
          <wd-icon custom-style="color: #cbd5e1;" name="document" size="48px" />
          <text class="no-data-text">暂无填报项目</text>
        </view>
      </view>
      <wd-gap bg-color="#FFFFFF" height="88rpx"></wd-gap>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed, nextTick, onUnmounted, ref, watchEffect } from 'vue'
import { onHide, onLoad, onPullDownRefresh, onShow } from '@dcloudio/uni-app'
import { getCityStandardItemByTask } from '@/service/task/taskAPI'
import { useSolveProblemStore } from '@/store/modules/solveProblem' // 数据类型定义

// 数据类型定义
interface TopicItem {
  id: string | number
  itemAlias: string
  isAnswered?: boolean
  status?: string
  description?: string
  remarks?: string
  taskType?: string
  citystandardId?: string
  cityStandardId?: string
  [key: string]: any
}

interface TabData {
  dimensionLevel2: string
  items: TopicItem[]
  [key: string]: any
}

// 响应式数据
const tabs = ref<TabData[]>([])
const taskId = ref<string | null>(null)
const businessId = ref<string | null>(null)
const objectTitle = ref<string>('') // 调查对象标题
const taskType = ref<string>('ZF') // 任务类型
const isLoading = ref(false)
const isRefreshing = ref(false) // 下拉刷新状态

// 添加store
const solveProblemStore = useSolveProblemStore()

// 🚀 性能优化：缓存机制和状态快照
const statsCache = new Map<string, ReturnType<typeof calculateStats>>()
const lastUpdateTime = ref<number>(0)
const itemStatusSnapshot = ref<Map<string | number, boolean>>(new Map())
let syncTimer: number | null = null

// 🚀 优化的统计计算函数
const calculateStats = (items: TopicItem[]) => {
  const total = items.length
  const completed = items.filter((item) => item.isAnswered === true).length
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0
  return { total, completed, percentage }
}

// 🚀 带缓存的获取标签页统计
const getTabStats = (tab: TabData) => {
  if (!tab.items || !Array.isArray(tab.items)) {
    return { total: 0, completed: 0, percentage: 0 }
  }

  // 生成缓存键：标签名_项目数量_最后更新时间
  const cacheKey = `${tab.dimensionLevel2}_${tab.items.length}_${lastUpdateTime.value}`

  // 检查缓存
  if (statsCache.has(cacheKey)) {
    return statsCache.get(cacheKey)!
  }

  // 计算新的统计信息
  const stats = calculateStats(tab.items)

  // 存入缓存
  statsCache.set(cacheKey, stats)

  // 清理旧缓存（保留最新的10个）
  if (statsCache.size > 10) {
    const oldestKey = statsCache.keys().next().value
    statsCache.delete(oldestKey)
  }

  return stats
}

// 🚀 创建状态快照
const createStatusSnapshot = (items: any[]): Map<string | number, boolean> => {
  const snapshot = new Map<string | number, boolean>()
  items?.forEach((item) => {
    snapshot.set(item.id, item.isAnswered || false)
  })
  return snapshot
}

// 🚀 高效的状态差异检测
const detectStatusChanges = (newSnapshot: Map<string | number, boolean>) => {
  const changes: Array<{ id: string | number; isAnswered: boolean }> = []
  const oldSnapshot = itemStatusSnapshot.value

  // 检测变化 - O(n) 复杂度
  newSnapshot.forEach((newStatus, id) => {
    const oldStatus = oldSnapshot.get(id)
    if (oldStatus !== newStatus) {
      changes.push({ id, isAnswered: newStatus })
    }
  })

  return changes
}

// 🚀 防抖同步函数
const debouncedSync = (changes: Array<{ id: string | number; isAnswered: boolean }>) => {
  if (syncTimer) {
    clearTimeout(syncTimer)
  }

  syncTimer = setTimeout(() => {
    if (changes.length > 0) {
      console.log(`🔄 检测到 ${changes.length} 个状态变化，执行同步`)
      batchUpdateItemsStatus(changes)
    }
    syncTimer = null
  }, 50) // 50ms 防抖
}

// 🚀 优化的监听方案 - 使用 watchEffect + computed
const storeItemsSnapshot = computed(() => {
  const items = solveProblemStore.currentSession?.itemsFlattened
  return items ? createStatusSnapshot(items) : new Map()
})

// 🚀 高效的状态监听 - 避免深度监听
watchEffect(() => {
  const newSnapshot = storeItemsSnapshot.value

  // 首次加载时初始化快照
  if (itemStatusSnapshot.value.size === 0 && newSnapshot.size > 0) {
    itemStatusSnapshot.value = new Map(newSnapshot)
    console.log('📸 初始化状态快照，包含 ' + newSnapshot.size + ' 个项目')
    return
  }

  // 检测状态变化
  if (newSnapshot.size > 0) {
    const changes = detectStatusChanges(newSnapshot)

    if (changes.length > 0) {
      // 更新快照
      itemStatusSnapshot.value = new Map(newSnapshot)
      // 防抖同步
      debouncedSync(changes)
    }
  }
})

// 🚀 手动状态同步方法（供外部调用）
const forceStatusSync = () => {
  const currentItems = solveProblemStore.currentSession?.itemsFlattened
  if (currentItems) {
    console.log('🔄 开始强制状态同步，当前 store 中的项目状态:')
    currentItems.forEach((item) => {
      console.log(`  项目 ${item.id}: ${item.itemAlias} - isAnswered: ${item.isAnswered}`)
    })

    const newSnapshot = createStatusSnapshot(currentItems)
    const changes = detectStatusChanges(newSnapshot)

    if (changes.length > 0) {
      itemStatusSnapshot.value = newSnapshot
      batchUpdateItemsStatus(changes)
      console.log(`🔄 强制同步 ${changes.length} 个状态变化:`, changes)
    } else {
      // 即使没有变化，也强制更新一次以确保UI同步
      console.log('🔄 没有检测到状态变化，但强制更新UI')
      itemStatusSnapshot.value = newSnapshot
      // 强制清空缓存并更新时间戳
      statsCache.clear()
      lastUpdateTime.value = Date.now()
    }
  } else {
    console.log('⚠️ 无法获取 store 中的项目数据')
  }
}

// 🚀 清理资源方法
const cleanupWatchers = () => {
  if (syncTimer) {
    clearTimeout(syncTimer)
    syncTimer = null
  }
  itemStatusSnapshot.value.clear()
}

// 在组件卸载时清理
onUnmounted(() => {
  cleanupWatchers()
})

// 页面加载
onLoad((option) => {
  taskId.value = option.taskId
  businessId.value = option.id
  objectTitle.value = option.objectTitle ? decodeURIComponent(option.objectTitle) : ''
  taskType.value = option.taskType || 'ZF'

  // 动态设置页面标题
  if (objectTitle.value) {
    uni.setNavigationBarTitle({
      title: `${objectTitle.value}`,
    })
  }

  getData()
})

// 页面显示时刷新状态
onShow(() => {
  console.log('📱 topicList 页面显示')

  // 🚀 如果页面有数据，强制同步状态（处理从填报页面返回的场景）
  if (tabs.value.length > 0) {
    console.log('🔄 检测到页面有数据，开始强制同步状态')

    // 清空所有缓存，确保重新计算
    statsCache.clear()
    lastUpdateTime.value = Date.now()

    // 强制同步状态
    forceStatusSync()

    // 额外的强制刷新：直接触发响应式更新
    setTimeout(() => {
      // 通过修改 lastUpdateTime 触发所有 getTabStats 重新计算
      lastUpdateTime.value = Date.now()
      console.log('🔄 强制触发统计数据重新计算')
    }, 100)
  } else {
    console.log('📱 页面无数据，可能需要重新加载')
    // 如果没有数据，可能需要重新加载
    if (taskId.value && businessId.value) {
      console.log('🔄 重新加载页面数据')
      getData()
    }
  }
})

onHide(() => {
  console.log('📱 topicList 页面隐藏')
})

// 下拉刷新处理
onPullDownRefresh(async () => {
  console.log('🔄 用户触发下拉刷新')
  await handleRefresh()
})

// 🚀 优化的数据获取函数
const getData = async () => {
  if (!businessId.value || !taskId.value) {
    uni.showToast({ title: '参数错误', icon: 'error' })
    return
  }

  // 如果是刷新状态，不显示loading遮罩
  if (!isRefreshing.value) {
    isLoading.value = true
  }

  try {
    const params = {
      businessId: businessId.value, // 🔧 移除Number()转换，保持原始值
      taskId: Number(taskId.value),
    }

    const response = await getCityStandardItemByTask(params)
    console.log('📊 获取数据成功，共', response?.length, '个分类')

    if (Array.isArray(response) && response.length > 0) {
      // 🚀 重要：先设置tabs数据，确保页面有数据可显示
      tabs.value = response

      // 🚀 延迟初始化store，确保tabs数据已设置
      await nextTick()

      // 初始化填报状态store
      solveProblemStore.initSession(taskId.value!, businessId.value!, response)

      // 清空缓存，重新开始计算
      statsCache.clear()
      lastUpdateTime.value = Date.now()

      console.log(`📊 数据加载完成，共 ${response.length} 个分类`)
    } else {
      tabs.value = []
      uni.showToast({ title: '暂无数据', icon: 'none' })
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    // 如果不是刷新状态，显示错误提示
    if (!isRefreshing.value) {
      uni.showToast({ title: '数据加载失败', icon: 'error' })
    }
    throw error // 重新抛出错误，让刷新函数能够捕获
  } finally {
    // 只有非刷新状态才控制loading
    if (!isRefreshing.value) {
      isLoading.value = false
    }
  }
}

// 🚀 下拉刷新处理函数
const handleRefresh = async () => {
  try {
    isRefreshing.value = true
    console.log('🔄 开始刷新数据...')

    // 清理缓存，确保获取最新数据
    statsCache.clear()

    // 重新获取数据
    await getData()

    // 强制同步状态
    forceStatusSync()

    console.log('✅ 数据刷新完成')
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1500,
    })
  } catch (error) {
    console.error('❌ 刷新失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    isRefreshing.value = false
    // 停止下拉刷新动画
    uni.stopPullDownRefresh()
  }
}

// 获取标签页图标
const getTabIcon = (tabName: string) => {
  const iconMap: Record<string, string> = {
    安全耐久: 'shield-check',
    功能完备: 'settings',
    建筑结构: 'home',
    设施设备: 'gear',
    环境质量: 'leaf',
  }
  return iconMap[tabName] || 'document'
}

// 获取标签页项目数量
const getTabItemCount = (tab: TabData) => {
  return tab.items?.length || 0
}

// 获取填报状态 - 根据isAnswered字段
const getAnsweredStatus = (item: TopicItem) => {
  if (item.isAnswered === true) {
    return 'completed'
  } else {
    return 'pending'
  }
}

// 获取状态文本 - 基于isAnswered
const getStatusText = (item: TopicItem) => {
  if (item.isAnswered === true) {
    return '已填报'
  } else {
    return '未填报'
  }
}

// 获取状态图标 - 基于isAnswered
const getStatusIcon = (item: TopicItem) => {
  if (item.isAnswered === true) {
    return 'check-circle'
  } else {
    return 'circle'
  }
}

// 获取状态图标颜色 - 基于isAnswered
const getStatusIconColor = (item: TopicItem) => {
  if (item.isAnswered === true) {
    return 'var(--status-completed-color)' // 绿色 - 已填报
  } else {
    return 'var(--status-active-color)' // 橙色 - 未填报
  }
}

// 获取状态样式类 - 基于isAnswered
const getStatusClass = (item: TopicItem) => {
  if (item.isAnswered === true) {
    return 'status-badge status-completed'
  } else {
    return 'status-badge status-pending'
  }
}

// 🚀 处理项目点击 - 增加状态回调
const handleItemClick = (item: TopicItem) => {
  // 添加点击反馈
  uni.vibrateShort?.()

  // 设置当前项目到store中
  solveProblemStore.setCurrentItem(item.id)

  // 从点击的item中获取需要的数据
  const itemTaskType = item.taskType || 'ZF'
  const itemCitystandardId = item.citystandardId || item.cityStandardId || ''

  console.log('点击项目数据:', {
    item,
    taskType: itemTaskType,
    citystandardId: itemCitystandardId,
  })

  const url = `/pages/solveProblem/index?id=${item.id}&title=${item.itemAlias}&taskId=${taskId.value}&businessId=${businessId.value}&taskType=${itemTaskType}&citystandardId=${itemCitystandardId}`

  // 导航成功后的回调中更新状态
  uni.navigateTo({
    url,
    success: () => {
      console.log(`🎯 导航到项目 ${item.id} 成功`)
    },
  })
}

// 🚀 批量状态更新（从store同步时使用）
const batchUpdateItemsStatus = (updates: Array<{ id: string | number; isAnswered: boolean }>) => {
  if (updates.length === 0) return

  const updateTime = Date.now()
  let hasUpdates = false

  // 批量更新
  updates.forEach(({ id, isAnswered }) => {
    tabs.value.forEach((tab) => {
      if (tab.items) {
        const targetItem = tab.items.find((item) => item.id.toString() === id.toString())
        if (targetItem && targetItem.isAnswered !== isAnswered) {
          targetItem.isAnswered = isAnswered
          hasUpdates = true
        }
      }
    })
  })

  // 批量清理缓存
  if (hasUpdates) {
    statsCache.clear() // 批量更新时直接清空缓存
    lastUpdateTime.value = updateTime
    console.log(`🔄 批量更新 ${updates.length} 个项目状态`)
  }
}
</script>

<style lang="scss" scoped>
// 🚀 CSS 变量定义
// 使用项目全局定义的CSS变量
:root {
  // 状态颜色统一定义 - 与 src/style/index.scss 保持一致
  --status-pending-color: #f56c6c; // 未开始状态颜色
  --status-active-color: #e6a23c; // 进行中状态颜色
  --status-completed-color: #67c23a; // 已完成状态颜色
  --status-selected-color: #409eff; // 选中面状态颜色

  // 状态背景色（浅色版本，用于背景和边框）
  --status-pending-bg: #f56c6c1a;
  --status-active-bg: #e6a23c1a;
  --status-completed-bg: #67c23a1a;
  --status-selected-bg: #409eff1a;

  // 状态边框色
  --status-pending-border: #f56c6c4d;
  --status-active-border: #e6a23c4d;
  --status-completed-border: #67c23a4d;
  --status-selected-border: #409eff4d;

  // 项目主题色
  --primary-color: #3b82f6;
  --primary-dark: #1d4ed8;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --success-bg: #f0fdf4;
  --success-border: #dcfce7;
  --warning-bg: #fef3c7;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --shadow-sm: 0 1px 2px #0000000d;
  --shadow-md: 0 1px 3px #0000001f, 0 1px 2px #0000003d;
}

.topic-list-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
    'Open Sans', 'Helvetica Neue', sans-serif;
  background: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.show {
    pointer-events: all;
    opacity: 1;
  }
}

.loading-content {
  text-align: center;

  .loading-text {
    display: block;
    margin-top: 12px;
    font-size: 14px;
    color: var(--text-secondary);
  }
}

// 刷新状态提示
.refresh-tip {
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 12px 16px;
  background: var(--primary-light);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 8px #0000001a;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .refresh-content {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;

    .refresh-text {
      font-size: 13px;
      font-weight: 500;
      color: var(--primary-color);
    }
  }
}

.main-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

// 分类容器
.categories-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: var(--bg-secondary);
}

@media (max-width: 768px) {
  .categories-container {
    padding: 12px;
  }

  .category-section {
    margin-bottom: 16px;
  }

  .section-header {
    padding: 12px;
  }

  .item-card {
    margin-bottom: 12px;
  }

  .card-header {
    padding: 12px;
  }

  .card-content {
    padding: 12px;
  }

  .status-badge {
    padding: 3px 6px;
    font-size: 10px;
  }

  .no-data-state {
    padding: 40px 15px;
    margin: 10px;
  }
}

.category-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 16px;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: 0 2px 8px #0000001f;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .header-left {
    display: flex;
    flex: 1;
    gap: 8px;
    align-items: center;
    min-width: 0;

    .section-title {
      overflow: hidden;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .header-right {
    flex-shrink: 0;

    .stats-info {
      text-align: right;

      .completion-rate {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: var(--success-color);
      }

      .item-count {
        display: block;
        font-size: 11px;
        font-weight: 500;
        color: var(--text-secondary);
        white-space: nowrap;
      }
    }
  }
}

// 🚀 简化的项目列表样式
.item-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// 🚀 基础的项目卡片样式
.item-card {
  position: relative;
  margin-bottom: 12px;
  overflow: hidden;
  cursor: pointer;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:last-child {
    margin-bottom: 0;
  }

  // 🚀 悬停效果增强
  &:hover {
    border-color: var(--border-color);
    box-shadow: 0 6px 16px #00000026;
    transform: translateY(-2px);
  }

  // 🚀 激活状态（点击时）
  &:active {
    box-shadow: 0 4px 12px #0000001f;
    transform: translateY(-1px);
  }
}

// 🚀 已填报项目的特殊样式
.item-card.is-answered {
  background: var(--success-bg);
  border-color: var(--success-border);

  // 🚀 悬停效果增强
  &:hover {
    border-color: var(--success-color);
    box-shadow: 0 6px 16px #16a34a33;
    transform: translateY(-2px);
  }

  // 🚀 激活状态（点击时）
  &:active {
    box-shadow: 0 4px 12px #16a34a26;
    transform: translateY(-1px);
  }
}

// 🚀 基础卡片头部样式
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 0;
  padding: 16px;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
  border-radius: var(--radius-md) var(--radius-md) 0 0;

  .header-content {
    display: flex;
    flex: 1;
    gap: 12px;
    align-items: center;
    min-width: 0;

    .item-icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: #3b82f61a;
      border-radius: var(--radius-md);
      box-shadow: 0 2px 4px #0000000d;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .item-info {
      flex: 1;
      min-width: 0;

      .item-title {
        display: block;
        margin-bottom: 6px;
        overflow: hidden;
        font-size: 16px;
        font-weight: 600;
        line-height: 1.5;
        color: var(--text-primary);
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .item-id {
        display: block;
        overflow: hidden;
        font-size: 12px;
        font-weight: 500;
        color: var(--text-secondary);
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .card-right {
    display: flex;
    flex-shrink: 0;
    gap: 8px;
    align-items: center;

    .arrow-icon {
      flex-shrink: 0;
      opacity: 0.7;
      transition: all 0.3s ease;
    }
  }
}

// 🚀 已填报项目的卡片头部特殊样式
.item-card.is-answered .card-header {
  background: var(--status-completed-bg);
  border-bottom-color: var(--status-completed-border);

  .header-content .item-icon {
    background: #16a34a26;
    transform: scale(1.05);

    :deep(.wd-icon) {
      color: var(--status-completed-color) !important;
    }
  }

  .header-content .item-info .item-title {
    color: var(--status-completed-color);
  }
}

.card-content {
  padding: 16px;
  background: var(--bg-primary);
  border-radius: 0 0 var(--radius-md) var(--radius-md);

  .item-description,
  .item-remarks {
    display: block;
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-secondary);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: 0 2px 8px #0000001a;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .empty-content {
    text-align: center;

    .empty-text {
      display: block;
      margin-top: 12px;
      font-size: 14px;
      color: var(--text-secondary);
    }
  }
}

// 🚀 精简的状态标签样式（移除未使用的状态）
.status-badge {
  display: flex;
  gap: 4px;
  align-items: center;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  border-radius: var(--radius-sm);
  box-shadow: 0 2px 8px #0000001f;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.status-completed {
    color: var(--status-completed-color);
    background: var(--status-completed-bg);
    border: 1px solid var(--status-completed-border);
  }

  &.status-pending {
    color: var(--status-active-color);
    background: var(--status-active-bg);
    border: 1px solid var(--status-active-border);
  }
}

// 🚀 无数据状态样式
.no-data-state {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  margin: 20px;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: 0 6px 16px #0000001f;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .no-data-content {
    text-align: center;

    .no-data-text {
      display: block;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 500;
      color: var(--text-secondary);
    }
  }
}
</style>
