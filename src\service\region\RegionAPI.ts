import { httpGet } from '@/utils/http'

/**
 * 区域数据接口类型
 */
export interface RegionData {
  id: string
  name: string
  code: string
  parentCode?: string
  level: number
  [key: string]: any
}

/**
 * 获取区域列表
 * @param parentCode 父级区域代码，不传则获取顶级区域
 * @returns 区域列表
 */
export const getRegionList = (parentCode?: string): Promise<RegionData[]> => {
  const query = parentCode ? { parentCode } : { parentCode: '222401' }
  return httpGet<RegionData[]>('/urban/region/list', query)
}

/**
 * 根据区域代码获取区域信息
 * @param code 区域代码
 * @returns 区域信息
 */
export const getRegionByCode = (code: string): Promise<RegionData> => {
  return httpGet<RegionData>('/urban/region/get', { code })
}
