import { httpGet, httpPost, httpPut } from '@/utils/http'

// 任务项接口定义
interface TaskItem {
  id: string
  taskName: string
  geom?: string
  [key: string]: any
}

// API响应接口定义
interface TaskListResponse {
  list: TaskItem[]
  [key: string]: any
}

// 房屋数据接口定义
interface HouseItem {
  id: string | number
  name?: string
  geom?: string
  height?: number
  status?: string | null
  createTime?: number
  updateTime?: number
  taskId?: number
  citystandardId?: number
  [key: string]: any
}

// 位置查询房屋的响应格式
type HouseLocationResponse = HouseItem | HouseItem[] | { data: HouseItem | HouseItem[] } | any

/**
 * 获取用户任务列表
 * 参数:
 * - taskId: 任务ID，可选
 * 返回: 包含任务列表的响应对象
 */
export const getMyTasks = (taskType?: string): Promise<TaskListResponse> => {
  return httpGet<TaskListResponse>('/urban/task/my-tasks', { taskType })
}

/**
 * 根据任务ID获取区域数据
 * 参数:
 * - taskId: 任务ID
 * 返回: 区域数据数组的Promise
 */
export const getRegionsByTask = (taskId: string): Promise<HouseItem[]> => {
  return httpGet<HouseItem[]>(`/urban/rs-task-business/get-region-by-task/${taskId}`)
}

/**
 * 根据任务ID获取住房图斑
 * 参数:
 * - taskId: 任务ID
 * 返回: 住房数据数组的Promise
 */
export const getHousesByTask = (taskId: string): Promise<HouseItem[]> => {
  return httpGet<HouseItem[]>(`/urban/rs-task-business/get-houses-by-task/${taskId}`)
}

/**
 * 根据经纬度查询调查住房
 * 参数:
 * - taskId: 任务ID
 * - citystandardId: 城市标准ID
 * - longitude: 经度
 * - latitude: 纬度
 * 返回: 匹配的住房数据数组的Promise
 */
export const getHouseByLocation = (params: {
  taskId: number
  citystandardId: number
  longitude: number
  latitude: number
}): Promise<HouseLocationResponse> => {
  return httpPost<HouseLocationResponse>('/urban/task/house-by-location', params)
}
/**
 * 根据任务ID获取城市标准项
 * 参数:
 * - taskId: 任务ID
 * - businessId: 业务ID，支持字符串或数字类型
 * 返回: 城市标准项数据的Promise
 */
export const getCityStandardItemByTask = (params: {
  taskId: number
  businessId: string | number
}): Promise<any> => {
  return httpGet<any>(`/urban/citystandard-item/get-by-task-id`, { ...params })
}

/**
 * 根据城市标准项ID获取表单数据
 * 参数:
 * - itemId: 城市标准项ID
 * 返回: 包含城市标准项表单数据的Promise对象
 */
export const getCityStandardItemFormByItemId = (itemId: number): Promise<any> => {
  return httpGet<any>(`/urban/citystandard-item-form/get-by-item-id`, { itemId })
}

/**
 * 保存城市标准项数据
 * 参数:
 * - params: 保存参数，包括必填字段
 * 返回: 保存结果的Promise对象
 */
export const saveCityStandardItemData = (params: {
  id: number // 主键ID - 必填
  itemId: number // 指标ID - 必填
  taskId: number // 任务ID - 必填
  businessId: string // 业务ID - 必填
  formData: Record<string, any> // 表单结果 - 必填
  taskType: string // 任务类型 - 必填
  formId: number // 表单ID - 必填
  citystandardId?: number // 城市指标体系ID - 可选
  formRule?: Record<string, any> // 表单组件配置 - 可选
}): Promise<any> => {
  return httpPost<any>('/urban/citystandard-item-data/create', params)
}

/**
 * 获取下一个城市标准项
 * 参数:
 * - taskId: 任务ID
 * - businessId: 业务ID
 * - currentItemId: 当前项ID
 * 返回: 下一个城市标准项数据的Promise对象
 */
export const getNextCityStandardItem = (params: {
  taskId: number
  businessId: number
  currentItemId: number
}): Promise<any> => {
  return httpGet<any>('/urban/citystandard-item/get-next-item', params)
}

/**
 * 根据条件获取城市标准项数据
 * 参数:
 * - taskId: 任务ID - 必填
 * - businessId: 业务ID - 必填
 * - itemId: 指标ID - 必填
 * 返回: 城市标准项数据的Promise对象
 */
export const getCityStandardItemDataByCondition = (params: {
  taskId: number
  businessId: string
  itemId: number
}): Promise<any> => {
  return httpGet<any>('/urban/citystandard-item-data/get-by-condition', params)
}

/**
 * 更新城市标准项数据
 * 参数:
 * - params: 更新参数，包括必填字段
 * 返回: 更新结果的Promise对象
 */
export const updateCityStandardItemData = (params: {
  id: number // 主键ID - 必填
  itemId: number // 指标ID - 必填
  taskId: number // 任务ID - 必填
  businessId: string // 业务ID - 必填
  formData: Record<string, any> // 表单结果 - 必填
  taskType: string // 任务类型 - 必填
  formId: number // 表单ID - 必填
  citystandardId?: number // 城市指标体系ID - 可选
  formRule?: Record<string, any> // 表单组件配置 - 可选
}): Promise<any> => {
  return httpPut<any>('/urban/citystandard-item-data/update', params)
}
