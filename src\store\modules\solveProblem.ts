import { defineStore } from 'pinia'
import { ref } from 'vue'

// 填报项目接口定义
export interface SolveProblemItem {
  id: string | number
  itemName: string
  isAnswered?: boolean // 填报状态
  taskType?: string
  citystandardId?: string | number
  cityStandardId?: string | number
  dimensionLevel2?: string
  status?: string
  [key: string]: any
}

// 填报会话信息
export interface SolveProblemSession {
  taskId: string | number
  businessId: string | number
  currentItemId?: string | number
  allItems: SolveProblemItem[]
  itemsFlattened: SolveProblemItem[] // 扁平化的项目列表，用于导航
}

export const useSolveProblemStore = defineStore('solveProblem', () => {
  // 当前填报会话
  const currentSession = ref<SolveProblemSession | null>(null)

  // 初始化填报会话
  const initSession = (taskId: string | number, businessId: string | number, tabsData: any[]) => {
    // 扁平化所有项目
    const flattenedItems: SolveProblemItem[] = []

    tabsData.forEach((tab) => {
      if (tab.items && Array.isArray(tab.items)) {
        tab.items.forEach((item) => {
          // 🚀 完全保持API状态，不做任何默认值处理
          const processedItem = {
            ...item,
            dimensionLevel2: tab.dimensionLevel2, // 添加分类信息
            taskType: item.taskType || tab.taskType || 'ZF',
            citystandardId:
              item.citystandardId ||
              item.cityStandardId ||
              tab.citystandardId ||
              tab.cityStandardId,
            // 🚀 关键修复：完全保持API原始状态，不要用 || false
            isAnswered: item.isAnswered, // 不使用默认值，保持原始值（包括undefined）
          }

          // 对关键项目进行特别日志记录
          if ([32, 33, 34].includes(Number(item.id))) {
            console.log(`🔧 处理关键项目 ${item.id}:`, {
              原始isAnswered: item.isAnswered,
              原始类型: typeof item.isAnswered,
              处理后isAnswered: processedItem.isAnswered,
              处理后类型: typeof processedItem.isAnswered,
              完整原始项目: JSON.stringify(item),
            })
          }

          flattenedItems.push(processedItem)
        })
      }
    })

    // 🚀 如果已有会话且为同一任务，保留现有的填报状态记录
    let mergedItems = flattenedItems
    if (
      currentSession.value &&
      currentSession.value.taskId.toString() === taskId.toString() &&
      currentSession.value.businessId.toString() === businessId.toString()
    ) {
      console.log('🔄 检测到同一任务的会话更新，开始状态合并...')

      // 🚀 优先保持API数据，只有在特殊情况下才使用store数据
      mergedItems = flattenedItems.map((apiItem) => {
        const existingItem = currentSession.value!.itemsFlattened.find(
          (storeItem) => storeItem.id.toString() === apiItem.id.toString(),
        )

        if (existingItem) {
          // 对关键项目进行特别处理
          if ([32, 33, 34].includes(Number(apiItem.id))) {
            console.log(`🔄 合并关键项目 ${apiItem.id}:`, {
              API状态: apiItem.isAnswered,
              API类型: typeof apiItem.isAnswered,
              Store状态: existingItem.isAnswered,
              Store类型: typeof existingItem.isAnswered,
              将使用: 'API状态（优先）',
            })
          }

          // 🚀 完全优先保持API状态
          return {
            ...apiItem, // 使用API数据为基准
            isAnswered: apiItem.isAnswered, // 完全保持API状态
          }
        }

        return apiItem
      })
    }

    currentSession.value = {
      taskId,
      businessId,
      allItems: tabsData,
      itemsFlattened: mergedItems,
    }

    // 🚀 详细的会话初始化日志
    console.log('🔧 填报会话初始化完成:', {
      taskId,
      businessId,
      totalItems: mergedItems.length,
      completedItems: mergedItems.filter((item) => item.isAnswered === true).length,
    })

    // 🚀 对关键项目进行最终验证
    const targetIds = [32, 33, 34]
    mergedItems.forEach((item) => {
      if (targetIds.includes(Number(item.id))) {
        console.log(`🔧 最终验证项目 ${item.id}:`, {
          id: item.id,
          itemName: item.itemName,
          isAnswered: item.isAnswered,
          type: typeof item.isAnswered,
        })
      }
    })
  }

  // 设置当前项目
  const setCurrentItem = (itemId: string | number) => {
    if (currentSession.value) {
      currentSession.value.currentItemId = itemId
    }
  }

  // 获取当前项目索引
  const getCurrentItemIndex = () => {
    if (!currentSession.value || !currentSession.value.currentItemId) {
      return -1
    }

    return currentSession.value.itemsFlattened.findIndex(
      (item) => item.id.toString() === currentSession.value!.currentItemId!.toString(),
    )
  }

  // 获取上一项
  const getPreviousItem = (): SolveProblemItem | null => {
    const currentIndex = getCurrentItemIndex()
    if (currentIndex <= 0) {
      return null // 已经是第一项
    }

    return currentSession.value!.itemsFlattened[currentIndex - 1]
  }

  // 获取下一项
  const getNextItem = (): SolveProblemItem | null => {
    const currentIndex = getCurrentItemIndex()
    if (currentIndex === -1 || currentIndex >= currentSession.value!.itemsFlattened.length - 1) {
      return null // 已经是最后一项或找不到当前项
    }

    return currentSession.value!.itemsFlattened[currentIndex + 1]
  }

  // 获取当前项目信息
  const getCurrentItem = (): SolveProblemItem | null => {
    const currentIndex = getCurrentItemIndex()
    if (currentIndex === -1) {
      return null
    }

    return currentSession.value!.itemsFlattened[currentIndex]
  }

  // 获取导航信息
  const getNavigationInfo = () => {
    if (!currentSession.value) {
      return {
        currentIndex: -1,
        totalItems: 0,
        hasPrevious: false,
        hasNext: false,
        progress: 0,
      }
    }

    const currentIndex = getCurrentItemIndex()
    const totalItems = currentSession.value.itemsFlattened.length

    return {
      currentIndex: currentIndex + 1, // 显示从1开始
      totalItems,
      hasPrevious: currentIndex > 0,
      hasNext: currentIndex < totalItems - 1,
      progress: totalItems > 0 ? Math.round(((currentIndex + 1) / totalItems) * 100) : 0,
    }
  }

  // 更新项目填报状态
  const updateItemAnsweredStatus = (itemId: string | number, isAnswered: boolean) => {
    if (!currentSession.value) return

    // 更新扁平化列表中的状态
    const itemIndex = currentSession.value.itemsFlattened.findIndex(
      (item) => item.id.toString() === itemId.toString(),
    )

    if (itemIndex !== -1) {
      currentSession.value.itemsFlattened[itemIndex].isAnswered = isAnswered
    }

    // 更新原始数据中的状态
    currentSession.value.allItems.forEach((tab) => {
      if (tab.items && Array.isArray(tab.items)) {
        tab.items.forEach((item) => {
          if (item.id.toString() === itemId.toString()) {
            item.isAnswered = isAnswered
          }
        })
      }
    })

    console.log('🔄 更新项目填报状态:', { itemId, isAnswered })
  }

  // 清除会话
  const clearSession = () => {
    currentSession.value = null
  }

  return {
    currentSession,
    initSession,
    setCurrentItem,
    getCurrentItemIndex,
    getPreviousItem,
    getNextItem,
    getCurrentItem,
    getNavigationInfo,
    updateItemAnsweredStatus,
    clearSession,
  }
})
