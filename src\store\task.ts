/**
 * 任务相关的全局状态管理
 * 创建于：2025-06-13 14:30:47 +08:00
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useTaskStore = defineStore(
  'task',
  () => {
    // 当前任务的WKT几何数据
    const myTaskWktGeom = ref<string>('')

    // 当前任务的状态信息
    const myTaskStatus = ref<number | string>('')

    // 当前任务的完整信息
    const currentTaskInfo = ref<any>(null)

    /**
     * 设置任务的WKT几何数据
     * @param wktGeom WKT格式的几何数据
     */
    const setTaskWktGeom = (wktGeom: string) => {
      myTaskWktGeom.value = wktGeom
      console.log('🚀 全局变量 myTaskWktGeom 已更新:', wktGeom)
    }

    /**
     * 获取任务的WKT几何数据
     * @returns WKT格式的几何数据
     */
    const getTaskWktGeom = (): string => {
      return myTaskWktGeom.value
    }

    /**
     * 设置任务状态
     * @param status 任务状态
     */
    const setTaskStatus = (status: number | string) => {
      myTaskStatus.value = status
      console.log('🚀 全局变量 myTaskStatus 已更新:', status)
    }

    /**
     * 获取任务状态
     * @returns 任务状态
     */
    const getTaskStatus = (): number | string => {
      return myTaskStatus.value
    }

    /**
     * 设置完整的任务信息（包含geom和status）
     * @param taskInfo 任务信息对象
     */
    const setCurrentTaskInfo = (taskInfo: any) => {
      currentTaskInfo.value = taskInfo
      if (taskInfo?.geom) {
        setTaskWktGeom(taskInfo.geom)
      }
      if (taskInfo?.status !== undefined) {
        setTaskStatus(taskInfo.status)
      }
      console.log('🚀 当前任务信息已更新:', taskInfo)
    }

    /**
     * 获取当前任务信息
     * @returns 任务信息对象
     */
    const getCurrentTaskInfo = () => {
      return currentTaskInfo.value
    }

    /**
     * 清空任务的WKT几何数据
     */
    const clearTaskWktGeom = () => {
      myTaskWktGeom.value = ''
      console.log('🚀 全局变量 myTaskWktGeom 已清空')
    }

    /**
     * 清空所有任务数据
     */
    const clearAllTaskData = () => {
      myTaskWktGeom.value = ''
      myTaskStatus.value = ''
      currentTaskInfo.value = null
      console.log('🚀 所有任务数据已清空')
    }

    return {
      myTaskWktGeom,
      myTaskStatus,
      currentTaskInfo,
      setTaskWktGeom,
      getTaskWktGeom,
      setTaskStatus,
      getTaskStatus,
      setCurrentTaskInfo,
      getCurrentTaskInfo,
      clearTaskWktGeom,
      clearAllTaskData,
    }
  },
  {
    persist: true, // 启用数据持久化
  },
)
