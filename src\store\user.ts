import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getAccessToken, removeToken, setToken } from '@/utils/auth'
import { getInfo, loginOut } from '@/service/login/LoginAPI'
import { currRoute } from '@/utils'
import { useDictStore } from '@/store/dict'
import { changePassword as changePasswordAPI } from '@/service/userAPI'

const initState = {
  permissions: [],
  roles: [],
  isSetUser: false,
  user: {
    id: 0,
    avatar: '',
    nickname: '',
    deptId: 0,
  },
}

export const useUserStore = defineStore(
  'user',
  () => {
    // state

    const userInfo = ref<UserInfoVO>({ ...initState })

    // actions methods
    const setUserInfoAction = async () => {
      if (!getAccessToken()) {
        // 获取不到accessToken，直接返回
        resetState()
        return
      }

      const data = await getInfo()
      userInfo.value = {
        ...data,
        isSetUser: true,
      }
    }

    const setUserAvatarAction = async (avatar: string) => {
      userInfo.value.user.avatar = avatar
    }

    const setUserNicknameAction = async (nickname: string) => {
      userInfo.value.user.nickname = nickname
    }

    const LogOut = async () => {
      await loginOut()
      removeToken()
      resetState()
    }

    const resetState = () => {
      console.log('initState', initState)
      userInfo.value = initState
      console.log('重置userInfo', userInfo.value)
    }
    /** 登录后的回调 */
    const afterLogin = async (params: Recordable) => {
      // 设置 token
      setToken(params)
      // 获取用户信息，保存到 store
      await setUserInfoAction()
      const dictStore = useDictStore()
      if (!dictStore.dictInfo.isSetDict) {
        await dictStore.setDictMap()
      }
      // 跳转为登录前的页面（redirect）
      const fullPath = currRoute()
      uni.reLaunch({
        url: fullPath.query.redirect || '/pages/work/index',
      })
    }

    /** 修改密码 */
    const changePassword = async ({
      oldPassword,
      newPassword,
    }: {
      oldPassword: string
      newPassword: string
    }) => {
      return await changePasswordAPI({ oldPassword, newPassword })
    }

    // 暴露到外面的方法
    return {
      userInfo,
      setUserInfoAction,
      setUserAvatarAction,
      setUserNicknameAction,
      LogOut,
      resetState,
      afterLogin,
      changePassword,
    }
  },
  {
    // 持久化
    persist: true,
  },
)
