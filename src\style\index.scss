@font-face {
  font-family: 'HarmonyOS_Sans';
  src: url('https://oss.urban.udo.top/public/fonts/HarmonyOS_Sans_Condensed_Regular.ttf')
    format('opentype');
}

body {
  font-family: 'HarmonyOS_Sans' !important;
}

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

:root,
page {
  // 修改按主题色
  // --wot-color-theme: #37c2bc;

  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;

  //
  --wot-search-light-bg: #f2f3f7;
  --wot-search-input-radius: 5px;

  // 状态颜色统一定义 - 用于任务状态、进度条、统计数字等
  --status-pending-color: #f56c6c; // 未开始状态颜色
  --status-active-color: #e6a23c; // 进行中状态颜色
  --status-completed-color: #67c23a; // 已完成状态颜色
  --status-selected-color: #409eff; // 选中面状态颜色

  // 状态背景色（浅色版本，用于背景和边框）
  --status-pending-bg: #f56c6c1a;
  --status-active-bg: #e6a23c1a;
  --status-completed-bg: #67c23a1a;
  --status-selected-bg: #409eff1a;

  // 状态边框色
  --status-pending-border: #f56c6c4d;
  --status-active-border: #e6a23c4d;
  --status-completed-border: #67c23a4d;
  --status-selected-border: #409eff4d;
}

.yt-input-textAlign {
  text-align: end;
}

.yt-textarea-textAlign {
  text-align: end !important;
}

// 多行输入框美化样式
.textarea-cell {
  padding: 8px 0;

  .wd-cell__title {
    margin-bottom: 8px !important;
    font-weight: 500 !important;
    color: #333 !important;
  }

  .wd-cell__value {
    width: 100% !important;
  }
}

.beautiful-textarea {
  .wd-textarea__inner {
    padding: 12px 16px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #333 !important;
    resize: none !important;
    background-color: #fafbfc !important;
    border: 1px solid #e4e7ed !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
  }

  .wd-textarea__inner:focus {
    background-color: #fff !important;
    border-color: #409eff !important;
    outline: none !important;
    box-shadow: 0 0 0 2px #409eff1a !important;
  }

  .wd-textarea__inner:hover {
    border-color: #c0c4cc !important;
  }

  .wd-textarea__count {
    padding: 2px 6px !important;
    font-size: 12px !important;
    color: #909399 !important;
    background-color: #ffffffcc !important;
    border-radius: 4px !important;
  }
}

.compact-secondary-btn {
  display: flex;
  flex: 1;
  gap: 4px;
  align-items: center;
  justify-content: center;
  padding: 8px 14px;
  font-size: 13px;
  font-weight: 600;
  color: #475569;
  text-align: center;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px !important;
  transition: all 0.2s ease;
}

// 🚀 2025-06-25 16:07:25 +08:00 - 自定义 Wot Design Uni Tabs 样式
.yt-custom-tabs {
  // 标签页头部
  :deep(.wd-tabs__nav) {
    padding: 12px 16px 0 16px;
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 2px #0000000d;
  }

  // 标签页滚动容器
  :deep(.wd-tabs__nav-wrap) {
    background: #ffffff;
  }

  // 标签项容器
  :deep(.wd-tabs__nav-scroll) {
    display: flex;
    gap: 8px;
  }

  // 单个标签项 - 恢复原始简洁风格
  :deep(.wd-tab__title) {
    position: relative;
    display: flex;
    flex: 1;
    gap: 6px;
    align-items: center;
    justify-content: center;
    min-width: 0;
    padding: 10px 12px !important;
    overflow: hidden;
    font-size: 13px;
    font-weight: 500;
    color: #64748b;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: #f1f5f9;
    border-radius: 8px 8px 0 0;
    transition: all 0.2s ease;

    &:hover {
      color: #475569;
      background: #e2e8f0;
    }
  }

  // 激活状态的标签项
  :deep(.wd-tab__title.is-active) {
    color: #ffffff !important;
    background: #3b82f6 !important;
    box-shadow: 0 2px 8px #3b82f64d;
    transform: translateY(-1px);
  }

  // 底部指示线（隐藏默认的，使用自定义背景）
  :deep(.wd-tabs__line) {
    display: none;
  }

  // 标签页内容区域
  :deep(.wd-tabs__content) {
    flex: 1;
    overflow-y: auto;
    background: #f8fafc;
    -webkit-overflow-scrolling: touch;
  }

  // 单个标签页面板
  :deep(.wd-tab-pane) {
    height: 100%;
  }
}

// 响应式设计 - 移动端优化
@media (max-width: 768px) {
  .custom-tabs {
    :deep(.wd-tabs__nav) {
      padding: 8px 12px 0 12px;
    }

    :deep(.wd-tabs__nav-scroll) {
      gap: 6px;
    }

    :deep(.wd-tab__title) {
      gap: 4px;
      padding: 8px 10px !important;
      font-size: 12px;
    }
  }
}

@media (max-width: 375px) {
  .custom-tabs {
    :deep(.wd-tabs__nav) {
      padding: 6px 8px 0 8px;
    }

    :deep(.wd-tabs__nav-scroll) {
      gap: 4px;
    }

    :deep(.wd-tab__title) {
      gap: 2px;
      padding: 6px 8px !important;
      font-size: 11px;
    }
  }
}
.yt-checkbox-group {
  .wd-checkbox {
    white-space: nowrap;
  }

  // checkbox 文本标签样式 - 支持换行和空格保留
  .wd-checkbox__label {
    white-space: pre-wrap !important;
  }

  // 兼容可能存在的 txt 类名
  .wd-checkbox__txt {
    white-space: pre-wrap !important;
  }
}
