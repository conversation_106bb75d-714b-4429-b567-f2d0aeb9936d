// 表单项基础接口
export interface FormItem {
  type:
    | 'input'
    | 'textarea'
    | 'select'
    | 'checkbox'
    | 'calendar'
    | 'timePicker'
    | 'datePicker'
    | 'datetime-picker'
    | 'RegionCascader'
    | 'MapSelector'
    | 'inputNumber'
    | 'DynamicCheckboxWithUpload'
    | 'upload' // 新增上传类型
    | 'tableForm' // 新增表格表单类型
    | 'MapDraw' // 新增地图绘图类型
    | 'DictSelectWithOther' // 新增字典选择带其他选项类型
    | 'dict-select' // 新增字典选择类型
    | 'DictSelect' // 新增字典选择类型（大写版本，兼容性）
    | 'NoticeBar' // 新增通知栏类型
    | 'eAlert' // 新增警告提示类型
    | 'elAlert' // 新增Element Alert警告提示类型
    | 'elDivider' // 新增分割线类型
  field?: string // 对于某些特殊组件（如分割线），field为可选
  title?: string // 某些展示型组件可能不需要标题
  info?: string
  $required?: boolean | string // 支持布尔值或字符串(错误提示信息)
  props?: FormItemProps | TableFormProps // 修正为支持表格表单属性
  display?: boolean
  hidden?: boolean
  _fc_id?: string
  name?: string
  _fc_drag_tag?: string
  // form-create特有属性
  children?: string[] | string // 支持children属性（用于文本内容）
  // select 特有属性
  options?: FormSelectOption[] // 选项列表
  // tableForm 特有属性
  columns?: TableColumn[] // 表格列配置
  // RegionCascader 行政区域级联选择器特有属性
  rootParentCode?: string // 根级区域代码，用于指定级联选择器的起始数据源
  // col-picker 级联选择器特有属性
  apiUrl?: string // API地址
  // 新增: 条件判断函数，用于动态显示/隐藏表单项
  condition?: (formModel: Record<string, any>) => boolean
  control?: ControlConfig[]
  [key: string]: any
}

// 选择器选项接口
export interface FormSelectOption {
  label: string
  value: string | number
  disabled?: boolean
  [key: string]: any
}

// 表单项属性配置
export interface FormItemProps {
  disabled?: boolean
  readonly?: boolean
  maxlength?: number
  minlength?: number
  clearable?: boolean
  showPassword?: boolean
  suffixIcon?: string
  prefixIcon?: string
  placeholder?: string
  // textarea 特有属性
  rows?: number // 行数
  autosize?: boolean // 自适应高度
  showCount?: boolean // 显示字符计数
  // select 特有属性
  multiple?: boolean // 是否多选
  _optionType?: number // 选项类型
  filterable?: boolean // 是否可筛选
  // checkbox 特有属性
  shape?: 'circle' | 'square' | 'button' // 复选框形状
  checkedColor?: string // 选中颜色
  inline?: boolean // 是否同行展示
  cell?: boolean // 是否表单模式
  min?: number // 最小选中数量
  max?: number // 最大选中数量，0为无限制
  withCell?: boolean // 是否使用Cell模式渲染
  cellBorder?: boolean // Cell模式下是否显示边框
  size?: 'large' | string // 尺寸
  maxWidth?: string // 文字位置最大宽度
  // calendar 特有属性
  calendarType?:
    | 'date'
    | 'dates'
    | 'week'
    | 'month'
    | 'daterange'
    | 'weekrange'
    | 'monthrange'
    | 'datetime'
    | 'datetimerange' // 日历类型
  minDate?: number // 最小日期（13位时间戳）
  maxDate?: number // 最大日期（13位时间戳）
  showConfirm?: boolean // 是否显示确认按钮
  showTypeSwitch?: boolean // 是否显示类型切换
  firstDayOfWeek?: number // 一周的第一天 (0-6, 0为周日)
  allowSameDay?: boolean // 范围选择是否允许同一天
  hideSecond?: boolean // 是否隐藏秒
  timeFilter?: any // 时间过滤函数（使用 any 类型避免复杂的类型匹配）
  withCellSelector?: boolean // 是否使用内置 cell 选择器
  // datetime-picker 特有属性
  pickerType?: 'datetime' | 'date' | 'year-month' | 'time' | 'year' // 选择器类型
  defaultValue?: number | string | number[] | string[] // 默认值
  loading?: boolean // 加载状态
  loadingColor?: string // 加载颜色
  columnsHeight?: number // 滚筒高度
  title?: string // 弹出层标题
  cancelButtonText?: string // 取消按钮文字
  confirmButtonText?: string // 确认按钮文字
  displayFormat?: any // 自定义显示文本格式化函数
  formatter?: any // 自定义弹出层选项文本格式化函数
  filter?: any // 自定义过滤选项函数
  displayFormatTabLabel?: any // 范围选择模式下，自定义标签显示格式化函数
  minHour?: number // 最小小时，对 time 类型有效
  maxHour?: number // 最大小时，对 time 类型有效
  minMinute?: number // 最小分钟，对 time 类型有效
  maxMinute?: number // 最大分钟，对 time 类型有效
  error?: boolean // 错误状态
  alignRight?: boolean // 右对齐显示
  beforeConfirm?: any // 确认前验证函数
  closeOnClickModal?: boolean // 点击遮罩是否关闭
  zIndex?: number // 弹出层层级
  safeAreaInsetBottom?: boolean // 是否设置底部安全距离
  ellipsis?: boolean // 是否超出隐藏
  prop?: string // 表单字段名
  rules?: any[] // 表单验证规则
  immediateChange?: boolean // 是否立即触发change事件
  // col-picker 级联选择器特有属性
  apiUrl?: string // API地址
  columns?: any[][] | TableColumn[] // 列数据，支持多种类型
  columnChange?: any // 列变化回调函数
  useDefaultSlot?: boolean // 使用默认插槽
  useLabelSlot?: boolean // 使用label插槽
  autoComplete?: boolean // 自动补全数据
  lineWidth?: number // 底部条宽度
  lineHeight?: number // 底部条高度
  // map-selector 地图选择器特有属性
  mapType?: 'point' | 'area' | 'polygon' // 地图选择类型：点选、区域选择、多边形绘制
  defaultLocation?: {
    latitude: number
    longitude: number
  } // 默认地图中心点
  mapScale?: number // 地图缩放级别
  showLocationBtn?: boolean // 是否显示定位按钮
  enableDraw?: boolean // 是否启用绘制功能
  drawType?: 'point' | 'polyline' | 'polygon' // 绘制类型
  mapWidth?: string // 地图宽度
  mapHeight?: string // 地图高度
  // upload 上传组件特有属性
  action?: string // 上传地址
  accept?: 'image' | 'video' | 'media' | 'file' | 'all' // 文件类型
  limit?: number // 最大上传数量
  reupload?: boolean // 是否覆盖上传
  maxSize?: number // 文件大小限制(MB)
  extension?: string[] // 文件扩展名过滤
  imageMode?: string // 图片显示模式
  compressed?: boolean // 是否压缩视频
  maxDuration?: number // 视频最大时长(秒)
  camera?: 'front' | 'back' // 摄像头方向
  successStatus?: number // 成功状态码
  autoUpload?: boolean // 是否自动上传
  beforeUpload?: (params: any) => boolean | Promise<boolean> // 上传前钩子
  beforePreview?: (params: any) => boolean | Promise<boolean> // 预览前钩子
  uploadMethod?: (params: any) => Promise<any> // 自定义上传方法
  // DictSelectWithOther 字典选择带其他选项特有属性
  dictType?: string // 字典类型
  selectType?: 'checkbox' | 'radio' // 选择类型
  otherLabel?: string // "其他"选项的标签文本
  otherInputPlaceholder?: string // "其他"输入框的占位符
  keepOtherTextOnUnselect?: boolean // 取消选择"其他"时是否保留输入文本
  valueType?: 'str' | 'array' // 值类型
  // dict-select 字典选择特有属性
  allowEmpty?: boolean // 是否允许空值
  emptyText?: string // 空值显示文本
  autoLoad?: boolean // 是否自动加载字典数据
  filterPlaceholder?: string // 搜索占位符
  customClass?: string // 自定义样式类
  customLabelClass?: string // 自定义标签样式类
  customValueClass?: string // 自定义值样式类
  customContentClass?: string // 自定义内容样式类
  // inputNumber 数字输入器特有属性
  step?: number // 步长
  precision?: number // 小数精度
  allowNull?: boolean // 是否允许空值
  disableInput?: boolean // 是否禁用输入框
  withoutInput?: boolean // 是否不显示输入框
  inputWidth?: string // 输入框宽度
  longPress?: boolean // 是否支持长按
  stepStrictly?: boolean // 是否严格步数
  beforeChange?: (value: number | string) => boolean | Promise<boolean> // 值变化前的回调
  // 补充新增属性
  disablePlus?: boolean // 禁用增加按钮
  disableMinus?: boolean // 禁用减少按钮
  adjustPosition?: boolean // 键盘弹起时，是否自动上推页面
  updateOnInit?: boolean // 是否在初始化时更新v-model为修正后的值
  inputType?: 'number' | 'digit' // 输入框类型
  [key: string]: any
}

// 表格列接口
export interface TableColumn {
  label: string
  required?: boolean
  style?: {
    width?: string | number
    [key: string]: any
  }
  rule: FormItem[] // 列中的表单项规则
  [key: string]: any
}

// 表格表单属性配置
export interface TableFormProps {
  columns?: TableColumn[]
  [key: string]: any
}

// 表单配置
export interface FormConfig {
  items: FormItem[]
  model: Record<string, any>
  rules?: Record<string, any>
}

/**
 * 上传文件数据结构
 */
export interface UploadFile {
  uid?: number // 唯一标识
  url?: string // 文件地址
  action?: string // 上传地址
  percent?: number // 上传进度
  size?: number // 文件大小
  status?: string // 上传状态
  response?: string | object // 服务器响应
  name?: string // 文件名
  type?: string // 文件类型
  thumb?: string // 缩略图地址(视频封面)
}

// NoticeBar 通知栏特有属性
export interface NoticeBarProps {
  text?: string | string[] // 通知栏文案
  type?: 'info' | 'warning' | 'danger' // 通知栏类型
  alertType?: 'info' | 'warning' | 'danger' // eAlert类型映射
  prefix?: string // 左侧图标
  scrollable?: boolean // 是否可以滚动
  delay?: number // 滚动动画初始延时(秒)
  speed?: number // 滚动速度(px/s)
  closable?: boolean // 是否可以关闭
  wrapable?: boolean // 是否换行展示
  color?: string // 文字、图标颜色
  backgroundColor?: string // 背景颜色
  direction?: 'horizontal' | 'vertical' // 滚动方向
  prefixSlot?: string // 前置图标插槽内容
  suffixSlot?: string // 后置插槽内容
  // elAlert 特有属性
  description?: string // elAlert 描述文本
  effect?: 'light' | 'dark' // elAlert 主题效果
}

// Divider 分割线特有属性
export interface DividerProps {
  color?: string // 自定义颜色
  hairline?: boolean // 是否显示边框
  dashed?: boolean // 是否显示为虚线
  contentPosition?: 'left' | 'center' | 'right' // 内容位置
  vertical?: boolean // 是否显示为垂直分割线
  // 支持children属性（来自form-create格式）
  children?: string[] | string // 分割线中间的文本内容
}

export interface ControlConfig {
  value?: any
  condition?: string // '==', '!=', 'in', ...
  // handle?: (val: any, model: Record<string, any>) => boolean; // 暂不支持自定义handle
  method?: 'display' | 'hidden' | 'required' | 'disabled' | 'if' // 支持'if'，等同于hidden
  rule: string[] // 被控制的字段名
}
