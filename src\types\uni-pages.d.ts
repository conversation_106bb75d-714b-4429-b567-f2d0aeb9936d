/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/work/index" |
       "/pages/topicList/index" |
       "/pages/solveProblem/index" |
       "/pages/dataEntry/index" |
       "/pages/colab/index" |
       "/pages/contacts/index" |
       "/pages/demo/formDemo" |
       "/pages/demo/map" |
       "/pages/demo/map2" |
       "/pages/login/index" |
       "/pages/mapDraw/mapDraw" |
       "/pages/mapDraw/mapDrawPlot" |
       "/pages/message/index" |
       "/pages/my/change-password" |
       "/pages/my/index" |
       "/pages/my/privacy-policy";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/work/index" | "/pages/my/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
