import { TENANT_CONFIG } from '@/config/global'

const AccessTokenKey = 'ACCESS_TOKEN'
const RefreshTokenKey = 'REFRESH_TOKEN'
const TenantIdKey = 'tenantId'

// ========== Token 相关 ==========

// 获取 Token
export function getAccessToken() {
  return uni.getStorageSync(AccessTokenKey)
}

// 获取 RefreshToken
export function getRefreshToken() {
  return uni.getStorageSync(RefreshTokenKey)
}

// 设置 Token
export function setToken(token) {
  uni.setStorageSync(AccessTokenKey, token.accessToken)
  uni.setStorageSync(RefreshTokenKey, token.refreshToken)
}

// 移除 Token
export function removeToken() {
  uni.removeStorageSync(AccessTokenKey)
  uni.removeStorageSync(RefreshTokenKey)
}

// ========== 租户相关 ==========

export const getTenantId = () => {
  const tenantId = uni.getStorageSync(TenantIdKey)
  // 如果没有设置租户ID，返回默认值
  return tenantId || TENANT_CONFIG.DEFAULT_TENANT_ID
}

export const setTenantId = (tenantId: string) => {
  uni.setStorageSync(TenantIdKey, tenantId)
}

export const removeTenantId = () => {
  uni.removeStorageSync(TenantIdKey)
}

export const getDefaultTenantId = () => {
  return TENANT_CONFIG.DEFAULT_TENANT_ID
}
