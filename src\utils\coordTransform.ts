/**
 * 坐标系转换工具 (基于 gcoord 库)
 * 支持 WGS84 ↔ GCJ02 精确转换
 *
 * WGS84: 世界大地测量系统坐标系，GPS设备采用的坐标系
 * GCJ02: 中国国家测绘局制定的地理坐标系统，也叫火星坐标系
 *
 * 使用 gcoord 库确保转换精度和可靠性
 *
 * @example
 * // 基本使用
 * import { wgs84ToGcj02, gcj02ToWgs84 } from '@/utils/coordTransform'
 *
 * const wgsPoint = { latitude: 39.9042, longitude: 116.4074 }
 * const gcjPoint = wgs84ToGcj02(wgsPoint)
 * console.log(gcjPoint) // { latitude: 39.905603, longitude: 116.413642 }
 *
 * const backToWgs = gcj02ToWgs84(gcjPoint)
 * console.log(backToWgs) // 接近原始 wgsPoint
 *
 * <AUTHOR> Assistant
 * @version 2.0.0 (使用gcoord库)
 * @since 2025-06-11
 */

import gcoord from 'gcoord'

// 坐标点接口
export interface CoordPoint {
  /** 纬度 (-90 到 90) */
  latitude: number
  /** 经度 (-180 到 180) */
  longitude: number
}

/**
 * WGS84坐标转换为GCJ02坐标 (使用gcoord库)
 *
 * WGS84 是 GPS 设备使用的标准坐标系，而 GCJ02 是中国地图使用的坐标系。
 * 这个转换对于在中国地图上正确显示GPS坐标非常重要。
 *
 * @param wgsPoint WGS84坐标点
 * @returns GCJ02坐标点
 *
 * @example
 * const wgsPoint = { latitude: 39.9042, longitude: 116.4074 } // 北京天安门 GPS坐标
 * const gcjPoint = wgs84ToGcj02(wgsPoint)
 * console.log(gcjPoint) // { latitude: 39.905603, longitude: 116.413642 } 中国地图坐标
 */
export function wgs84ToGcj02(wgsPoint: CoordPoint): CoordPoint {
  try {
    const { latitude: wgsLat, longitude: wgsLon } = wgsPoint

    // 验证输入坐标的有效性
    if (
      typeof wgsLat !== 'number' ||
      typeof wgsLon !== 'number' ||
      isNaN(wgsLat) ||
      isNaN(wgsLon) ||
      wgsLat < -90 ||
      wgsLat > 90 ||
      wgsLon < -180 ||
      wgsLon > 180
    ) {
      console.warn('⚠️ 无效的WGS84坐标，返回原坐标:', wgsPoint)
      return wgsPoint
    }

    // 使用 gcoord 进行精确转换
    // 注意：gcoord 的输入格式是 [longitude, latitude] (经度在前)
    const result = gcoord.transform(
      [wgsLon, wgsLat], // [经度, 纬度]
      gcoord.WGS84, // 源坐标系
      gcoord.GCJ02, // 目标坐标系
    )

    const gcjPoint = {
      latitude: result[1], // 纬度
      longitude: result[0], // 经度
    }

    // 详细的转换日志
    // console.log('🌍 WGS84→GCJ02转换 (gcoord):', {
    //   input: { lat: wgsLat, lon: wgsLon, system: 'WGS84' },
    //   output: { lat: gcjPoint.latitude, lon: gcjPoint.longitude, system: 'GCJ02' },
    //   offset: {
    //     lat: (gcjPoint.latitude - wgsLat).toFixed(6) + '°',
    //     lon: (gcjPoint.longitude - wgsLon).toFixed(6) + '°',
    //   },
    // })

    return gcjPoint
  } catch (error) {
    console.error('❌ WGS84→GCJ02转换失败:', error, '原坐标:', wgsPoint)
    // 转换失败时返回原坐标，确保应用不会崩溃
    return wgsPoint
  }
}

/**
 * GCJ02坐标转换为WGS84坐标 (使用gcoord库)
 *
 * 将中国地图坐标系转换回GPS标准坐标系。
 * 通常用于将地图上的点转换为GPS设备可识别的坐标。
 *
 * @param gcjPoint GCJ02坐标点
 * @returns WGS84坐标点
 *
 * @example
 * const gcjPoint = { latitude: 39.905603, longitude: 116.413642 } // 中国地图坐标
 * const wgsPoint = gcj02ToWgs84(gcjPoint)
 * console.log(wgsPoint) // { latitude: 39.9042, longitude: 116.4074 } GPS坐标
 */
export function gcj02ToWgs84(gcjPoint: CoordPoint): CoordPoint {
  try {
    const { latitude: gcjLat, longitude: gcjLon } = gcjPoint

    // 验证输入坐标的有效性
    if (
      typeof gcjLat !== 'number' ||
      typeof gcjLon !== 'number' ||
      isNaN(gcjLat) ||
      isNaN(gcjLon) ||
      gcjLat < -90 ||
      gcjLat > 90 ||
      gcjLon < -180 ||
      gcjLon > 180
    ) {
      console.warn('⚠️ 无效的GCJ02坐标，返回原坐标:', gcjPoint)
      return gcjPoint
    }

    // 使用 gcoord 进行精确转换
    // 注意：gcoord 的输入格式是 [longitude, latitude] (经度在前)
    const result = gcoord.transform(
      [gcjLon, gcjLat], // [经度, 纬度]
      gcoord.GCJ02, // 源坐标系
      gcoord.WGS84, // 目标坐标系
    )

    const wgsPoint = {
      latitude: result[1], // 纬度
      longitude: result[0], // 经度
    }

    // 详细的转换日志
    console.log('🌍 GCJ02→WGS84转换 (gcoord):', {
      input: { lat: gcjLat, lon: gcjLon, system: 'GCJ02' },
      output: { lat: wgsPoint.latitude, lon: wgsPoint.longitude, system: 'WGS84' },
      offset: {
        lat: (wgsPoint.latitude - gcjLat).toFixed(6) + '°',
        lon: (wgsPoint.longitude - gcjLon).toFixed(6) + '°',
      },
    })

    return wgsPoint
  } catch (error) {
    console.error('❌ GCJ02→WGS84转换失败:', error, '原坐标:', gcjPoint)
    // 转换失败时返回原坐标，确保应用不会崩溃
    return gcjPoint
  }
}

/**
 * 批量转换坐标点数组的通用函数
 *
 * @param points 坐标点数组
 * @param transform 转换函数 (wgs84ToGcj02 或 gcj02ToWgs84)
 * @returns 转换后的坐标点数组
 *
 * @example
 * const wgsPoints = [
 *   { latitude: 39.9042, longitude: 116.4074 },
 *   { latitude: 31.2304, longitude: 121.4737 }
 * ]
 * const gcjPoints = transformPoints(wgsPoints, wgs84ToGcj02)
 */
export function transformPoints(
  points: CoordPoint[],
  transform: (point: CoordPoint) => CoordPoint,
): CoordPoint[] {
  if (!Array.isArray(points)) {
    console.warn('⚠️ transformPoints: 输入不是数组, 返回空数组')
    return []
  }

  console.log(`🔄 开始批量转换 ${points.length} 个坐标点...`)

  const result = points.map((point, index) => {
    try {
      return transform(point)
    } catch (error) {
      console.error(`❌ 第 ${index + 1} 个坐标点转换失败:`, point, error)
      return point // 失败时返回原坐标
    }
  })

  console.log(`✅ 批量转换完成, 成功转换 ${result.length} 个坐标点`)
  return result
}

/**
 * 批量WGS84转GCJ02坐标
 *
 * @param wgsPoints WGS84坐标点数组
 * @returns GCJ02坐标点数组
 *
 * @example
 * const wgsPoints = [
 *   { latitude: 39.9042, longitude: 116.4074 }, // 北京
 *   { latitude: 31.2304, longitude: 121.4737 }  // 上海
 * ]
 * const gcjPoints = batchWgs84ToGcj02(wgsPoints)
 * console.log(gcjPoints) // 转换后的中国地图坐标
 */
export function batchWgs84ToGcj02(wgsPoints: CoordPoint[]): CoordPoint[] {
  return transformPoints(wgsPoints, wgs84ToGcj02)
}

/**
 * 批量GCJ02转WGS84坐标
 *
 * @param gcjPoints GCJ02坐标点数组
 * @returns WGS84坐标点数组
 *
 * @example
 * const gcjPoints = [
 *   { latitude: 39.905603, longitude: 116.413642 }, // 北京（中国地图坐标）
 *   { latitude: 31.23189, longitude: 121.480139 }   // 上海（中国地图坐标）
 * ]
 * const wgsPoints = batchGcj02ToWgs84(gcjPoints)
 * console.log(wgsPoints) // 转换后的GPS坐标
 */
export function batchGcj02ToWgs84(gcjPoints: CoordPoint[]): CoordPoint[] {
  return transformPoints(gcjPoints, gcj02ToWgs84)
}

/**
 * 计算两点之间的距离（米）- 使用 Haversine 公式
 *
 * @param point1 第一个点
 * @param point2 第二个点
 * @returns 距离（米）
 *
 * @example
 * const beijing = { latitude: 39.9042, longitude: 116.4074 }
 * const shanghai = { latitude: 31.2304, longitude: 121.4737 }
 * const distance = calculateDistance(beijing, shanghai)
 * console.log(distance) // 约 1068000 米 (1068公里)
 */
export function calculateDistance(point1: CoordPoint, point2: CoordPoint): number {
  if (
    !point1 ||
    !point2 ||
    typeof point1.latitude !== 'number' ||
    typeof point1.longitude !== 'number' ||
    typeof point2.latitude !== 'number' ||
    typeof point2.longitude !== 'number'
  ) {
    console.warn('⚠️ calculateDistance: 无效的坐标点')
    return 0
  }

  const R = 6371000 // 地球半径（米）
  const lat1Rad = (point1.latitude * Math.PI) / 180
  const lat2Rad = (point2.latitude * Math.PI) / 180
  const deltaLatRad = ((point2.latitude - point1.latitude) * Math.PI) / 180
  const deltaLonRad = ((point2.longitude - point1.longitude) * Math.PI) / 180

  const a =
    Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return R * c
}

/**
 * 检查坐标是否在中国境内 (大致范围)
 *
 * @param point 坐标点
 * @returns 是否在中国境内
 *
 * @example
 * const beijing = { latitude: 39.9042, longitude: 116.4074 }
 * const newYork = { latitude: 40.7128, longitude: -74.0060 }
 * console.log(isInChina(beijing)) // true
 * console.log(isInChina(newYork)) // false
 */
export function isInChina(point: CoordPoint): boolean {
  if (!point || typeof point.latitude !== 'number' || typeof point.longitude !== 'number') {
    return false
  }

  const { latitude: lat, longitude: lon } = point
  // 中国大致经纬度范围
  return lon >= 72.004 && lon <= 137.8347 && lat >= 0.8293 && lat <= 55.8271
}

/**
 * 坐标转换测试函数
 * 测试WGS84→GCJ02→WGS84的完整转换流程，评估精度损失
 *
 * @param testPoint 测试坐标点 (WGS84)，默认为用户提供的坐标
 * @returns 完整的转换结果和精度报告
 *
 * @example
 * // 使用默认坐标测试
 * const result = testCoordTransform()
 *
 * // 使用自定义坐标测试
 * const customResult = testCoordTransform({ latitude: 39.9042, longitude: 116.4074 })
 * console.log(customResult.precisionLoss) // 精度损失（米）
 */
export function testCoordTransform(
  testPoint: CoordPoint = { latitude: 42.916, longitude: 129.504 },
) {
  console.log('🧪 开始坐标转换测试 (使用gcoord库)...')
  console.log('📍 测试坐标 (WGS84):', testPoint)

  // WGS84 → GCJ02
  const gcjResult = wgs84ToGcj02(testPoint)
  console.log('📍 转换结果 (GCJ02):', gcjResult)

  // GCJ02 → WGS84 (验证逆转换)
  const wgsResult = gcj02ToWgs84(gcjResult)
  console.log('📍 逆转换结果 (WGS84):', wgsResult)

  // 计算精度损失
  const distance = calculateDistance(testPoint, wgsResult)
  console.log('📏 往返转换精度损失:', distance.toFixed(3), '米')

  // 检查是否在中国
  console.log('🇨🇳 是否在中国境内:', isInChina(testPoint))

  console.log('✅ 坐标转换测试完成')

  return {
    original: testPoint,
    gcj02: gcjResult,
    backToWgs84: wgsResult,
    precisionLoss: distance,
  }
}
