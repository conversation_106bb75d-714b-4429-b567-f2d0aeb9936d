/**
 * 坐标转换工具 (基于 coordtransform 库)
 * 支持 WGS84 ↔ GCJ02 精确转换
 *
 * WGS84: 世界大地测量系统坐标系，GPS设备采用的坐标系
 * GCJ02: 中国国家测绘局制定的地理坐标系统，也叫火星坐标系
 *
 * 使用 coordtransform 库确保转换精度和可靠性
 *
 * @example
 * // 基本使用
 * import { wgs84ToGcj02, gcj02ToWgs84 } from '@/utils/coordTransformNew'
 *
 * const wgsPoint = { latitude: 39.9042, longitude: 116.4074 }
 * const gcjPoint = wgs84ToGcj02(wgsPoint)
 * console.log(gcjPoint) // { latitude: 39.905603, longitude: 116.413642 }
 *
 * const backToWgs = gcj02ToWgs84(gcjPoint)
 * console.log(backToWgs) // 接近原始 wgsPoint
 *
 * <AUTHOR> Assistant
 * @version 4.0.0 (只包含WGS84↔GCJ02转换)
 * @since 2025-01-17
 */

// 导入 coordtransform 库
import { gcj02towgs84, wgs84togcj02 } from 'coordtransform'

// 坐标点接口
export interface CoordPoint {
  /** 纬度 (-90 到 90) */
  latitude: number
  /** 经度 (-180 到 180) */
  longitude: number
}

// 转换结果接口
export interface TransformResult {
  /** 转换后的坐标 */
  coordinates: CoordPoint
  /** 源坐标系 */
  sourceSystem: string
  /** 目标坐标系 */
  targetSystem: string
  /** 转换时间戳 */
  timestamp: number
  /** 是否成功 */
  success: boolean
  /** 错误信息（如果有） */
  error?: string
}

/**
 * 验证坐标有效性
 * @param point 坐标点
 * @returns 是否有效
 */
function validateCoordinates(point: CoordPoint): boolean {
  return (
    typeof point.latitude === 'number' &&
    typeof point.longitude === 'number' &&
    !isNaN(point.latitude) &&
    !isNaN(point.longitude) &&
    point.latitude >= -90 &&
    point.latitude <= 90 &&
    point.longitude >= -180 &&
    point.longitude <= 180
  )
}

/**
 * WGS84坐标转换为GCJ02坐标 (使用coordtransform库)
 *
 * WGS84 是 GPS 设备使用的标准坐标系，而 GCJ02 是中国地图使用的坐标系。
 * 这个转换对于在中国地图上正确显示GPS坐标非常重要。
 *
 * @param wgsPoint WGS84坐标点
 * @returns GCJ02坐标点
 *
 * @example
 * const wgsPoint = { latitude: 39.9042, longitude: 116.4074 } // 北京天安门 GPS坐标
 * const gcjPoint = wgs84ToGcj02(wgsPoint)
 * console.log(gcjPoint) // { latitude: 39.905603, longitude: 116.413642 } 中国地图坐标
 */
export function wgs84ToGcj02(wgsPoint: CoordPoint): CoordPoint {
  try {
    if (!validateCoordinates(wgsPoint)) {
      console.warn('⚠️ 无效的WGS84坐标，返回原坐标:', wgsPoint)
      return wgsPoint
    }

    // 使用 coordtransform 进行转换
    // 注意：coordtransform 的输入格式是 [经度, 纬度]
    const result = wgs84togcj02(wgsPoint.longitude, wgsPoint.latitude)

    const gcjPoint: CoordPoint = {
      latitude: result[1], // 纬度
      longitude: result[0], // 经度
    }

    return gcjPoint
  } catch (error) {
    console.error('❌ WGS84→GCJ02转换失败:', error, '原坐标:', wgsPoint)
    return wgsPoint
  }
}

/**
 * GCJ02坐标转换为WGS84坐标 (使用coordtransform库)
 *
 * 将中国地图坐标系转换回GPS标准坐标系。
 * 通常用于将地图上的点转换为GPS设备可识别的坐标。
 *
 * @param gcjPoint GCJ02坐标点
 * @returns WGS84坐标点
 *
 * @example
 * const gcjPoint = { latitude: 39.905603, longitude: 116.413642 } // 中国地图坐标
 * const wgsPoint = gcj02ToWgs84(gcjPoint)
 * console.log(wgsPoint) // { latitude: 39.9042, longitude: 116.4074 } GPS坐标
 */
export function gcj02ToWgs84(gcjPoint: CoordPoint): CoordPoint {
  try {
    if (!validateCoordinates(gcjPoint)) {
      console.warn('⚠️ 无效的GCJ02坐标，返回原坐标:', gcjPoint)
      return gcjPoint
    }

    // 使用 coordtransform 进行转换
    const result = gcj02towgs84(gcjPoint.longitude, gcjPoint.latitude)

    const wgsPoint: CoordPoint = {
      latitude: result[1], // 纬度
      longitude: result[0], // 经度
    }

    // 详细的转换日志
    console.log('🌍 GCJ02→WGS84转换 (coordtransform):', {
      input: { lat: gcjPoint.latitude, lng: gcjPoint.longitude, system: 'GCJ02' },
      output: { lat: wgsPoint.latitude, lng: wgsPoint.longitude, system: 'WGS84' },
      offset: {
        lat: (wgsPoint.latitude - gcjPoint.latitude).toFixed(6) + '°',
        lng: (wgsPoint.longitude - gcjPoint.longitude).toFixed(6) + '°',
      },
    })

    return wgsPoint
  } catch (error) {
    console.error('❌ GCJ02→WGS84转换失败:', error, '原坐标:', gcjPoint)
    return gcjPoint
  }
}

/**
 * 批量转换坐标点数组的通用函数
 *
 * @param points 坐标点数组
 * @param transform 转换函数
 * @returns 转换后的坐标点数组
 *
 * @example
 * const wgsPoints = [
 *   { latitude: 39.9042, longitude: 116.4074 },
 *   { latitude: 31.2304, longitude: 121.4737 }
 * ]
 * const gcjPoints = transformPoints(wgsPoints, wgs84ToGcj02)
 */
export function transformPoints(
  points: CoordPoint[],
  transform: (point: CoordPoint) => CoordPoint,
): CoordPoint[] {
  if (!Array.isArray(points)) {
    console.warn('⚠️ transformPoints: 输入不是数组, 返回空数组')
    return []
  }

  console.log(`🔄 开始批量转换 ${points.length} 个坐标点...`)

  const result = points.map((point, index) => {
    try {
      return transform(point)
    } catch (error) {
      console.error(`❌ 第 ${index + 1} 个坐标点转换失败:`, point, error)
      return point // 失败时返回原坐标
    }
  })

  console.log(`✅ 批量转换完成, 成功转换 ${result.length} 个坐标点`)
  return result
}

/**
 * 批量WGS84转GCJ02坐标
 *
 * @param wgsPoints WGS84坐标点数组
 * @returns GCJ02坐标点数组
 */
export function batchWgs84ToGcj02(wgsPoints: CoordPoint[]): CoordPoint[] {
  return transformPoints(wgsPoints, wgs84ToGcj02)
}

/**
 * 批量GCJ02转WGS84坐标
 *
 * @param gcjPoints GCJ02坐标点数组
 * @returns WGS84坐标点数组
 */
export function batchGcj02ToWgs84(gcjPoints: CoordPoint[]): CoordPoint[] {
  return transformPoints(gcjPoints, gcj02ToWgs84)
}

/**
 * 计算两点之间的距离（米）- 使用 Haversine 公式
 *
 * @param point1 第一个点
 * @param point2 第二个点
 * @returns 距离（米）
 *
 * @example
 * const beijing = { latitude: 39.9042, longitude: 116.4074 }
 * const shanghai = { latitude: 31.2304, longitude: 121.4737 }
 * const distance = calculateDistance(beijing, shanghai)
 * console.log(distance) // 约 1068000 米 (1068公里)
 */
export function calculateDistance(point1: CoordPoint, point2: CoordPoint): number {
  if (!validateCoordinates(point1) || !validateCoordinates(point2)) {
    console.warn('⚠️ calculateDistance: 无效的坐标点')
    return 0
  }

  const R = 6371000 // 地球半径（米）
  const lat1Rad = (point1.latitude * Math.PI) / 180
  const lat2Rad = (point2.latitude * Math.PI) / 180
  const deltaLatRad = ((point2.latitude - point1.latitude) * Math.PI) / 180
  const deltaLonRad = ((point2.longitude - point1.longitude) * Math.PI) / 180

  const a =
    Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return R * c
}

/**
 * 检查坐标是否在中国境内 (大致范围)
 *
 * @param point 坐标点
 * @returns 是否在中国境内
 *
 * @example
 * const beijing = { latitude: 39.9042, longitude: 116.4074 }
 * const newYork = { latitude: 40.7128, longitude: -74.0060 }
 * console.log(isInChina(beijing)) // true
 * console.log(isInChina(newYork)) // false
 */
export function isInChina(point: CoordPoint): boolean {
  if (!validateCoordinates(point)) {
    return false
  }

  const { latitude: lat, longitude: lng } = point
  // 中国大致经纬度范围
  return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271
}

/**
 * 高级转换函数 - 带完整结果信息
 *
 * @param point 源坐标点
 * @param sourceSystem 源坐标系
 * @param targetSystem 目标坐标系
 * @returns 转换结果
 */
export function transformCoordWithResult(
  point: CoordPoint,
  sourceSystem: 'WGS84' | 'GCJ02',
  targetSystem: 'WGS84' | 'GCJ02',
): TransformResult {
  const timestamp = Date.now()

  try {
    if (!validateCoordinates(point)) {
      return {
        coordinates: point,
        sourceSystem,
        targetSystem,
        timestamp,
        success: false,
        error: '无效的坐标点',
      }
    }

    if (sourceSystem === targetSystem) {
      return {
        coordinates: point,
        sourceSystem,
        targetSystem,
        timestamp,
        success: true,
      }
    }

    let result: CoordPoint

    // 根据转换路径选择对应函数
    if (sourceSystem === 'WGS84' && targetSystem === 'GCJ02') {
      result = wgs84ToGcj02(point)
    } else if (sourceSystem === 'GCJ02' && targetSystem === 'WGS84') {
      result = gcj02ToWgs84(point)
    } else {
      return {
        coordinates: point,
        sourceSystem,
        targetSystem,
        timestamp,
        success: false,
        error: `不支持的转换路径: ${sourceSystem} → ${targetSystem}`,
      }
    }

    return {
      coordinates: result,
      sourceSystem,
      targetSystem,
      timestamp,
      success: true,
    }
  } catch (error) {
    return {
      coordinates: point,
      sourceSystem,
      targetSystem,
      timestamp,
      success: false,
      error: error instanceof Error ? error.message : '转换失败',
    }
  }
}

/**
 * 坐标转换测试函数 - 基于 coordtransform 库
 * 测试WGS84→GCJ02→WGS84的完整转换流程，评估精度损失
 *
 * @param testPoint 测试坐标点 (WGS84)，默认为用户提供的坐标
 * @returns 完整的转换结果和精度报告
 *
 * @example
 * // 使用默认坐标测试
 * const result = testCoordTransform()
 *
 * // 使用自定义坐标测试
 * const customResult = testCoordTransform({ latitude: 39.9042, longitude: 116.4074 })
 * console.log(customResult.precisionLoss) // 精度损失（米）
 */
export function testCoordTransform(
  testPoint: CoordPoint = { latitude: 42.916, longitude: 129.504 },
) {
  console.log('🧪 开始坐标转换测试 (使用coordtransform库)...')
  console.log('📍 测试坐标 (WGS84):', testPoint)

  // WGS84 → GCJ02
  const gcjResult = wgs84ToGcj02(testPoint)
  console.log('📍 转换结果 (GCJ02):', gcjResult)

  // GCJ02 → WGS84 (验证逆转换)
  const wgsResult = gcj02ToWgs84(gcjResult)
  console.log('📍 逆转换结果 (WGS84):', wgsResult)

  // 计算精度损失
  const distance = calculateDistance(testPoint, wgsResult)
  console.log('📏 往返转换精度损失:', distance.toFixed(3), '米')

  // 检查是否在中国
  console.log('🇨🇳 是否在中国境内:', isInChina(testPoint))

  console.log('✅ 坐标转换测试完成')

  return {
    original: testPoint,
    gcj02: gcjResult,
    backToWgs84: wgsResult,
    precisionLoss: distance,
    isInChina: isInChina(testPoint),
  }
}
