/**
 * 地图搜索工具
 * 支持多平台地图搜索功能
 */

// 搜索结果接口
export interface SearchResult {
  name: string
  address: string
  latitude: number
  longitude: number
  distance?: number
  type?: string
  province?: string
  city?: string
  district?: string
}

// 搜索配置
interface SearchConfig {
  key?: string
  region?: string
  radius?: number
  pageSize?: number
}

/**
 * 使用uniapp内置地图搜索功能
 * 主要适用于App端
 */
const searchWithUniMap = async (
  keyword: string,
  config: SearchConfig = {},
): Promise<SearchResult[]> => {
  return new Promise((resolve, reject) => {
    // #ifdef APP-PLUS
    // 使用uniapp的mapSearch模块
    const mapSearch = uni.requireNativePlugin('mapSearch')
    if (mapSearch) {
      mapSearch.poiKeywordsSearch(
        {
          key: keyword,
          city: config.region || '长春市',
          types: '',
          offset: config.pageSize || 10,
          index: 1,
        },
        (result: any) => {
          if (result.type === 'success' && result.poiList) {
            const results: SearchResult[] = result.poiList.map((poi: any) => ({
              name: poi.name,
              address: poi.address,
              latitude: poi.location.latitude,
              longitude: poi.location.longitude,
              distance: poi.distance,
              type: poi.type,
            }))
            resolve(results)
          } else {
            reject(new Error(result.message || '搜索失败'))
          }
        },
      )
    } else {
      reject(new Error('地图搜索模块不可用'))
    }
    // #endif

    // #ifndef APP-PLUS
    // 非App端返回空结果，使用其他搜索方式
    resolve([])
    // #endif
  })
}

/**
 * 使用腾讯地图Web API搜索
 * 适用于H5和小程序
 */
const searchWithTencentAPI = async (
  keyword: string,
  config: SearchConfig = {},
): Promise<SearchResult[]> => {
  // 这里需要配置腾讯地图的key
  const key = config.key || 'YOUR_TENCENT_MAP_KEY' // 需要替换为实际的key

  if (key === 'YOUR_TENCENT_MAP_KEY') {
    console.warn('腾讯地图API key未配置，使用模拟数据')
    return getSimulatedResults(keyword)
  }

  try {
    const response = await uni.request({
      url: 'https://apis.map.qq.com/ws/place/v1/search',
      method: 'GET',
      data: {
        keyword,
        boundary: config.region ? `region(${config.region},0)` : 'region(长春市,0)',
        page_size: config.pageSize || 10,
        page_index: 1,
        key,
      },
    })

    if (response.statusCode === 200 && response.data.status === 0) {
      const results: SearchResult[] = response.data.data.map((item: any) => ({
        name: item.title,
        address: item.address,
        latitude: item.location.lat,
        longitude: item.location.lng,
        type: item.category,
        province: item.ad_info?.province,
        city: item.ad_info?.city,
        district: item.ad_info?.district,
      }))
      return results
    } else {
      throw new Error(response.data.message || '搜索失败')
    }
  } catch (error) {
    console.error('腾讯地图API搜索失败:', error)
    throw error
  }
}

/**
 * 获取模拟搜索结果
 * 用于演示和测试
 */
const getSimulatedResults = (keyword: string): SearchResult[] => {
  const simulatedData: SearchResult[] = [
    {
      name: '长春高新海容广场',
      address: '吉林省长春市朝阳区前进大街2699号',
      latitude: 43.823755,
      longitude: 125.277062,
      type: '商业楼宇',
      province: '吉林省',
      city: '长春市',
      district: '朝阳区',
      distance: 500,
    },
    {
      name: '长春站',
      address: '吉林省长春市宽城区长白路1号',
      latitude: 43.8235,
      longitude: 125.3235,
      type: '交通设施',
      province: '吉林省',
      city: '长春市',
      district: '宽城区',
      distance: 1200,
    },
    {
      name: '长春市政府',
      address: '吉林省长春市朝阳区人民大街10111号',
      latitude: 43.8868,
      longitude: 125.3245,
      type: '政府机构',
      province: '吉林省',
      city: '长春市',
      district: '朝阳区',
      distance: 800,
    },
    {
      name: '吉林大学',
      address: '吉林省长春市朝阳区前进大街2699号',
      latitude: 43.8642,
      longitude: 125.3237,
      type: '教育机构',
      province: '吉林省',
      city: '长春市',
      district: '朝阳区',
      distance: 600,
    },
    {
      name: '长春世界雕塑园',
      address: '吉林省长春市南关区人民大街9518号',
      latitude: 43.8156,
      longitude: 125.3089,
      type: '旅游景点',
      province: '吉林省',
      city: '长春市',
      district: '南关区',
      distance: 1500,
    },
    {
      name: '南关区政府',
      address: '吉林省长春市南关区大马路1366号',
      latitude: 43.8456,
      longitude: 125.3189,
      type: '政府机构',
      province: '吉林省',
      city: '长春市',
      district: '南关区',
      distance: 900,
    },
    {
      name: '朝阳区政府',
      address: '吉林省长春市朝阳区西安大路5088号',
      latitude: 43.8756,
      longitude: 125.2989,
      type: '政府机构',
      province: '吉林省',
      city: '长春市',
      district: '朝阳区',
      distance: 700,
    },
    {
      name: '长春公园',
      address: '吉林省长春市朝阳区人民大街9999号',
      latitude: 43.8356,
      longitude: 125.3089,
      type: '公园',
      province: '吉林省',
      city: '长春市',
      district: '朝阳区',
      distance: 400,
    },
    {
      name: '长春医院',
      address: '吉林省长春市朝阳区建设街1445号',
      latitude: 43.8556,
      longitude: 125.2889,
      type: '医疗机构',
      province: '吉林省',
      city: '长春市',
      district: '朝阳区',
      distance: 1100,
    },
    {
      name: '长春商场',
      address: '吉林省长春市朝阳区重庆路1255号',
      latitude: 43.8456,
      longitude: 125.3189,
      type: '购物中心',
      province: '吉林省',
      city: '长春市',
      district: '朝阳区',
      distance: 650,
    },
  ]

  // 根据关键词过滤结果，支持模糊搜索
  const filteredResults = simulatedData.filter((item) => {
    const searchText = keyword.toLowerCase()
    return (
      item.name.toLowerCase().includes(searchText) ||
      item.address.toLowerCase().includes(searchText) ||
      item.type.toLowerCase().includes(searchText) ||
      item.district.toLowerCase().includes(searchText)
    )
  })

  // 按距离排序
  return filteredResults.sort((a, b) => (a.distance || 0) - (b.distance || 0))
}

/**
 * 主搜索函数
 * 根据平台自动选择合适的搜索方式
 */
export const searchLocation = async (
  keyword: string,
  config: SearchConfig = {},
): Promise<SearchResult[]> => {
  if (!keyword.trim()) {
    return []
  }

  try {
    // #ifdef APP-PLUS
    // App端优先使用原生地图搜索
    try {
      const results = await searchWithUniMap(keyword, config)
      if (results.length > 0) {
        return results
      }
    } catch (error) {
      console.warn('原生地图搜索失败，尝试使用Web API:', error)
    }
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序使用腾讯地图API
    return await searchWithTencentAPI(keyword, config)
    // #endif

    // #ifdef H5
    // H5端使用腾讯地图API
    return await searchWithTencentAPI(keyword, config)
    // #endif

    // 其他平台或API不可用时使用模拟数据
    return getSimulatedResults(keyword)
  } catch (error) {
    console.error('地图搜索失败:', error)
    // 搜索失败时返回模拟数据作为备选
    return getSimulatedResults(keyword)
  }
}

/**
 * 根据坐标反向地理编码获取地址信息
 */
export const reverseGeocode = async (
  latitude: number,
  longitude: number,
  config: SearchConfig = {},
): Promise<string> => {
  const key = config.key || 'YOUR_TENCENT_MAP_KEY'

  if (key === 'YOUR_TENCENT_MAP_KEY') {
    return `纬度: ${latitude.toFixed(6)}, 经度: ${longitude.toFixed(6)}`
  }

  try {
    const response = await uni.request({
      url: 'https://apis.map.qq.com/ws/geocoder/v1/',
      method: 'GET',
      data: {
        location: `${latitude},${longitude}`,
        key,
      },
    })

    if (response.statusCode === 200 && response.data.status === 0) {
      return response.data.result.address
    } else {
      throw new Error(response.data.message || '反向地理编码失败')
    }
  } catch (error) {
    console.error('反向地理编码失败:', error)
    return `纬度: ${latitude.toFixed(6)}, 经度: ${longitude.toFixed(6)}`
  }
}
