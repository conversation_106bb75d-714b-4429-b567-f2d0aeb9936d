// src/utils/wktConverter.ts
/**
 * WKT格式与地图数据格式转换工具
 * 支持 POINT, LINESTRING, POLYGON, MULTIPOLYGON 等WKT几何类型
 * 与uniapp地图组件数据格式互相转换
 *
 * 坐标系处理：
 * - 解析WKT时：假设输入是WGS84，转换为当前平台坐标系用于显示
 * - 生成WKT时：假设输入是当前平台坐标系，转换为WGS84用于存储
 */

import { gcj02ToWgs84, wgs84ToGcj02 } from './coordTransformNew'

// 坐标点接口
export interface MapPoint {
  latitude: number
  longitude: number
}

// 地图标记点接口（对应uniapp map组件的markers）
export interface MapMarker {
  id: string
  latitude: number
  longitude: number
  iconPath: string
  width: number
  height: number
}

// 地图线段接口（对应uniapp map组件的polyline）
export interface MapPolyline {
  points: MapPoint[]
  width?: number
  color?: string
}

// 地图多边形接口（对应uniapp map组件的polygons）
export interface MapPolygon {
  points: MapPoint[]
  fillColor?: string
  strokeColor?: string
  strokeWidth?: number
}

// 通用几何对象接口
export interface MapGeometry {
  type: 'point' | 'polyline' | 'polygon'
  geometry: MapMarker | MapPolyline | MapPolygon
}

// 默认样式配置
const DEFAULT_STYLES = {
  point: {
    iconPath: '/static/map/icon/marker.png',
    width: 32,
    height: 32,
  },
  polyline: {
    width: 3,
    color: '#FF0000',
  },
  polygon: {
    fillColor: '#67c23a4d', // 已完成状态颜色的透明版本
    strokeColor: '#67c23a', // 已完成状态颜色
    strokeWidth: 2,
  },
}

/**
 * 将WGS84坐标转换为当前平台坐标系（用于显示）
 * @param point WGS84坐标点
 * @returns 平台坐标系坐标点
 */
function convertWgs84ToPlatform(point: MapPoint): MapPoint {
  // #ifdef MP-WEIXIN
  // 微信小程序使用GCJ02坐标系
  const gcjPoint = wgs84ToGcj02(point)
  return gcjPoint
  // #endif
}

/**
 * 将当前平台坐标系转换为WGS84（用于存储）
 * @param point 平台坐标系坐标点
 * @returns WGS84坐标点
 */
function convertPlatformToWgs84(point: MapPoint): MapPoint {
  // #ifdef MP-WEIXIN
  // 微信小程序使用GCJ02坐标系，需要转换为WGS84
  const wgsPoint = gcj02ToWgs84(point)
  console.log('🔄 WKT生成 GCJ02→WGS84:', {
    input: point,
    output: wgsPoint,
    platform: '微信小程序',
  })
  return wgsPoint
  // #endif
}

/**
 * 解析坐标字符串（假设输入是WGS84）
 * @param coordStr 坐标字符串，如："129.504 42.916"
 * @returns MapPoint或null (转换为当前平台坐标系)
 */
function parseCoordinate(coordStr: string): MapPoint | null {
  if (!coordStr || typeof coordStr !== 'string') {
    return null
  }

  const parts = coordStr.trim().split(/\s+/)
  if (parts.length < 2) {
    return null
  }

  const lon = parseFloat(parts[0])
  const lat = parseFloat(parts[1])

  if (isNaN(lon) || isNaN(lat) || lon < -180 || lon > 180 || lat < -90 || lat > 90) {
    return null
  }

  // 假设WKT中的坐标是WGS84，转换为当前平台坐标系用于显示
  const wgs84Point = { latitude: lat, longitude: lon }
  const platformPoint = convertWgs84ToPlatform(wgs84Point)

  return platformPoint
}

/**
 * 解析坐标序列（假设输入是WGS84）
 * @param coordsStr 坐标序列字符串，如："129.504 42.916, 129.505 42.917"
 * @returns MapPoint数组 (转换为当前平台坐标系)
 */
function parseCoordinateSequence(coordsStr: string): MapPoint[] {
  if (!coordsStr || typeof coordsStr !== 'string') {
    return []
  }

  const points: MapPoint[] = []
  const coordPairs = coordsStr.split(',')

  for (const coordPair of coordPairs) {
    const point = parseCoordinate(coordPair)
    if (point) {
      points.push(point)
    }
  }

  return points
}

/**
 * 解析POINT类型WKT
 * @param wkt WKT字符串
 * @returns MapGeometry数组
 */
function parsePoint(wkt: string): MapGeometry[] {
  const results: MapGeometry[] = []

  // 简单的字符串查找和提取
  const startIndex = wkt.indexOf('(')
  const endIndex = wkt.lastIndexOf(')')

  if (startIndex === -1 || endIndex === -1 || startIndex >= endIndex) {
    return results
  }

  const coordsStr = wkt.substring(startIndex + 1, endIndex)
  const point = parseCoordinate(coordsStr)

  if (point) {
    results.push({
      type: 'point',
      geometry: {
        id: `point_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
        latitude: point.latitude,
        longitude: point.longitude,
        ...DEFAULT_STYLES.point,
      } as MapMarker,
    })
  }

  return results
}

/**
 * 解析LINESTRING类型WKT
 * @param wkt WKT字符串
 * @returns MapGeometry数组
 */
function parseLineString(wkt: string): MapGeometry[] {
  const results: MapGeometry[] = []

  const startIndex = wkt.indexOf('(')
  const endIndex = wkt.lastIndexOf(')')

  if (startIndex === -1 || endIndex === -1 || startIndex >= endIndex) {
    return results
  }

  const coordsStr = wkt.substring(startIndex + 1, endIndex)
  const points = parseCoordinateSequence(coordsStr)

  if (points.length >= 2) {
    results.push({
      type: 'polyline',
      geometry: {
        points,
        ...DEFAULT_STYLES.polyline,
      } as MapPolyline,
    })
  }

  return results
}

/**
 * 解析POLYGON类型WKT
 * @param wkt WKT字符串
 * @returns MapGeometry数组
 */
function parsePolygon(wkt: string): MapGeometry[] {
  const results: MapGeometry[] = []

  // 查找第一层括号
  const firstOpenIndex = wkt.indexOf('(')
  if (firstOpenIndex === -1) {
    return results
  }

  // 查找第二层括号（多边形环）
  const secondOpenIndex = wkt.indexOf('(', firstOpenIndex + 1)
  const secondCloseIndex = wkt.indexOf(')', secondOpenIndex + 1)

  if (secondOpenIndex === -1 || secondCloseIndex === -1) {
    return results
  }

  const coordsStr = wkt.substring(secondOpenIndex + 1, secondCloseIndex)
  const points = parseCoordinateSequence(coordsStr)

  if (points.length >= 3) {
    results.push({
      type: 'polygon',
      geometry: {
        points,
        ...DEFAULT_STYLES.polygon,
      } as MapPolygon,
    })
  }

  return results
}

/**
 * 解析MULTIPOLYGON类型WKT
 * @param wkt WKT字符串
 * @returns MapGeometry数组
 */
function parseMultiPolygon(wkt: string): MapGeometry[] {
  const results: MapGeometry[] = []

  // 查找所有的多边形环：((coords))
  let searchIndex = 0

  while (true) {
    // 查找下一个 ((
    const doubleOpenIndex = wkt.indexOf('((', searchIndex)
    if (doubleOpenIndex === -1) {
      break
    }

    // 查找对应的 ))
    const doubleCloseIndex = wkt.indexOf('))', doubleOpenIndex + 2)
    if (doubleCloseIndex === -1) {
      break
    }

    // 提取坐标字符串
    const coordsStr = wkt.substring(doubleOpenIndex + 2, doubleCloseIndex)
    const points = parseCoordinateSequence(coordsStr)

    if (points.length >= 3) {
      results.push({
        type: 'polygon',
        geometry: {
          points,
          ...DEFAULT_STYLES.polygon,
        } as MapPolygon,
      })
    }

    searchIndex = doubleCloseIndex + 2
  }

  return results
}

/**
 * 解析WKT格式字符串并转换为地图数据格式
 * @param wkt WKT格式字符串
 * @returns 地图几何对象数组
 */
export function wktToMapData(wkt: string): MapGeometry[] {
  if (!wkt || typeof wkt !== 'string') {
    return []
  }

  const results: MapGeometry[] = []

  try {
    // 清理输入字符串
    const cleanWkt = wkt.trim().replace(/\s+/g, ' ')

    // 按行分割，处理多个WKT对象
    const lines = cleanWkt.split('\n').filter((line) => line.trim().length > 0)

    for (const line of lines) {
      const upperLine = line.trim().toUpperCase()

      if (upperLine.startsWith('POINT')) {
        results.push(...parsePoint(line))
      } else if (upperLine.startsWith('LINESTRING')) {
        results.push(...parseLineString(line))
      } else if (upperLine.startsWith('MULTIPOLYGON')) {
        results.push(...parseMultiPolygon(line))
      } else if (upperLine.startsWith('POLYGON')) {
        results.push(...parsePolygon(line))
      }
    }
  } catch (error) {
    console.error('WKT解析错误:', error)
  }

  return results
}

/**
 * 将地图数据格式转换为WKT格式字符串
 * @param geometries 地图几何对象数组
 * @returns WKT格式字符串 (坐标为WGS84)
 */
export function mapDataToWkt(geometries: MapGeometry[]): string {
  if (!Array.isArray(geometries)) {
    return ''
  }

  const wktParts: string[] = []

  for (const geo of geometries) {
    try {
      if (!geo || !geo.type || !geo.geometry) {
        continue
      }

      switch (geo.type) {
        case 'point': {
          const marker = geo.geometry as MapMarker
          if (
            marker &&
            typeof marker.latitude === 'number' &&
            typeof marker.longitude === 'number'
          ) {
            // 将平台坐标系转换为WGS84用于存储
            const wgs84Point = convertPlatformToWgs84({
              latitude: marker.latitude,
              longitude: marker.longitude,
            })
            wktParts.push(`POINT (${wgs84Point.longitude} ${wgs84Point.latitude})`)
          }
          break
        }

        case 'polyline': {
          const polyline = geo.geometry as MapPolyline
          if (polyline && Array.isArray(polyline.points) && polyline.points.length >= 2) {
            // 将所有点的坐标转换为WGS84
            const wgs84Coords = polyline.points
              .map((p) => {
                const wgs84Point = convertPlatformToWgs84(p)
                return `${wgs84Point.longitude} ${wgs84Point.latitude}`
              })
              .join(', ')
            wktParts.push(`LINESTRING (${wgs84Coords})`)
          }
          break
        }

        case 'polygon': {
          const polygon = geo.geometry as MapPolygon
          if (polygon && Array.isArray(polygon.points) && polygon.points.length >= 3) {
            // 将所有点的坐标转换为WGS84
            const wgs84Coords = polygon.points
              .map((p) => {
                const wgs84Point = convertPlatformToWgs84(p)
                return `${wgs84Point.longitude} ${wgs84Point.latitude}`
              })
              .join(', ')
            wktParts.push(`POLYGON ((${wgs84Coords}))`)
          }
          break
        }
      }
    } catch (error) {
      console.warn('几何对象转换失败:', geo, error)
    }
  }

  console.log('📤 WKT生成完成:', {
    输入几何对象数量: geometries.length,
    输出WKT行数: wktParts.length,
    坐标系: 'WGS84',
  })

  return wktParts.join('\n')
}

/**
 * 便捷函数：将WKT直接转换为uniapp地图组件可用的格式
 * @param wkt WKT格式字符串
 * @returns 包含markers、polylines、polygons的对象
 */
export function wktToMapComponents(wkt: string) {
  const geometries = wktToMapData(wkt)

  const markers: MapMarker[] = []
  const polylines: MapPolyline[] = []
  const polygons: MapPolygon[] = []

  for (const geo of geometries) {
    switch (geo.type) {
      case 'point':
        markers.push(geo.geometry as MapMarker)
        break
      case 'polyline':
        polylines.push(geo.geometry as MapPolyline)
        break
      case 'polygon':
        polygons.push(geo.geometry as MapPolygon)
        break
    }
  }

  return { markers, polylines, polygons }
}

/**
 * 便捷函数：从uniapp地图组件格式转换为WKT
 * @param mapData 地图组件数据
 * @returns WKT格式字符串
 */
export function mapComponentsToWkt(mapData: {
  markers?: MapMarker[]
  polylines?: MapPolyline[]
  polygons?: MapPolygon[]
}): string {
  if (!mapData || typeof mapData !== 'object') {
    return ''
  }

  const geometries: MapGeometry[] = []

  // 转换markers
  if (Array.isArray(mapData.markers)) {
    for (const marker of mapData.markers) {
      geometries.push({
        type: 'point',
        geometry: marker,
      })
    }
  }

  // 转换polylines
  if (Array.isArray(mapData.polylines)) {
    for (const polyline of mapData.polylines) {
      geometries.push({
        type: 'polyline',
        geometry: polyline,
      })
    }
  }

  // 转换polygons
  if (Array.isArray(mapData.polygons)) {
    for (const polygon of mapData.polygons) {
      geometries.push({
        type: 'polygon',
        geometry: polygon,
      })
    }
  }

  return mapDataToWkt(geometries)
}
